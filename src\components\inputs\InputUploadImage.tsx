import React, { useState } from 'react'
import type { UploadProps } from 'antd'
import { message, Row, Upload } from 'antd'
import Image from 'next/image'
import type { RcFile } from 'antd/es/upload'

import { IconUpload } from '@/icons'
import { PLACE_HOLDER } from '@/utils/string'

const { Dragger } = Upload

interface InputUploadImageProps {
  srcImage?: string
}

export const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })

const InputUploadImage = (props: InputUploadImageProps) => {
  const [imageBase64, setImageBase64] = useState<string>(props?.srcImage || '')
  const config: UploadProps = {
    name: 'file',
    multiple: false,
    async onChange(info) {
      const { status } = info.file
      if (status === 'done') {
        setImageBase64(await getBase64(info.file.originFileObj as RcFile))
      } else if (status === 'error') {
        message.error(`${info.file.name} file upload failed.`)
      }
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files)
    },
  }

  return (
    <Dragger showUploadList={false} maxCount={1} {...config}>
      <Row className="h-[160px] gap-y-2" align={'middle'} justify={'center'}>
        {imageBase64 ? (
          <Image
            alt="banner nft"
            src={imageBase64}
            width={564}
            height={160}
            className="object-contain w-[564px] h-[160px]"
          />
        ) : (
          <div className="flex flex-col items-center justify-center">
            <IconUpload />
            <p className="font-normal text-base text-tailwindNeutral3">
              {PLACE_HOLDER.uploadImage}
            </p>
          </div>
        )}
      </Row>
    </Dragger>
  )
}

export default InputUploadImage
