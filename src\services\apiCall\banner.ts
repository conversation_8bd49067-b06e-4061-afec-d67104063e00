import { toast } from 'react-toastify'

import {
  ACTION_TYPE,
  API_URLS,
  DEFAULT_CONFIG_PARAMS,
  LENGTH_BIT_IMAGE,
  MAX_SIZE_FILE,
  ROLE,
} from '@/constants'
import {
  TBanner,
  TBannerData,
  TDefaultBannerSearch,
  TUpdateIndexProps,
} from '@/interfaces/banner'
import { MESSAGE_FORM } from '@/utils/string'
import { del, get, patch, post } from './baseApi'

export const uploadBannerFile = (formData: FormData, configParams?: object) => {
  return post(
    `${API_URLS.adminBanners}/upload-image-editor`,
    formData,
    {
      ...configParams,
    },
    ROLE.systemAdmin
  )
}

export const createBanner = async (
  body: TBanner,
  role: ROLE.systemAdmin | ROLE.operator
) => {
  try {
    return await post(API_URLS.adminBanners, body, DEFAULT_CONFIG_PARAMS, role)
  } catch (error) {
    console.error('Failed to create banner:', error)
    throw error
  }
}

export const updateBanner = async ({
  body,
  bannerId,
  role,
}: {
  body: TBanner
  bannerId: string
  role: ROLE.systemAdmin | ROLE.operator
}) => {
  return await patch(`${API_URLS.adminBanners}/${bannerId}`, body, role)
}

export const uploadImageEditor = async (blobInfo) => {
  const formData = new FormData()
  if (blobInfo.blob().size / LENGTH_BIT_IMAGE > MAX_SIZE_FILE) {
    toast.error(MESSAGE_FORM.upload10MB)
    return MESSAGE_FORM.upload10MB
  }
  formData.append('file', blobInfo.blob(), blobInfo.filename())
  const imageUrl = await uploadBannerFile(formData)
  return imageUrl
}

export const uploadBannerImage = async (image, role) => {
  try {
    const base64Response = await fetch(image)
    const newBlob = await base64Response.blob()
    const formData = new FormData()
    formData.append('file', newBlob)
    const imageUrl = await post(
      `${API_URLS.adminBanners}/upload-image`,
      formData,
      DEFAULT_CONFIG_PARAMS,
      role
    )
    return imageUrl
  } catch (error) {
    toast.error(MESSAGE_FORM.uploadImageHaveError)
    return false
  }
}
export const getBannerDetail = async (
  bannerId: string | undefined
): Promise<TBanner> => {
  return await get(`${API_URLS.adminBanners}/${bannerId}`)
}

export const deleteBanner = async (
  bannerIds: string[],
  type: 'delete' | 'deleteAll',
  role: ROLE.systemAdmin | ROLE.operator
) => {
  await del(`${API_URLS.adminBanners}`, { ids: bannerIds, type }, role)
}

export const updateBannerIndex = async (items: TUpdateIndexProps) => {
  await post(
    `${API_URLS.adminBanners}/drag-and-drop`,
    { items },
    DEFAULT_CONFIG_PARAMS,
    ROLE.systemAdmin
  )
}

export const getBanners = async (
  params: TDefaultBannerSearch
): Promise<TBannerData> => {
  return await get(`${API_URLS.banners}`, params)
}

export const approvedRejectBanner = async ({
  approvalStatus,
  bannerId,
  role,
}: {
  approvalStatus: ACTION_TYPE.APPROVED | ACTION_TYPE.REJECTED
  bannerId: string
  role: ROLE.systemAdmin | ROLE.approver
}) => {
  return await patch(
    `${API_URLS.adminBanners}/${bannerId}/approval`,
    {
      approvalStatus,
    },
    role
  )
}
