import { IMAGE } from '@/utils/string'
import Image from 'next/image'

type ButtonProps = {
  className?: string
}

export const ShareToFacebookButton = ({ className }: ButtonProps) => {
  const share = () => {
    const url = encodeURIComponent(window.location.href)
    const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`
    window.open(facebookShareUrl, '_blank')
  }

  return (
    <button className={`${className || ''}`} onClick={share}>
      <Image src={IMAGE.logoFb} alt="Share to Facebook" />
    </button>
  )
}

export const ShareToXButton = ({ className }: ButtonProps) => {
  const share = () => {
    const url = encodeURIComponent(window.location.href)
    const twitterShareUrl = `https://twitter.com/intent/tweet?url=${url}`
    window.open(twitterShareUrl, '_blank')
  }

  return (
    <button className={`${className || ''}`} onClick={share}>
      <Image src={IMAGE.logoX} alt="Share to X" />
    </button>
  )
}
