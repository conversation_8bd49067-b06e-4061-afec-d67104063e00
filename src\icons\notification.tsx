import { IIcon } from '@/interfaces'

interface IconProps {
  width?: number
  height?: number
  color?: string
}

export const IconCompleted = (props: IconProps) => (
  <svg
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.5303 9.53033C17.8232 9.23744 17.8232 8.76256 17.5303 8.46967C17.2374 8.17678 16.7626 8.17678 16.4697 8.46967L17.5303 9.53033ZM9.99998 16L9.46965 16.5304C9.76255 16.8232 10.2374 16.8232 10.5303 16.5303L9.99998 16ZM7.53027 12.4697C7.23737 12.1768 6.7625 12.1768 6.46961 12.4697C6.17671 12.7626 6.17672 13.2374 6.46961 13.5303L7.53027 12.4697ZM16.4697 8.46967L9.46965 15.4697L10.5303 16.5303L17.5303 9.53033L16.4697 8.46967ZM6.46961 13.5303L9.46965 16.5304L10.5303 15.4697L7.53027 12.4697L6.46961 13.5303ZM20.25 12C20.25 16.5563 16.5563 20.25 12 20.25V21.75C17.3848 21.75 21.75 17.3848 21.75 12H20.25ZM12 20.25C7.44365 20.25 3.75 16.5563 3.75 12H2.25C2.25 17.3848 6.61522 21.75 12 21.75V20.25ZM3.75 12C3.75 7.44365 7.44365 3.75 12 3.75V2.25C6.61522 2.25 2.25 6.61522 2.25 12H3.75ZM12 3.75C16.5563 3.75 20.25 7.44365 20.25 12H21.75C21.75 6.61522 17.3848 2.25 12 2.25V3.75Z"
      fill={props.color || '#52DE20'}
    />
  </svg>
)

export const IconFailed = (props: IconProps) => (
  <svg
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.9999 9L8.99994 15M9.00006 9L15.0001 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
      stroke={props.color || '#FF3030'}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
)

export const IconWarning = (props: IIcon) => {
  const { width = '116', height = '104', color = '#CD1010' } = props
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 116 104"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_249_1694)">
        <path
          d="M114.672 89.0245L66.5726 4.98985C63.8389 0.217443 57.7873 -1.42002 53.0558 1.3373C51.5518 2.213 50.3027 3.47283 49.4345 4.98985L1.32849 89.0245C-1.40522 93.8002 0.214994 99.904 4.94975 102.661C6.45377 103.537 8.16113 103.999 9.90076 103.999H106.1C111.567 103.999 115.999 99.5297 115.999 94.0183C115.999 92.2636 115.54 90.5415 114.672 89.0245ZM58.0036 90.5513C54.5663 90.5513 51.7809 87.7419 51.7809 84.2749C51.7809 80.8079 54.5663 77.9985 58.0036 77.9985C61.4409 77.9985 64.2262 80.8079 64.2262 84.2749C64.2262 87.7419 61.4409 90.5513 58.0036 90.5513ZM64.2262 63.5185C64.2262 66.6339 61.7217 69.1601 58.633 69.1601H57.3774C54.2887 69.1601 51.7842 66.6339 51.7842 63.5185V36.5509C51.7842 33.4355 54.2887 30.9093 57.3774 30.9093H58.633C61.7217 30.9093 64.2262 33.4355 64.2262 36.5509V63.5185Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_249_1694">
          <rect width={width} height={height} fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export const CheckNotBoxIcon = ({
  className = '',
  width = '20px',
  height = '20px',
  color = '',
}) => {
  return (
    <svg
      color={color}
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      className={className}
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z" />
    </svg>
  )
}
