import React, { useEffect, useState } from 'react'
import { Empty, Space, Checkbox, Input, Select, Spin } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import router, { useRouter } from 'next/router'
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'react-toastify'

import {
  getNftForAdmin,
  deleteNft,
  setNftPriority,
} from '@/services/apiCall/nft'
import { getListCategories } from '@/services/apiCall/category'
import { getListCollections } from '@/services/apiCall/collection'
import { MESSAGE_FORM } from '@/utils/string'
import { getCookie, getPermission } from '@/utils'
import { Admin, NftDetail, Category, Collection } from '@/interfaces'
import { STORAGEKEY } from '@/services/cookies'
import UiModal from '@/components/ui/Modal'
import {
  APPROVAL_STATUS,
  DEFAULT_PAGE_SIZE,
  ROLE,
  ROUT<PERSON>,
  renderApprovalStatus,
} from '@/constants'
import UiButton from '@/components/ui/Button'
import { IconPen, IconSearch } from '@/icons'
import { TextAdminTitle } from '@/components/texts/TextAdminTitle'
import ApprovalStatusBadge from '@/components/badge/ApprovalStatusBadge'
import { NFT_FILTER_DATA } from '@/utils/type'
import IconDelete from '@/components/icons/IconDelete'
import AdminTable from '@/components/table/AdminTable'
import { PreviewNft } from '@/components/nfts'

export const DataTable = ({
  setNftFilterData,
}: {
  setNftFilterData: (nftFilterData: NFT_FILTER_DATA | null) => void
}) => {
  const { push } = useRouter()
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const admin = JSON.parse(getCookie(STORAGEKEY.ADMIN) || '{}') as Admin

  // Search/filter/pagination state
  const [searchWord, setSearchWord] = useState('')
  const [category, setCategory] = useState('')
  const [approvalStatus, setApprovalStatus] = useState('')
  const [isPriority, setIsPriority] = useState('')
  const [collection, setCollection] = useState('')
  const [pageIndex, setPageIndex] = useState(1)
  const pageSize = DEFAULT_PAGE_SIZE

  // Debounce searchWord
  const [debouncedSearchWord, setDebouncedSearchWord] = useState(searchWord)
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchWord(searchWord)
    }, 400)
    return () => clearTimeout(handler)
  }, [searchWord])

  // Category query
  const { data: categoryData, isLoading: categoryLoading } = useQuery({
    queryKey: ['categories'],
    queryFn: getListCategories,
  })

  // Collection query (with infinite scroll and search)
  const [collectionPage, setCollectionPage] = useState(1)
  const [collectionSearch, setCollectionSearch] = useState('')
  const { data: collectionData, isLoading: collectionLoading } = useQuery({
    queryKey: ['collections', collectionPage, collectionSearch],
    queryFn: () =>
      getListCollections({
        pageIndex: collectionPage,
        pageSize: pageSize,
        approvalStatus: APPROVAL_STATUS.approved,
        ...(collectionSearch ? { searchWord: collectionSearch } : {}),
      }),
  })

  // NFT list query
  const {
    data: nftData,
    isLoading: nftLoading,
    refetch: refetchNfts,
  } = useQuery({
    queryKey: [
      'nfts',
      debouncedSearchWord,
      category,
      approvalStatus,
      isPriority,
      collection,
      pageIndex,
      pageSize,
    ],
    queryFn: () => {
      // Convert isPriority to boolean or undefined
      let isPriorityValue: boolean | undefined = undefined
      if (isPriority === 'true') isPriorityValue = true
      else if (isPriority === 'false') isPriorityValue = false

      let approvalStatusValue: APPROVAL_STATUS | undefined = undefined
      if (approvalStatus && approvalStatus !== '') {
        approvalStatusValue = approvalStatus as APPROVAL_STATUS
      }

      const nftFilterData = {
        searchWord: debouncedSearchWord,
        category,
        approvalStatus: approvalStatusValue,
        isPriority: isPriorityValue,
        collection,
        pageIndex,
        pageSize,
      }
      setNftFilterData(nftFilterData)
      return getNftForAdmin(nftFilterData)
    },
  })

  // Priority options
  const priorityOptions = [
    { label: '全て優先度', value: '' },
    { label: '優先', value: 'true' },
    { label: '非優先', value: 'false' },
  ]

  // Status options
  const statusOptions = [
    { label: '全てステータス', value: '' },
    ...Object.entries(renderApprovalStatus).map(([key, value]) => ({
      label: value,
      value: key,
    })),
  ]

  // Category options with default "all"
  const categoryOptions = [
    { label: '全てのカテゴリー', value: '' },
    ...(categoryData?.items?.map((item: Category) => ({
      label: item.name,
      value: item._id || '',
    })) || []),
  ]

  const text = {
    title: {
      manageNft: 'NFT管理',
    },
    tableHeader: {
      priority: '優先度',
      image: '画像',
      name: '名前',
      collection: 'コレクション',
      status: '状態',
      creator: '作成者',
      createdDate: '作成日',
      approver: '承認者',
      approveDate: '承認日',
      action: 'アクション',
    },
    search: {
      searchWord: 'NFTを名前で検索',
      category: 'カテゴリー',
      status: 'ステータス',
      priority: '優先度',
      collection: 'コレクション',
      search: '検索',
      clear: 'クリア',
      allCategories: '全てのカテゴリー',
      allCollections: '全てのコレクション',
    },
    buttons: {
      createNft: 'NFT作成',
      view: '詳細',
    },
    messages: {
      deleteSuccess: 'NFTを削除しました。',
      deleteFailed: 'NFTの削除に失敗しました。',
      categoryFetchFailed: 'カテゴリーの取得に失敗しました',
      collectionFetchFailed: 'コレクションの取得に失敗しました',
      confirmDelete: '"$NFT_NAME"を削除してもよろしいでしょうか。',
      prioritySuccess: 'NFTの優先度を設定しました。',
      priorityFailed: 'NFTの優先度の設定に失敗しました。',
    },
    modal: {
      yes: 'はい',
      cancel: 'キャンセル',
    },
  }

  // Handle search form changes
  // Individual filter change handlers
  const handleSearchWordChange = (value: string) => {
    setSearchWord(value)
    setPageIndex(1)
  }
  const handleCategoryChange = (value: string) => {
    setCategory(value)
    setPageIndex(1)
  }
  const handleApprovalStatusChange = (value: string) => {
    setApprovalStatus(value)
    setPageIndex(1)
  }
  const handleIsPriorityChange = (value: string) => {
    setIsPriority(value)
    setPageIndex(1)
  }
  const handleCollectionChange = (value: string) => {
    setCollection(value)
    setPageIndex(1)
  }

  // Handle collection search
  const handleCollectionSearch = (value: string) => {
    setCollectionSearch(value)
    setCollectionPage(1)
  }

  // Handle collection scroll for infinite scroll
  const handleCollectionPopupScroll = (
    e: React.UIEvent<HTMLDivElement, UIEvent>
  ) => {
    const target = e.target as HTMLDivElement
    if (
      target.scrollTop + target.offsetHeight >= target.scrollHeight - 10 &&
      collectionData &&
      collectionData.items.length < collectionData.totalItems &&
      !collectionLoading
    ) {
      setCollectionPage((prev) => prev + 1)
    }
  }

  // Delete logic
  const { mutate: handleDeleteNft, isPending: loadingDelete } = useMutation({
    mutationFn: async (id: string) => {
      await deleteNft(id)
    },
    onSuccess: () => {
      toast.success(text.messages.deleteSuccess)
      setDeletingId(null)
      refetchNfts()
    },
    onError: (error: any) => {
      toast.error(error?.response?.message || text.messages.deleteFailed)
      setDeletingId(null)
    },
  })

  // Set priority logic
  const { mutate: handleSetPriority, isPending: loadingPriority } = useMutation(
    {
      mutationFn: async ({
        id,
        isPriority,
      }: {
        id: string
        isPriority: boolean
      }) => {
        await setNftPriority(id, isPriority)
      },
      onSuccess: () => {
        toast.success(text.messages.prioritySuccess)
        refetchNfts()
      },
      onError: (error: any) => {
        toast.error(error?.response?.message || text.messages.priorityFailed)
      },
    }
  )

  // Table columns (wireframe order)
  const columns: ColumnsType<NftDetail> = [
    {
      title: text.tableHeader.priority,
      dataIndex: '_id',
      key: 'priority',
      width: '80px',
      render: (_, record) => {
        const { canSetPriority } = getPermission({
          approvalStatus: record.approvalStatus,
        })
        if (canSetPriority) {
          return (
            <Checkbox
              checked={record.isPriority}
              disabled={loadingPriority}
              onChange={(e) => {
                const isPriority = e.target.checked
                handleSetPriority({ id: record._id, isPriority })
              }}
            />
          )
        }
      },
    },
    {
      key: 'image',
      title: text.tableHeader.image,
      dataIndex: 'image',
      render: (src: string, record) => (
        <div className="flex justify-center">
          <PreviewNft
            src={src}
            isVideo={record?.isVideo}
            alt="preview"
            className="object-contain max-h-[120px] max-w-[120px] rounded-md"
          />
        </div>
      ),
    },
    {
      title: text.tableHeader.name,
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <span className="font-montserrat block truncate max-w-[200px]">
          {name}
        </span>
      ),
    },
    {
      title: text.tableHeader.collection,
      dataIndex: 'collection',
      key: 'collection',
      render: (collection: Collection) => (
        <span className="font-montserrat block truncate max-w-[200px]">
          {collection?.name}
        </span>
      ),
      onHeaderCell: () => ({
        style: { whiteSpace: 'nowrap', paddingLeft: 1, paddingRight: 1 },
      }),
    },
    {
      title: text.tableHeader.status,
      dataIndex: 'approvalStatus',
      key: 'status',
      render: (status) => <ApprovalStatusBadge status={status} />,
    },
    {
      title: text.tableHeader.creator,
      dataIndex: 'createdBy',
      key: 'creator',
      render: (createdBy: Admin) => (
        <span className="font-montserrat">{createdBy?.email || '-'}</span>
      ),
    },
    {
      title: text.tableHeader.createdDate,
      dataIndex: 'saleStartAt',
      key: 'createdDate',
      render: (date) => (
        <span className="font-montserrat">
          {date ? date.slice(0, 10) : '-'}
        </span>
      ),
    },
    {
      title: text.tableHeader.approver,
      dataIndex: 'approvedBy',
      key: 'approver',
      render: (approvedBy: Admin) => (
        <span className="font-montserrat">{approvedBy?.email || '-'}</span>
      ),
    },
    {
      title: text.tableHeader.approveDate,
      dataIndex: 'approvedAt',
      key: 'approveDate',
      render: (date) => (
        <span className="font-montserrat">
          {date ? date.slice(0, 10) : '-'}
        </span>
      ),
    },
    {
      title: text.tableHeader.action,
      key: 'action',
      width: 180,
      render: (_, record) => {
        const { canEdit, canDelete } = getPermission({
          approvalStatus: record.approvalStatus,
          creatorId: record.createdBy?._id || '',
        })
        return (
          <Space size={8}>
            <UiButton
              className="w-full flex !text-primary"
              title={<IconPen />}
              handleClick={() => router.push(`/admin/nfts/edit/${record._id}`)}
              isGradient={false}
              isDisabled={!canEdit}
            />
            <UiButton
              className="w-full flex !text-primary"
              title={<IconDelete />}
              handleClick={() => {
                if (!canDelete) return
                setDeletingId(record._id)
              }}
              isGradient={false}
              isDisabled={!canDelete || loadingDelete}
            />
            <UiButton
              className="w-full flex !text-primary"
              title={
                <span className="flex items-center gap-1">
                  <span className="mr-1">{text.buttons.view}</span>
                </span>
              }
              handleClick={() =>
                router.push(`/admin/nfts/detail/${record._id}`)
              }
              isGradient={false}
            />
          </Space>
        )
      },
    },
  ]

  return (
    <div>
      <TextAdminTitle title={text.title.manageNft} />
      {/* Search Bar */}
      <div className="mb-6 flex justify-between sm:gap-4 gap-2">
        <div className="flex flex-wrap gap-2 sm:gap-4 flex-1">
          <Input
            size="large"
            className="w-1/3"
            placeholder={text.search.searchWord}
            suffix={<IconSearch height={20} width={20} />}
            value={searchWord}
            onChange={(e) => handleSearchWordChange(e.target.value)}
            onPressEnter={() => handleSearchWordChange(searchWord)}
            allowClear
          />
          <Select
            size="large"
            placeholder={text.search.allCategories}
            value={category}
            onChange={handleCategoryChange}
            options={categoryOptions}
            loading={categoryLoading}
            allowClear
          />
          <Select
            size="large"
            className="w-[120px]"
            placeholder={text.search.status}
            value={approvalStatus}
            onChange={handleApprovalStatusChange}
            options={statusOptions}
            allowClear
          />
          <Select
            size="large"
            className="w-[120px]"
            placeholder={text.search.priority}
            value={isPriority}
            onChange={handleIsPriorityChange}
            options={priorityOptions}
            allowClear
          />
          <Select
            size="large"
            className="w-[120px]"
            placeholder={text.search.allCollections}
            value={collection}
            onChange={handleCollectionChange}
            options={[
              { label: text.search.allCollections, value: '' },
              ...(collectionData?.items?.map((item: Collection) => ({
                label: item.name,
                value: item._id || '',
              })) || []),
            ]}
            loading={collectionLoading}
            showSearch
            filterOption={false}
            onSearch={handleCollectionSearch}
            onPopupScroll={handleCollectionPopupScroll}
            allowClear
            notFoundContent={collectionLoading ? <Spin size="small" /> : null}
          />
        </div>
        {admin.role !== ROLE.approver && (
          <UiButton
            className="h-[40px]"
            title={text.buttons.createNft}
            handleClick={() => push(ROUTES.adminCreateNft)}
          />
        )}
      </div>
      <AdminTable
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={MESSAGE_FORM.noData}
            />
          ),
        }}
        loading={nftLoading}
        className="mt-[18px] custom-select custom-table"
        columns={columns}
        dataSource={nftData?.items || []}
        pagination={{
          total: nftData?.totalItems || 0,
          position: ['bottomCenter'],
          pageSize,
          showSizeChanger: false,
          current: pageIndex,
          onChange: (page) => setPageIndex(page),
        }}
        // scroll={{ y: tableHeight }}
        scroll={{ x: 'auto' }}
        rowKey="_id"
      />
      <UiModal
        isOpen={!!deletingId}
        header="NFT削除"
        onClose={() => setDeletingId(null)}
        confirmButton={{
          isShow: true,
          title: text.modal.yes,
          action: () => deletingId && handleDeleteNft(deletingId),
        }}
        cancelButton={{
          isShow: true,
          title: text.modal.cancel,
          action: () => setDeletingId(null),
        }}
      >
        {text.messages.confirmDelete.replace(
          '$NFT_NAME',
          nftData?.items?.find((item) => item._id === deletingId)?.name || 'NFT'
        )}
      </UiModal>
    </div>
  )
}
