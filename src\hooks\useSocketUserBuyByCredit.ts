import { useEffect, useMemo } from 'react'
import { useRouter } from 'next/router'
import { nftErrors, socketEvents } from '@/constants'
import socketUser from 'socket'
import {
  TShowNotification,
  TSocketResponse,
  TUseSocketUserBuyByCredit,
} from '@/interfaces'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'

const useSocketUserBuyByCredit = (props: TUseSocketUserBuyByCredit) => {
  const { setTransferStatus, setOpenModal, setTransactionHash, setErrStatus } =
    props
  const { query, pathname, push } = useRouter()
  const fincodeOrderId = query?.fincodeOrderId as string
  const userCancelBuyNftPage = pathname.includes('buy-cancel')
  const [cookies] = useCookies([STORAGEKEY.WALLET_ADDRESS])
  const walletAddress = cookies[STORAGEKEY.WALLET_ADDRESS]
  const isConnectSocket =
    !!fincodeOrderId && walletAddress && !userCancelBuyNftPage && !query.status

  const showNotification = (props: TShowNotification) => {
    const { data, transferStatus = 'processing' } = props
    if (!data.fincodeOrderId) return
    setOpenModal(true)
    setTransferStatus(transferStatus)
    setTransactionHash(data?.txHash || '')
  }

  const socket = useMemo(
    () => (isConnectSocket ? socketUser() : null),
    [isConnectSocket]
  )
  //when re
  const transactionHasBeenProcessed = () => {
    push(
      {
        pathname: pathname,
        query: { ...query, success: 'done' },
      },
      undefined,
      { shallow: true }
    )
  }
  const socketGetBuyNftStatus = () => {
    if (socket) {
      const dataJoinPrivateRoom = {
        walletAddress,
        fincodeOrderId,
      }

      socket.emit('user:joinPrivateRoom', dataJoinPrivateRoom)
      socket.on(
        socketEvents.CREDIT_AUTH_PAYMENT_SUCCESS,
        (data: TSocketResponse) => {
          showNotification({ data, transferStatus: 'processing' })
        }
      )

      socket.on(socketEvents.CREDIT_PROCESS_START, (data: TSocketResponse) => {
        console.log('CREDIT_PROCESS_START', data)
        showNotification({ data, transferStatus: 'processing' })
      })

      socket.on(
        socketEvents.CREDIT_PROCESS_SUCCESS,
        (data: TSocketResponse) => {
          socket.emit(socketEvents.CREDIT_PROCESS_DONE, {
            walletAddress,
            fincodeOrderId: data?.fincodeOrderId,
          })
          transactionHasBeenProcessed()
          showNotification({
            data,
            transferStatus: 'completed',
          })
        }
      )

      socket.on(socketEvents.CREDIT_PROCESS_FAILED, (data: TSocketResponse) => {
        socket.emit(socketEvents.CREDIT_PROCESS_DONE, {
          walletAddress,
          fincodeOrderId: data?.fincodeOrderId,
        })
        transactionHasBeenProcessed()

        setErrStatus(nftErrors[data.message || 'default'])
        showNotification({ data, transferStatus: 'fail' })
      })
    }
  }

  useEffect(() => {
    socketGetBuyNftStatus()
    return () => {
      socket?.disconnect()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [walletAddress, fincodeOrderId])
}

export default useSocketUserBuyByCredit
