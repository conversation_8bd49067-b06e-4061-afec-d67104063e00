const fs = require('fs')
const path = require('path')

// Static routes
const STATIC_ROUTES = [
  '',
  '/collections',
  '/news',
  '/knowledge',
  '/about-us',
  '/contact',
  '/faqs',
  '/privacy-policy',
  '/terms-of-use',
  '/commercial-transactions-law',
  '/knowledge/what-is-an-nft',
  '/knowledge/what-is-minting',
  '/knowledge/what-is-a-get-wallet',
  '/knowledge/how-to-stay-protected-in-web3',
]

const generateSitemapUrl = (url, lastmod, changefreq = 'weekly', priority = '0.7') => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com'
  return `
  <url>
    <loc>${baseUrl}${url}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`
}

async function generateSitemap() {
  try {
    // Generate static URLs
    const staticUrls = STATIC_ROUTES.map((route) => {
      let priority = '0.7'
      let changefreq = 'weekly'
      
      if (route === '') priority = '1.0' // Homepage
      if (route === '/collections') priority = '0.9'
      if (route === '/news') priority = '0.8'
      
      if (route === '' || route === '/collections' || route === '/news') {
        changefreq = 'daily'
      }
      
      return generateSitemapUrl(route, undefined, changefreq, priority)
    })

    // For static generation, you would fetch data here
    // const collections = await fetchCollections()
    // const news = await fetchNews()
    
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${staticUrls.join('')}
</urlset>`

    // Write sitemap to public directory
    const publicDir = path.join(process.cwd(), 'public')
    fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), sitemap)
    
    console.log('✅ Sitemap generated successfully!')
  } catch (error) {
    console.error('❌ Error generating sitemap:', error)
  }
}

generateSitemap()
