import { APPROVAL_STATUS, CONTRACT_TYPE } from '@/constants'
import { Admin } from './admin'
import { Paginated } from '.'

export type NftApprovalStats = {
  totalNft: number
  totalWaitingForApproval: number
  totalRejected: number
  totalApproved: number
  totalDraft: number
}

export type CreateNftPayload = {
  maximumNumber: number
  name: string
  description: string
  imageId: string
  tags: string[]
  attributes: {
    traitType: string
    value: string
  }[]
  collection: string
  price: number
  saleStartAt: string // or Date if parsed
  saleEndAt: string // or Date if parsed
  isDraft: boolean
}

export interface UpdateNftPayload {
  maximumNumber: number
  name: string
  description: string
  imageId?: string
  tags: string[]
  attributes: {
    traitType: string
    value: string
  }[]
  collection: string
  price: number
  externalLink: string
  saleStartAt: string
  saleEndAt: string
  isDraft: boolean
}

export type NftDetail = {
  _id: any // Replace `any` with `string` or `ObjectId` depending on usage
  itemId: number
  maximumNumber: number
  mintedNumber: number
  name: string
  description: string
  tags: string[]
  attributes: {
    traitType: string
    value: string
  }[]
  collection: {
    _id: string
    name: string
    standard: CONTRACT_TYPE
  }
  price: number
  externalLink: string
  saleStartAt: string // or Date
  saleEndAt: string // or Date
  image: string
  approvalStatus: APPROVAL_STATUS // extend as needed
  approvedBy: Admin
  approvedAt: string // or Date
  createdBy: Admin
  updatedBy: Admin
  isPriority?: boolean
  isVideo?: boolean
}

export type GetNftForAdminQuery = {
  pageIndex?: number // Default: 1
  pageSize?: number // Default: 12
  searchWord?: string
  category?: string
  collection?: string
  isPriority?: boolean
  approvalStatus?: APPROVAL_STATUS
  sortCondition?:
    | 'LATEST_UPDATED'
    | 'LATEST_CREATED'
    | 'OLDEST_CREATED'
    | 'OLDEST_SOLD'
    | 'LATEST_SOLD' // Default: LATEST_UPDATED
}

export type TCheckoutNftPayload = {
  nftId: string
  mintCount: number
}

export type TCancelPayment = {
  nftId: string
  mintCount: number
}

export type PaginatedNftDetail = Paginated<NftDetail>
