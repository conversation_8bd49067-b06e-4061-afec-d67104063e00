import React from 'react'

interface PropsType {
  className?: string
  title: string
}
export default function TextTitle(props: PropsType) {
  const { className, title = '', ...rest } = props
  return (
    <h2
      className={`text-4xl font-bold text-center text-tailwindNeutral1 flex items-end ${className}`}
      {...rest}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={45}
        height={29}
        viewBox="0 0 24 29"
        fill="none"
      >
        <path
          d="M2 2L22 27"
          stroke="black"
          strokeWidth={4}
          strokeLinecap="round"
        />
      </svg>

      {title}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="45"
        height="29"
        viewBox="0 0 24 29"
        fill="none"
      >
        <path
          d="M22 2L2 27"
          stroke="black"
          strokeWidth="4"
          strokeLinecap="round"
        />
      </svg>
    </h2>
  )
}
