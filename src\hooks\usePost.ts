import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-toastify'
import { ROUTES, textNotifications } from '@/constants'
import { ERR_MESSAGE } from '@/constants'

interface PostProps {
  queryKey: unknown[]
  callback: (data: any) => Promise<any>
}

const showCreateSuccessToast = () => {
  const isAdmin = window.location.pathname.startsWith(ROUTES.admin)
  let message: string
  if (isAdmin) {
    message = textNotifications.CREATE_SUCCESS
  } else {
    message = textNotifications.CREATE_SUCCESS_EN
  }
  toast.success(message)
}

export const usePost = ({ queryKey, callback }: PostProps) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => callback(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey })
      showCreateSuccessToast()
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message
      toast.error(ERR_MESSAGE[errorMessage] || textNotifications.CREATE_FAILED)
    },
  })
}
