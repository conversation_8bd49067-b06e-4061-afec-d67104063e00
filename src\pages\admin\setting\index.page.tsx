import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import { TextAdminTitle } from '@/components/texts/TextAdminTitle'
import { Input, Form } from 'antd'
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons'
import UiButton from '@/components/ui/Button'
import { usePost } from '@/hooks/usePost'
import { createKeys } from '@/services/apiCall/config'
import { useEffect } from 'react'
import { useRouter } from 'next/router'

const text = {
  settingConfig: 'システム設定',
  apiKey: 'APIキー',
  systemKey: 'システムキー',
  inputApiKeyFromGetPlatform:
    'GETプラットフォームからAPIキーを入力してください',
  inputSystemKeyFromGetPlatform:
    'GETプラットフォームからシステムキーを入力してください',
  save: 'セーブ ',
  pleaseEnterApiKey: 'APIキーを入力してください',
  pleaseEnterSecretKey: '秘密鍵を入力してください',
  apiLargeThan: 'APIキーは255文字以内にしてください',
  secretLargeThan: '秘密鍵は255文字以内にしてください',
}

export default function Setting() {
  const router = useRouter()
  useEffect(() => {
    router.replace('/404')
  }, [])

  const [form] = Form.useForm()

  const { mutate: saveKeys, status } = usePost({
    queryKey: ['admin-keys'],
    callback: async (data: { apiKey: string; secretKey: string }) => {
      await createKeys(data)
    },
  })
  const isLoading = status === 'pending'

  const handleFinish = (values: { apiKey: string; secretKey: string }) => {
    saveKeys(values, {
      onSuccess: () => {
        form.resetFields()
      },
    })
  }

  return (
    <>
      <TextAdminHeader title={text.settingConfig} />
      <div className="container">
        <Form form={form} layout="vertical" onFinish={handleFinish}>
          <TextAdminTitle title={text.apiKey} />
          <Form.Item
            name="apiKey"
            rules={[
              { required: true, message: text.pleaseEnterApiKey },
              { max: 255, message: text.apiLargeThan },
            ]}
          >
            <Input.Password
              className="md:w-1/2"
              size="large"
              placeholder={text.inputApiKeyFromGetPlatform}
              iconRender={(visible) =>
                visible ? (
                  <EyeTwoTone rev={undefined} />
                ) : (
                  <EyeInvisibleOutlined rev={undefined} />
                )
              }
              maxLength={255}
            />
          </Form.Item>
          <div className="p-4" />
          <TextAdminTitle title={text.systemKey} />
          <Form.Item
            name="secretKey"
            rules={[
              { required: true, message: text.pleaseEnterSecretKey },
              { max: 255, message: text.secretLargeThan },
            ]}
          >
            <Input.Password
              className="md:w-1/2"
              size="large"
              placeholder={text.inputSystemKeyFromGetPlatform}
              iconRender={(visible) =>
                visible ? (
                  <EyeTwoTone rev={undefined} />
                ) : (
                  <EyeInvisibleOutlined rev={undefined} />
                )
              }
              maxLength={255}
            />
          </Form.Item>
          <div className="p-4" />
          <UiButton
            title={text.save}
            handleClick={() => form.submit()}
            isDisabled={isLoading}
          />
        </Form>
      </div>
    </>
  )
}
