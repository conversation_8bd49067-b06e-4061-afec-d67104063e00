import React, { useState } from 'react'
import { Modal, Form, Input, Select } from 'antd'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import { ADMIN_ROLE } from '@/constants/user'
import AdminAccountStatistics from './AdminAccountStastistics'
import RecentActivity from './RecentActivity'
import ListAdminAccounts from './ListAdminAccounts'

const statusOptions = [
  { label: '有効', value: 'active' },
  { label: '無効', value: 'inactive' },
]

const AdminAccountsPage = () => {
  const [modalOpen, setModalOpen] = useState(false)

  const [form] = Form.useForm()

  // Fetch admin accounts (replace with real API call)

  return (
    <div className="rounded-md shadow text-white">
      <TextAdminHeader title="アカウント管理" />
      <div className="space-y-12">
        <AdminAccountStatistics />
        <RecentActivity />
        <ListAdminAccounts />
      </div>

      <Modal
        // title={editingAdmin ? '管理者アカウント編集' : '管理者アカウント追加'}
        open={modalOpen}
        // onOk={handleModalOk}
        onCancel={() => setModalOpen(false)}
        okText="保存"
        cancelText="キャンセル"
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ status: 'active', role: 'admin' }}
        >
          <Form.Item
            name="email"
            label="メールアドレス"
            rules={[
              { required: true, message: 'メールアドレスを入力してください' },
              {
                type: 'email',
                message: '有効なメールアドレスを入力してください',
              },
            ]}
          >
            <Input placeholder="メールアドレス" />
          </Form.Item>
          <Form.Item
            name="role"
            label="権限"
            rules={[{ required: true, message: '権限を選択してください' }]}
          >
            <Select
              options={Object.keys(ADMIN_ROLE).map((item) => ({
                value: item,
                label: ADMIN_ROLE[item],
              }))}
            />
          </Form.Item>
          <Form.Item
            name="status"
            label="状態"
            rules={[{ required: true, message: '状態を選択してください' }]}
          >
            <Select options={statusOptions} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AdminAccountsPage
