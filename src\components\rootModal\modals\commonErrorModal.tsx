import clsx from 'clsx'

import ModalLayout from '../modalLayout'
import { useStore } from '@/store'
import { IconWarning } from '@/icons'
import ButtonComponent from '@/components/buttons/Button'

const CommonErrorModal: React.FC = () => {
  const { message } = useStore()

  const renderMessage = (message: string[] | string) => {
    if (Array.isArray(message)) {
      return message.map((text, idx) => <div key={idx}>{text}</div>)
    }
    return message
  }
  const { setModalType } = useStore()

  return (
    <ModalLayout
      className="h-auto flex items-center"
      backgroundAfter="after:bg-gray-800"
      noHeader
    >
      <div className="flex flex-col items-center justify-center">
        <IconWarning className="-mt-16" />
        <div
          className={clsx(
            'mt-12 text-xl text-black text-center font-bold break-words line-clamp-4 px-5 mb-4',
            'max-sm:text-xl max-sm:p-0 max-sm:mb-3 max-sm:mt-6'
          )}
        >
          {message && renderMessage(message)}
        </div>
      </div>
      <ButtonComponent
        className="m-auto mt-12 max-sm:mt-6"
        type="primary"
        title="OK"
        onClick={() => setModalType({ type: null })}
      />
    </ModalLayout>
  )
}

export default CommonErrorModal
