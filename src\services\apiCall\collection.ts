import { API_URLS, ROLE } from '../../constants'
import {
  Collection,
  CollectionStats,
  PaginatedCollection,
  CollectionSearch,
} from '../../interfaces'
import { post, del, get } from './baseApi'

export const getCollectionStats = (): Promise<CollectionStats> => {
  return get(API_URLS.adminCollectionStats, {}, ROLE.systemAdmin)
}

export const getListCollections = (
  query: CollectionSearch
): Promise<PaginatedCollection> => {
  return get(API_URLS.adminCollections, query, ROLE.systemAdmin)
}

export const deleteCollection = async (
  ids: string | string[]
): Promise<void> => {
  return await del(API_URLS.adminCollections, { ids }, ROLE.systemAdmin)
}

export const createCollection = async (data: Collection): Promise<void> => {
  return await post(API_URLS.adminCollections, data, {}, ROLE.systemAdmin)
}
