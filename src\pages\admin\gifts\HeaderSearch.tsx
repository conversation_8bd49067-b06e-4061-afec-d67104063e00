import React, { useState } from 'react'
import { HiSearch } from 'react-icons/hi'
import { CheckboxValueType } from 'antd/es/checkbox/Group'
import { Checkbox, Col, Row } from 'antd'
import { useRouter } from 'next/router'

import { Search } from '@/components/common'
import ButtonComponent from '@/components/buttons/Button'
import { IHeaderSearch } from '@/interfaces'
import { PLACE_HOLDER } from '@/utils/string'
import { ETYPE_GIFT, PAGE_INFO_GIFT } from '@/constants'
import { formatObjValueEmpty } from '@/utils'

const optionCheckbox = [
  {
    label: '受け取った',
    value: ETYPE_GIFT.received,
  },
  {
    label: '受信していない',
    value: ETYPE_GIFT.notReceived,
  },
]

function HeaderSearch() {
  const { query, push } = useRouter()
  const [filterSearch, setFilterSearch] = useState<IHeaderSearch>({})

  const onChangeCheckBox = (value: CheckboxValueType[]) => {
    const firstValue = value[0]
    if (value.length === 0 || value.length === optionCheckbox.length) {
      delete filterSearch.status
      delete query.status
      setFilterSearch({
        ...formatObjValueEmpty({ ...query, ...filterSearch, pageIndex: 1 }),
      })
      return
    }
    setFilterSearch({
      ...formatObjValueEmpty({
        ...query,
        ...filterSearch,
        status: firstValue,
        pageIndex: 1,
      }),
    })
  }

  return (
    <Row align={'middle'} className="mt-3">
      <Col xxl={9} xl={7}>
        <Search
          searchValue={filterSearch.searchWord}
          handleClearAll={() =>
            setFilterSearch({ ...filterSearch, searchWord: '' })
          }
          handleSearch={() =>
            push({
              query: {
                ...query,
                searchWord: filterSearch.searchWord?.trim(),
                pageIndex: 1,
              },
            })
          }
          setSearchValue={(value) =>
            setFilterSearch({ ...filterSearch, searchWord: value as string })
          }
          placeholder={PLACE_HOLDER.searchGift}
          className=" border-tailwindNeutral3 rounded-md"
        />
      </Col>
      <Col xxl={4} xl={4} className="ml-5">
        <Checkbox.Group options={optionCheckbox} onChange={onChangeCheckBox} />
      </Col>
      <Col xxl={5} xl={5}>
        <ButtonComponent
          type="admin"
          title="検索"
          afterIcon={<HiSearch className="w-6 h-6 text-white" />}
          onClick={() =>
            push({
              query: {
                ...formatObjValueEmpty({
                  ...filterSearch,
                  searchWord: filterSearch.searchWord?.trim(),
                  pageIndex: PAGE_INFO_GIFT.index,
                  pageSize: PAGE_INFO_GIFT.size,
                  sortCondition: PAGE_INFO_GIFT.sortCondition,
                }),
              },
            })
          }
        />
      </Col>
    </Row>
  )
}

export default HeaderSearch
