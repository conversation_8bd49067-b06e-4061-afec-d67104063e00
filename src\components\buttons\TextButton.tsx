import { ButtonHTMLAttributes, ReactElement } from 'react'

type TextButtonProp = {
  className?: string
  startIcon?: ReactElement
  endIcon?: ReactElement
  title: string
} & Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'children'>

export const TextButton = ({
  className,
  startIcon,
  endIcon,
  title,
  ...props
}: TextButtonProp) => {
  return (
    <button
      {...props}
      className={`text-primary hover:text-primaryHover flex space-x-1 ${className}`}
    >
      {startIcon}
      <span>{title}</span>
      {endIcon}
    </button>
  )
}
