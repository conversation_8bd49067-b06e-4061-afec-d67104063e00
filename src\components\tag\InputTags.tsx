import clsx from 'clsx'
import { toast } from 'react-toastify'
import React, { useRef, useState } from 'react'

import { regexEmoji } from '@/utils'
import { MESSAGE_FORM } from '@/utils/string'
import TagList from './TagList'

export interface TagsInputProps {
  name?: string
  placeholder: string
  value?: string[]
  disabled?: boolean
  maxLength?: number
  classNames?: {
    input?: string
    tag?: string
    warp?: string
  }
  tags: string[]
  handleChange: (tags: string[]) => void
  isError?: boolean
  isSubmitting?: boolean
  onBlur?: boolean
}

type eventType = React.ChangeEvent<HTMLInputElement> &
  React.KeyboardEvent<HTMLInputElement>

const defaultSeparators = [',']

const checkAllSpace = (text: string) => {
  const isAllSpace = text === ''
  return !isAllSpace
}
const checkEmoji = (text: string) => {
  const isEmoji = text.match(regexEmoji)
  isEmoji && toast.warning(MESSAGE_FORM.dataEmoji)
  return !isEmoji
}

const tagExists = (text: string, tags: string[]) => {
  const tagExist = tags?.includes(text)
  tagExist && toast.warning(MESSAGE_FORM.tagExist)
  return !tagExist
}
const checkMaxLength = (text: string, tags: string[], maxLength: number) => {
  const isMaxLength = tags?.toString()?.length + text?.length > maxLength
  isMaxLength && toast.warning(`255${MESSAGE_FORM.maxCharacter}`)
  return !isMaxLength
}

const InputTags = (props: TagsInputProps) => {
  const {
    name,
    placeholder,
    disabled,
    classNames,
    maxLength = 255,
    handleChange,
    tags = [],
    isError,
  } = props
  const [text, setText] = useState<string>('')
  const handleOnKeyUp = (event: eventType) => {
    event.stopPropagation()
    const checkPressEnterKey = defaultSeparators.includes(event.key)
    const text: string = event.target.value.trim()

    if (checkPressEnterKey) {
      const isValidate =
        checkAllSpace(text) &&
        checkEmoji(text) &&
        tagExists(text, tags) &&
        checkMaxLength(text, tags, maxLength)
      isValidate && saveValue(event, text)
    }
  }

  const onBlurKey = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation()
    const textInput: string = event.target.value.trim()
    const isValidate =
      checkAllSpace(textInput) &&
      checkEmoji(textInput) &&
      tagExists(textInput, tags) &&
      checkMaxLength(textInput, tags, maxLength)
    isValidate && saveValue(event, text)
  }

  const saveValue = (
    event: eventType | React.ChangeEvent<HTMLInputElement>,
    text: string
  ) => {
    event.preventDefault()
    const newTags = tags?.length === 0 ? [text] : [...tags, text]
    handleChange(newTags)
    setText('')
    event.target.value = ''
  }

  const onTagRemove = (text: string) => {
    handleChange(tags?.filter((tag) => tag !== text))
  }
  const handleChangeText = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault()
    const text = event.target.value.replace(/、/g, '').replace(/,/g, '')
    setText(text)
  }

  const modalRef = useRef(null)

  return (
    <div className="">
      <div
        ref={modalRef}
        aria-labelledby={name}
        className={clsx(
          'border-[#444] border-[1px] flex items-center gap-2 p-2 flex-wrap bg-[#0a0a0a] min-h-[40px] hover:border-[var(--primary-color)] focus:border-[var(--primary-color)]',
          { 'border-red-500 hover:!border-red-500': isError },
          classNames?.warp
        )}
      >
        {tags?.map((tag) => (
          <TagList
            key={tag}
            className={classNames?.tag}
            text={tag}
            remove={onTagRemove}
            disabled={disabled}
          />
        ))}

        <input
          className={clsx(
            'border-0 placeholder-[var(--placeholder-color)] outline-none flex-1 px-2 bg-[#0a0a0a] hover:border-[var(--primary-color)] focus:border-[var(--primary-color)]',
            classNames?.input
          )}
          type="text"
          value={text}
          name={name}
          placeholder={tags.length > 0 ? '' : placeholder}
          onChange={handleChangeText}
          onBlur={onBlurKey}
          disabled={disabled}
          onKeyUp={handleOnKeyUp}
        />
      </div>
      <div className="text-gray-500 text-sm">
        {text && 'タグを入力して登録するには「、」を入力してください。'}
      </div>
    </div>
  )
}

export default InputTags
