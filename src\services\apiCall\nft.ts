import {
  API_URLS,
  APPROVAL_STATUS,
  DEFAULT_CONFIG_PARAMS,
  ROLE,
  ROUTES,
} from '@/constants'
import { get, post, patch, del } from './baseApi'
import {
  CreateNftPayload,
  GetNftForAdminQuery,
  NftApprovalStats,
  PaginatedNftDetail,
  UpdateNftPayload,
  NftDetail,
  TCheckoutNftPayload,
  TCancelPayment,
} from '@/interfaces'

export const getNFTDetail = async (id: string) => {
  return await get(`${API_URLS.adminMetadata}/${id}`, DEFAULT_CONFIG_PARAMS)
}

export const getUserNft = async (id: string) => {
  return await get(
    `${API_URLS.userDetailNft}/${id}`,
    DEFAULT_CONFIG_PARAMS,
    ROLE.user
  )
}

export const getNftDetailForAdmin = async (id: string) => {
  return get(
    `${API_URLS.adminMetadata}/${id}`,
    DEFAULT_CONFIG_PARAMS,
    ROLE.systemAdmin
  )
}

export const buyNft = async (id: string) => {
  const currentUrl = window.location.origin + ROUTES.nfts + `/${id}`
  const body = {
    id,
    successUrl: `${currentUrl}/buy`,
    cancelUrl: `${currentUrl}/buy-cancel`,
  }
  return await post(API_URLS.buyNft, body, DEFAULT_CONFIG_PARAMS, ROLE.user)
}

export const getFurusatoSetting = async () => {
  const data = await get(
    API_URLS.adminFurusatoSetting,
    DEFAULT_CONFIG_PARAMS,
    ROLE.systemAdmin
  )
  const formatDataToAntdSelectType = data?.map(({ intermediaryId }) => ({
    label: intermediaryId,
    value: intermediaryId,
  }))

  return formatDataToAntdSelectType
}

export const getNftApprovalStats = (): Promise<NftApprovalStats> => {
  return get(API_URLS.adminNftApprovalStats, {}, ROLE.systemAdmin)
}

export const createNft = (
  payload: CreateNftPayload
): Promise<{ message: string }> => {
  return post(API_URLS.adminMetadata, payload, {}, ROLE.systemAdmin)
}

export const updateNft = (
  id: string,
  payload: UpdateNftPayload
): Promise<any> => {
  return patch(`${API_URLS.adminMetadata}/${id}`, payload, ROLE.systemAdmin)
}

export const uploadNftImage = async (file: File): Promise<string> => {
  const formData = new FormData()
  formData.append('file', file)
  const res = await post(API_URLS.adminUploadImage, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
  return res
}

export const approveNft = (id: string, isApproved: boolean) => {
  return patch(
    `${API_URLS.adminMetadata}/${id}/approval`,
    {
      approvalStatus: isApproved
        ? APPROVAL_STATUS.approved
        : APPROVAL_STATUS.rejected,
    },
    ROLE.systemAdmin
  )
}

export const deleteNft = (id: string) => {
  return del(`${API_URLS.adminMetadata}/${id}`, {}, ROLE.systemAdmin)
}

export const getNftForAdmin = (
  query: GetNftForAdminQuery
): Promise<PaginatedNftDetail> => {
  return get(API_URLS.adminMetadata, query, ROLE.systemAdmin)
}

export const setNftPriority = (id: string, isPriority: boolean) => {
  return patch(
    `${API_URLS.adminMetadata}/${id}/priority`,
    { isPriority },
    ROLE.systemAdmin
  )
}

export const getCollectionNftsForUser = (
  query: GetNftForAdminQuery,
  collectionId: string
): Promise<PaginatedNftDetail> => {
  return get(API_URLS.collectionNfts.replace('{id}', collectionId), query)
}

export const getNftDetail = (id: string): Promise<NftDetail> => {
  return get(`${API_URLS.nfts}/${id}`)
}

export const checkoutGetPlatform = async (
  payload: TCheckoutNftPayload
): Promise<{
  checkoutUrl: string
  paymentCode: string
}> => {
  return await post(
    API_URLS.checkout,
    payload,
    DEFAULT_CONFIG_PARAMS,
    ROLE.user
  )
}

export const cancelPayment = async (
  payload: TCancelPayment
): Promise<{
  message: string
}> => {
  return await post(
    API_URLS.cancelPaymnet,
    payload,
    DEFAULT_CONFIG_PARAMS,
    ROLE.user
  )
}
