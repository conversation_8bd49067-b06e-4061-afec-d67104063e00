import React, { useMemo } from 'react'
import { MdClose } from 'react-icons/md'
import dynamic from 'next/dynamic'
import { BUTTON_SIZES } from '@/constants'
import { useTranslate } from '@/hooks'
const UiButton = dynamic(() => import('@/components/ui/Button'))

interface IButtonAction {
  isShow: boolean
  title: string | React.ReactNode
  action: any
}

export interface IModal {
  isOpen: boolean
  header: string
  onClose: any
  children: React.ReactNode
  cancelButton?: IButtonAction
  confirmButton?: IButtonAction
}

const UiModal = ({
  isOpen,
  onClose,
  header,
  children,
  cancelButton = {} as IButtonAction,
  confirmButton = {} as IButtonAction,
}: IModal) => {
  const trans = useTranslate()
  const {
    isShow: isShowCancel = true,
    title: cancelTitle,
    action: cancelAction,
  } = cancelButton

  const titleCancel = useMemo(() => {
    return cancelTitle || trans.close
  }, [cancelTitle, trans])

  const {
    isShow: isShowConfirm = false,
    title: confirmTitle = trans.confirm,
    action: confirmAction,
  } = confirmButton

  const handleClose = () => {
    onClose?.()
  }

  return (
    <>
      {isOpen && (
        <div className="fixed flex justify-center items-center top-0 left-0 right-0 bottom-0 bg-[rgba(10,10,10,0.75)] z-[999] p-4 backdrop-blur-md">
          <div className="flex flex-col rounded-lg bg-[rgba(10,10,10,0.7)] border border-1 border-[rgba(255,255,255,0.3)] relative w-full max-w-[43.5rem] min-h-[20rem] max-h-[90vh] z-9999 py-6 px-4 sm:py-8 sm:px-6 md:py-12 md:px-20 gap-6 sm:gap-8 md:gap-12 overflow-y-auto">
            <div
              className="absolute right-[17px] top-[17px] sm:right-[34px] sm:top-[34px] cursor-pointer z-10"
              onClick={handleClose}
            >
              <MdClose
                size={'1.25rem'}
                className="sm:w-6 sm:h-6"
                color="white"
              />
            </div>
            <div className="flex w-full justify-center text-lg sm:text-xl text-white font-semibold mt-2 sm:mt-0">
              {header}
            </div>
            <div className="flex flex-col justify-center items-center flex-1 text-white text-sm sm:text-base px-2 sm:px-0">
              {children}
            </div>
            <div className="flex flex-col sm:flex-row justify-center items-center w-full gap-3 sm:gap-5 mt-4 sm:mt-0">
              {isShowCancel && (
                <UiButton
                  size={BUTTON_SIZES.LG}
                  isGradient={false}
                  isBorder={true}
                  title={titleCancel}
                  className="w-full p-2 min-w-48"
                  handleClick={() => {
                    onClose?.()
                    cancelAction?.()
                  }}
                />
              )}

              {isShowConfirm && (
                <UiButton
                  size={BUTTON_SIZES.LG}
                  isGradient={true}
                  title={confirmTitle}
                  className="w-full p-2 min-w-48"
                  handleClick={() => {
                    confirmAction?.()
                  }}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </>
  )
}
export default React.memo(UiModal)
