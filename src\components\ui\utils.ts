import { useInfiniteQuery, UseInfiniteQueryResult } from '@tanstack/react-query'

export const fetchPosts = async ({ pageParam = 0 }: { pageParam?: number }) => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  const pageSize = 10
  const start = pageParam * pageSize

  // Generate mock data
  const posts = Array.from({ length: pageSize }, (_, i) => ({
    id: start + i,
    title: `Post ${start + i + 1}`,
    content: `This is the content of post ${
      start + i + 1
    }. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`,
    author: `User ${((start + i) % 5) + 1}`,
    createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
  }))

  // Simulate reaching end of data after 5 pages
  const hasMore = pageParam < 4

  return {
    data: posts,
    nextCursor: hasMore ? pageParam + 1 : undefined,
    hasMore,
  }
}

export const fetchProducts = async ({
  pageParam = 0,
}: {
  pageParam?: number
}) => {
  await new Promise((resolve) => setTimeout(resolve, 800))

  const pageSize = 12
  const start = pageParam * pageSize

  const products = Array.from({ length: pageSize }, (_, i) => ({
    id: start + i,
    name: `Product ${start + i + 1}`,
    price: Math.floor(Math.random() * 1000) + 10,
    category: ['Electronics', 'Clothing', 'Books', 'Home'][
      Math.floor(Math.random() * 4)
    ],
    rating: Math.floor(Math.random() * 5) + 1,
    image: `https://picsum.photos/200/200?random=${start + i}`,
  }))

  const hasMore = pageParam < 3

  return {
    data: products,
    nextCursor: hasMore ? pageParam + 1 : undefined,
    hasMore,
  }
}

interface InfiniteQueryConfig {
  queryKey: any[]
  queryFn: ({ pageParam }: { pageParam: any }) => Promise<any>
  getNextPageParam?: (lastPage: any, allPages: any[]) => any
  enabled?: boolean
  staleTime?: number
  refetchOnWindowFocus?: boolean
  // Add other react-query options as needed
}

export const useAppInfiniteQuery = (
  config: InfiniteQueryConfig
): UseInfiniteQueryResult<any, Error> => {
  return useInfiniteQuery({
    queryKey: config.queryKey,
    queryFn: config.queryFn,
    getNextPageParam:
      config.getNextPageParam ||
      ((lastPage, allPages) => {
        // Default behavior: return next page number if hasMore is true
        if (lastPage?.hasMore) {
          return allPages.length
        }
        return undefined
      }),
    initialPageParam: 0,
    enabled: config.enabled,
    staleTime: config.staleTime,
    refetchOnWindowFocus: config.refetchOnWindowFocus,
    // Add other options as needed
  })
}
