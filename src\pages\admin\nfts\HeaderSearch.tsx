import React, { useLayoutEffect, useState } from 'react'
import { Col, Form, Row, Select } from 'antd'
import { HiSearch } from 'react-icons/hi'
import { useRouter } from 'next/router'

import ButtonComponent from '@/components/buttons/Button'
import { Search } from '@/components/common'
import { IHeaderSearch } from '@/interfaces'
import { NFTのタイプ, PLACE_HOLDER, ステータス } from '@/utils/string'
import { TYPE_NFT, TYPE_STATUS_NFT } from '@/constants/type'
import { formatObjValueEmpty, getTypeSelectedNFT } from '@/utils'
import { ENFT_TYPE, METADATA_STATUS } from '@/constants'

export const HeaderSearch = () => {
  const { push, query } = useRouter()
  const [form] = Form.useForm()
  const [filterInfo, setFilterInfo] = useState<IHeaderSearch>({
    itemType: ENFT_TYPE.all,
    status: METADATA_STATUS.all,
  })

  useLayoutEffect(() => {
    setFilterInfo(query)
  }, [query])

  const onFinish = () => {
    push({
      query: {
        ...formatObjValueEmpty({
          ...filterInfo,
          searchWord: filterInfo?.searchWord?.trim(),
        }),
        pageIndex: 1,
      },
    })
  }

  return (
    <Form form={form} onFinish={onFinish}>
      <Row align={'middle'} className="gap-8">
        <Col xxl={9} xl={7}>
          <Search
            form={false}
            searchValue={filterInfo.searchWord}
            handleClearAll={() =>
              setFilterInfo({ ...filterInfo, searchWord: '' })
            }
            setSearchValue={(value) =>
              setFilterInfo({ ...filterInfo, searchWord: value as string })
            }
            placeholder={PLACE_HOLDER.searchMyNFT}
            className=" border-tailwindNeutral3 rounded-md"
          />
        </Col>
        <Col xxl={4} xl={5}>
          <Row align={'middle'} justify={'space-between'}>
            <span>NFTのタイプ</span>
            <Select
              value={Number(filterInfo.itemType) || ENFT_TYPE.all}
              defaultValue={filterInfo.itemType || ENFT_TYPE.all}
              options={NFTのタイプ}
              onChange={(value: TYPE_NFT) =>
                setFilterInfo({
                  ...filterInfo,
                  itemType: getTypeSelectedNFT(value) as TYPE_NFT,
                })
              }
              style={{ width: '60%' }}
            />
          </Row>
        </Col>
        <Col xxl={4} xl={5}>
          <Row align={'middle'} justify={'space-between'}>
            <span>ステータス</span>
            <Select
              value={filterInfo.status || METADATA_STATUS.all}
              defaultValue={filterInfo.status || METADATA_STATUS.all}
              options={ステータス}
              onChange={(value: TYPE_STATUS_NFT) =>
                setFilterInfo({
                  ...filterInfo,
                  status: getTypeSelectedNFT(value) as TYPE_STATUS_NFT,
                })
              }
              style={{ width: '60%' }}
            />
          </Row>
        </Col>
        <Col xxl={3} xl={3}>
          <ButtonComponent
            className="w-full"
            type="link"
            title="検索"
            afterIcon={<HiSearch className="w-5 h-5 text-white" />}
            style={{ height: 40 }}
            typeSubmit="submit"
          />
        </Col>
      </Row>
    </Form>
  )
}
