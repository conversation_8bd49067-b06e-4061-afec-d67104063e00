import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { message } from 'antd'

import { getUserOwnedNfts } from '@/services/apiCall/nft-minted'
import { getListCategories } from '@/services/apiCall'
import { setProfileImage } from '@/services/apiCall/users'
import { MyNftCard } from '@/components/card/MyNftCard'
import SearchFilterBar from '@/pages/home/<USER>/SearchFilterBar'
import { CARD_SIZES } from '@/constants'
import { useTranslate } from '@/hooks'
import { IMAGE } from '@/utils/string'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'
import { UiPagination } from '@/components/ui/Pagination'
import Head from 'next/head'

const MyNFTsPage = () => {
  const router = useRouter()
  const trans = useTranslate()
  const queryClient = useQueryClient()
  const [cookies] = useCookies([
    STORAGEKEY.WALLET_ADDRESS,
    STORAGEKEY.USER_ACCESS_TOKEN,
  ])
  // State for search and filter
  const [searchValue, setSearchValue] = useState('')
  const [categoryValue, setCategoryValue] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [appliedSearchValue, setAppliedSearchValue] = useState('')
  const [appliedCategoryValue, setAppliedCategoryValue] = useState('')
  const pageSize = 12 // Show 12 NFTs per page (3 columns x 4 rows)

  // Get current page from URL query
  useEffect(() => {
    const page = router.query.page ? Number(router.query.page) : 1
    setCurrentPage(page)
  }, [router.query.page])

  // Query for categories
  const { data: categoriesData } = useQuery({
    queryKey: ['userCategories'],
    queryFn: () => getListCategories(),
  })

  // Query for user NFTs with pagination and filters
  const {
    data: userNfts,
    isLoading: nftsLoading,
    error: nftsError,
  } = useQuery({
    queryKey: [
      'userNfts',
      cookies[STORAGEKEY.WALLET_ADDRESS],
      currentPage,
      appliedSearchValue,
      appliedCategoryValue,
    ],
    queryFn: () =>
      getUserOwnedNfts(cookies[STORAGEKEY.WALLET_ADDRESS] || '', {
        pageIndex: currentPage,
        pageSize,
        searchWord: appliedSearchValue || undefined,
        category: appliedCategoryValue || undefined,
      }),
    enabled: !!cookies[STORAGEKEY.USER_ACCESS_TOKEN],
  })

  // Mutation for setting profile image
  const setProfileImageMutation = useMutation({
    mutationFn: ({ nftId }: { nftId: string }) => setProfileImage(nftId),
    onSuccess: () => {
      message.success(trans.profile_image_upload_success)
      // Optionally refetch user data or NFTs to reflect the change
      queryClient.invalidateQueries({ queryKey: ['userNfts'] })
      router.reload()
    },
    onError: (error) => {
      console.error('Failed to set profile image:', error)
      message.error(trans.profile_image_set_error)
    },
  })

  // Handle search
  const handleSearch = () => {
    setAppliedSearchValue(searchValue)
    setAppliedCategoryValue(categoryValue)
    setCurrentPage(1)
    router.push({
      pathname: router.pathname,
      query: { ...router.query, page: 1 },
    })
  }

  // Handle category change
  const handleCategoryChange = (value: string) => {
    setCategoryValue(value)
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    router.push({
      pathname: router.pathname,
      query: { ...router.query, page: page },
    })
  }

  // Handle set profile image
  const handleSetProfileImage = (nftId: string) => {
    if (cookies[STORAGEKEY.WALLET_ADDRESS]) {
      setProfileImageMutation.mutate({
        nftId,
      })
    }
  }

  return (
    <>
      <Head>
        <title>{trans.my_nfts}</title>
      </Head>
      <div className="text-white">
        {/* Main Content */}
        <main className="container mx-auto">
          {/* Page Title */}
          <div className="mb-8">
            <h1 className="font-montserrat font-semibold text-3xl tracking-[0.02em] leading-[170%] text-white">
              {trans.my_nfts}
            </h1>
          </div>

          {/* Search and Filter Bar */}
          <div className="mb-8">
            <SearchFilterBar
              searchValue={searchValue}
              categoryValue={categoryValue}
              categories={categoriesData?.items || []}
              onSearchChange={setSearchValue}
              onCategoryChange={handleCategoryChange}
              onSearch={handleSearch}
              isLoading={nftsLoading}
            />
          </div>

          {/* Loading State */}
          {nftsLoading && (
            <div className="text-center py-12">
              <div className="text-white text-lg">{trans.loading_nfts}</div>
            </div>
          )}

          {/* Error State */}
          {nftsError && (
            <div className="text-center py-12">
              <div className="text-red-400 text-lg">
                {trans.failed_to_load_nfts}
              </div>
            </div>
          )}

          {/* NFT Grid */}
          {!nftsLoading && !nftsError && userNfts && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                {userNfts.items?.map((nft) => (
                  <MyNftCard
                    key={nft._id}
                    name={nft.name}
                    imageUrl={nft.image || IMAGE.defaultImage}
                    size={CARD_SIZES.MD}
                    actions={[
                      {
                        label: trans.view,
                        onClick: () =>
                          router.push(`/my-profile/nft/${nft.metadataId}`),
                      },
                      // Conditionally show "Set Profile" action
                      ...(nft.canSetAsProfileImage && nft._id
                        ? [
                            {
                              label: setProfileImageMutation.isPending
                                ? 'Setting...'
                                : trans.set_profile,
                              onClick: () =>
                                handleSetProfileImage(nft._id || ''),
                              disabled: setProfileImageMutation.isPending,
                            },
                          ]
                        : []),
                    ]}
                  />
                ))}
              </div>

              {/* Pagination */}
              {userNfts && userNfts.totalItems > pageSize && (
                <div className="flex justify-center">
                  <UiPagination
                    total={userNfts.totalItems}
                    pageSize={pageSize}
                    current={currentPage}
                    onChange={handlePageChange}
                    showSizeChanger={false}
                    showQuickJumper={true}
                    locale={{
                      items_per_page: '',
                      jump_to: trans.pagination_jump_to,
                      jump_to_confirm: trans.pagination_go_to,
                      page: trans.pagination_page,
                    }}
                  />
                </div>
              )}
            </>
          )}

          {/* Empty State */}
          {!nftsLoading &&
            !nftsError &&
            userNfts &&
            userNfts.items?.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg">
                  {appliedSearchValue || appliedCategoryValue
                    ? trans.no_nfts_found_with_filters
                    : trans.no_nfts_owned}
                </div>
              </div>
            )}
        </main>
      </div>
    </>
  )
}

export default MyNFTsPage
