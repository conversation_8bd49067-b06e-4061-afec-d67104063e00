import { Pagination, PaginationProps } from 'antd'
import React from 'react'
import { createItemRender, ItemsRenderConfig } from './ItemsRender'

type CustomPaginationProps = PaginationProps & {
  className?: string
  itemsRender?: () => ItemsRenderConfig
}

export const UiPagination = (props: CustomPaginationProps) => {
  const { itemsRender, ...restProps } = props

  return (
    <Pagination
      {...restProps}
      className="custom-pagination"
      itemRender={createItemRender(itemsRender?.())}
    />
  )
}
