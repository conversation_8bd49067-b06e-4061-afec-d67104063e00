import React, { useEffect, useMemo } from 'react'
import { useGetUserBalance } from '@/hooks'
import { formatNumberWithComma } from '@/utils/format'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'
import { walletStore } from '@/store/slices/wallet'

interface BalanceDisplayProps {
  className?: string
}

const BalanceDisplay: React.FC<BalanceDisplayProps> = ({ className = '' }) => {
  const { balance, loading, refetch } = useGetUserBalance()
  const walletBalance = walletStore((store) => store.balance)
  const successPayment = walletStore((store) => store.successPayment)
  const setBalance = walletStore((store) => store.setBalance)
  const [cookies] = useCookies([STORAGEKEY.USER_ACCESS_TOKEN])
  const isConnected = useMemo<boolean>(() => {
    return !!cookies[STORAGEKEY.USER_ACCESS_TOKEN]
  }, [cookies[STORAGEKEY.USER_ACCESS_TOKEN]])

  // refetch balance when connect wallet or checkout nft successfully
  useEffect(() => {
    if (isConnected) refetch()
  }, [isConnected, successPayment])

  useEffect(() => {
    if (balance) {
      setBalance(balance)
    }
  }, [balance])

  if (loading) {
    return (
      <div
        className={`flex items-center gap-2 px-3 py-2 bg-getCardBg text-white rounded text-sm ${className}`}
      >
        <span>Loading...</span>
      </div>
    )
  }

  if (!balance || !isConnected) {
    return null
  }

  return (
    <div
      className={`flex items-center gap-2 px-3 py-2 bg-getCardBg text-white rounded text-sm ${className}`}
    >
      <span>GP {formatNumberWithComma(walletBalance?.getPay) || 0}</span>
      <span className="text-gray-300">|</span>
      <span>G {formatNumberWithComma(walletBalance?.getToken) || 0}</span>
    </div>
  )
}

export default BalanceDisplay
