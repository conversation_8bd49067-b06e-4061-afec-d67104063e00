import { useState, useEffect } from 'react'
import { Form } from 'antd'
import NewsForm from '@/pages/admin/news/NewsForm'
import { useRouter } from 'next/router'
import { useGet } from '@/hooks/useGet'
import { approveNews, getNewsById } from '@/services/apiCall/news'
import { useStore } from '@/store'
import { ROLE } from '@/constants'
import { usePatch } from '@/hooks/usePatch'
import { toast } from 'react-toastify'
import { APPROVAL_STATUS } from '@/constants'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'

const TEXT = {
  APPROVE: '承認',
  REJECT: '却下',
  APPROVE_SUCCESS: 'ニュースを承認しました',
  REJECT_SUCCESS: 'ニュースを却下しました',
  VIEW_NEWS_DETAILS: 'ニュース詳細',
}

export default function ViewNewsPage() {
  const role = useStore((state) => state.role)
  const { canApprove } = useStore((state) => state)
  const [form] = Form.useForm()
  const router = useRouter()
  const { id } = router.query
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imageUrl, setImageUrl] = useState<string>('')
  const [editorValue, setEditorValue] = useState('')

  // Fetch news data by ID
  const { data: newsData, isLoading: loadingNews } = useGet({
    queryKey: ['news', id],
    callback: async () => {
      if (id) {
        return await getNewsById(id)
      }
      return null
    },
  })

  // usePost for approval/rejection
  const { mutate: updateApproval } = usePatch({
    queryKey: ['news', id],
    callback: (params: { approvalStatus: APPROVAL_STATUS }) => {
      return approveNews(
        id as string,
        params,
        role as ROLE.systemAdmin | ROLE.approver
      )
    },
  })

  // Populate form when newsData is loaded
  useEffect(() => {
    if (newsData) {
      form.setFieldsValue({
        title: newsData.title,
        context: newsData.context,
      })
      setEditorValue(newsData.context || '')
      setImageUrl(newsData.imageUrl || '')
    }
  }, [newsData, form])

  const handleApprove = () => {
    updateApproval(
      { approvalStatus: APPROVAL_STATUS.approved },
      {
        onSuccess: () => {
          toast.success(TEXT.APPROVE_SUCCESS)
          router.back()
        },
      }
    )
  }

  const handleReject = () => {
    updateApproval(
      { approvalStatus: APPROVAL_STATUS.rejected },
      {
        onSuccess: () => {
          toast.success(TEXT.REJECT_SUCCESS)
          router.back()
        },
      }
    )
  }

  return (
    <div>
      <TextAdminHeader title={TEXT.VIEW_NEWS_DETAILS} />
      <NewsForm
        form={form}
        loading={loadingNews}
        imageFile={imageFile}
        setImageFile={setImageFile}
        editorValue={editorValue}
        setEditorValue={setEditorValue}
        onFinish={handleApprove}
        onSaveDraft={handleReject}
        imageUrl={imageUrl || ''}
        setImageUrl={setImageUrl}
        disabled={true}
        canApprove={canApprove}
        canCreate={false}
        approvalStatus={newsData?.approvalStatus}
        saveButtonLabel={TEXT.REJECT}
        publishButtonLabel={TEXT.APPROVE}
      />
    </div>
  )
}
