import { Admin, Paginated } from '.'
import { APPROVAL_STATUS } from '@/constants'

export type TBannerCreate = {
  tags: string[] | null
  description: string
  imageId: string
  title: string
}

export type TBanner = {
  _id: string
  tags: string[]
  description: string
  imageId: string
  createdAt: string
  updatedAt: string
  image: string
  index: number
  collection:
    | {
        name: string
        id: string
      }
    | string
  externalLink: string
  title: string
  createdBy?: Partial<Admin>
  approvedBy?: {
    name: string
    email: string
  }
  approvalStatus?: APPROVAL_STATUS
}
export type TBannerData = {
  items: TBanner[]
  pageIndex: number
  pageSize: number
  totalItems: number
}

export type BannerHeaderPropsType = {
  total?: number
  className?: string
  setBanners: React.Dispatch<React.SetStateAction<[] | TBanner[]>>
  setPageIndex: React.Dispatch<React.SetStateAction<number>>
}

export type TBannerActionTable = {
  data: TBanner
}

export type TNewsTableListProps = {
  bannersData: TBannerData | undefined
  isLoading?: boolean
  refetchBanner: () => void
  banners: TBanner[]
  pageIndex: number
  setPageIndex: React.Dispatch<React.SetStateAction<number>>
  setBanners: React.Dispatch<React.SetStateAction<TBanner[] | []>>
  totalItems: number | null
  pageSize: number
  searchWord: string
  setSearchWord: React.Dispatch<React.SetStateAction<string>>
}

export type TDeleteType = 'deleteBanner' | 'deleteBanners'

export type TUpdateIndexProps = {
  id: string
  index: number
}[]
export type TDefaultBannerSearch = {
  pageSize?: number
  pageIndex?: number
  sortCondition?: string
  searchWord?: string
}

export type PaginatedBanner = Paginated<TBanner>
