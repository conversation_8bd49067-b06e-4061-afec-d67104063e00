import Image from 'next/image'
import { formatContext } from '@/utils/tinyMCE'
import Link from 'next/link'
interface NewsCardProps {
  id: string | undefined
  title: string
  imageUrl: string | undefined
  createdAt: string | undefined
  context: string | undefined
}

const formatDate = (date: string | undefined) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

const NewsCard = ({
  id,
  title,
  imageUrl,
  createdAt,
  context,
}: NewsCardProps) => {
  return (
    <Link href={`/news/${id}`}>
      <div className="group flex gap-5 max-md:flex-col w-full bg-getCardBg rounded-lg transition-all duration-300 cursor-pointer hover:bg-neutral-700 p-5 h-full min-h-[200px] overflow-hidden">
        {/* Image */}
        <div className="relative min-w-[240px] sm:h-full h-[160px] overflow-hidden rounded-lg">
          <Image
            src={imageUrl || '/placeholder-image.jpg'}
            alt={title}
            fill
            className="object-cover rounded-lg transition-all duration-300 group-hover:scale-105"
          />
        </div>

        {/* Content */}
        <div className="flex flex-col flex-1 gap-5 min-w-0">
          <h2 className="font-semibold text-white font-montserrat text-[14px] leading-[140%] tracking-[0.02em] line-clamp-2 transition duration-300">
            {title}
          </h2>
          <p className="font-montserrat font-normal text-[12px] leading-[140%] tracking-[0.02em] text-white">
            {formatDate(createdAt)}
          </p>
          <p className="font-montserrat font-normal text-[13px] leading-[140%] tracking-[0.02em] text-white line-clamp-3">
            {formatContext(context || '')}
          </p>
        </div>
      </div>
    </Link>
  )
}

export default NewsCard
