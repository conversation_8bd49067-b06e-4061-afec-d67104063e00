import { TNewsItem } from '@/interfaces'
import NewsCard from './NewsCard'
import { UiPagination } from '@/components/ui/Pagination'

interface NewsListProps {
  items: TNewsItem[]
  totalItems: number
  pageSize: number
  currentPage: number
  onPageChange: (page: number) => void
}

const NewsList = ({
  items,
  totalItems,
  pageSize,
  currentPage,
  onPageChange,
}: NewsListProps) => {
  return (
    <div className="space-y-6">
      <div className="grid gap-6">
        {items.map((news) => (
          <NewsCard
            key={news._id}
            id={news._id}
            title={news.title}
            imageUrl={news.imageUrl}
            createdAt={news.createdAt}
            context={news.context}
          />
        ))}
      </div>

      {totalItems > pageSize && (
        <div className="flex justify-center mt-8">
          <UiPagination
            current={currentPage}
            total={totalItems}
            pageSize={pageSize}
            onChange={onPageChange}
            showSizeChanger={false}
          />
        </div>
      )}
    </div>
  )
}

export default NewsList
