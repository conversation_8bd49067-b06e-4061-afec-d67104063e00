import { useEffect, useState } from 'react'
import { getCurrentLanguage, setLanguage } from '@/utils/language'

interface LanguageSwitcherProps {
  className?: string
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className = '',
}) => {
  const [currentLang, setCurrentLang] = useState('en')

  // Initialize language from localStorage or default to English
  useEffect(() => {
    const storedLang = getCurrentLanguage()
    setCurrentLang(storedLang)
  }, [])

  const handleLanguageChange = (lang: string) => {
    // Store the new language preference
    setLanguage(lang)
    setCurrentLang(lang)

    // Reload the page to apply the language change
    window.location.reload()
  }

  const getButtonStyles = (language: 'en' | 'ja') => {
    return `
      ${
        currentLang === language
          ? 'text-primary hover:text-primaryHover'
          : 'text-white hover:text-neutral-300'
      }
      font-montserrat font-normal text-[12px] leading-[180%] tracking-[0.02em]
    `
  }

  return (
    <div className={`flex items-center ${className}`}>
      <button
        onClick={() => handleLanguageChange('ja')}
        className={getButtonStyles('ja')}
      >
        JA
      </button>
      <span className="mx-2 text-neutral-400">|</span>
      <button
        onClick={() => handleLanguageChange('en')}
        className={getButtonStyles('en')}
      >
        EN
      </button>
    </div>
  )
}

export default LanguageSwitcher
