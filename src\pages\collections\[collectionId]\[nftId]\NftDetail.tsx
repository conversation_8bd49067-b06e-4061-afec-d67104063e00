import { useGetWallet } from '@/hooks/useGetWallet'
import Image from 'next/image'
import { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/router'
import clsx from 'clsx'
import { useTranslate } from '@/hooks/useTranslate'
import { useMutation } from '@tanstack/react-query'
import StandardBadge from '@/components/badge/StandardBadge'
import UiButton from '@/components/ui/Button'
import UiModal from '@/components/ui/Modal'
import { IMAGE } from '@/utils/string'
import {
  PAYMENT_STATUS,
  ROUTES,
  CONTRACT_TYPES,
  SOCKET_EVENTS,
} from '@/constants'
import {
  formatTimeToUTC,
  formatWithCurrency,
  getFontSizeByNumberOfCharacters,
} from '@/utils'
import { checkoutGetPlatform, cancelPayment } from '@/services/apiCall/nft'
import InputNumber from '@/components/ui/InputNumber'
import Spinner from '@/components/ui/Spinner'
import { BUTTON_SIZES } from '@/constants'
import UiStatusContent from '@/components/ui/StatusContent'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'
import { useSearchParams } from 'next/navigation'
import { useAppContext } from '@/hooks/useAppContext'
import { useViewport } from '@/hooks'
import useMobileConnect from '@/hooks/useMobileConnect'
import { TCancelPayment, TCheckoutNftPayload } from '@/interfaces'
import { TabButton } from '@/components/buttons'
import { VideoPreview } from '@/components/nfts'
import Link from 'next/link'
import { LinkOutlined } from '@ant-design/icons'

// Transfer status type
type TTransferStatus = 'processing' | 'fail' | 'success'

// Extended NFT interface to match the functionality
interface NFTDetailProps {
  nft: {
    _id?: string
    name: string
    description: string
    price: number
    maximumNumber: number
    mintedNumber: number
    saleStartAt: string
    saleEndAt: string
    image: string
    isVideo?: boolean
    status?: string
    mintedAt?: string
    soldAt?: string
    tags?: string[]
    externalLink
    attributes: {
      traitType: string
      value: string
    }[]
    collection: {
      _id: string
      name: string
      standard: string
    }
  }
  refetchDetail: any
  isLoading?: boolean
}

export default function NFTDetail({
  nft,
  refetchDetail,
  isLoading,
}: NFTDetailProps) {
  const trans = useTranslate()
  const [amount, setAmount] = useState(1)
  const [openModalBuyNft, setOpenModalBuyNft] = useState<boolean>(false)
  const [activeTab, setActiveTab] = useState<'info' | 'attributes'>('info')
  const search = useSearchParams()
  const paymentStatus = search.get('paymentStatus')
  const mintCount = search.get('mintCount')
  const { socket } = useAppContext()

  const checkoutStatus = {
    IDLE: 'idle',
    PROCESSING: 'processing',
    FAIL: 'fail',
    CONFIRMING: 'confirming',
    SUCCESS: 'success',
    CANCEL: 'cancel',
  }
  const { windowWidth } = useViewport()
  const isMobile = windowWidth < 768
  const { connectWalletMobile } = useMobileConnect({ isMobile })
  const router = useRouter()

  const [status, setCheckoutStatus] = useState<string>(checkoutStatus.IDLE)
  // const status = checkoutStatus.SUCCESS

  // Check if NFT is ERC-721 (unique token, can't have multiple quantities)
  const isERC721 = nft?.collection?.standard === CONTRACT_TYPES[0] // 'ERC-721'

  // Set amount to 1 for ERC-721 tokens since they are unique
  useEffect(() => {
    if (isERC721) {
      setAmount(1)
    }
  }, [isERC721])

  // Checkout NFT using useMutation instead of usePost
  const { mutate, isPending: loading } = useMutation({
    mutationFn: (data: TCheckoutNftPayload) => checkoutGetPlatform(data),
    onSuccess: (res: { checkoutUrl: string; paymentCode: string }) => {
      setCheckoutStatus(checkoutStatus.PROCESSING)
      if (res.checkoutUrl) {
        window.location.href = res.checkoutUrl
      }
    },
    onError: () => {
      setCheckoutStatus(checkoutStatus.FAIL)
    },
  })

  const { mutate: cancelPaymentMutation } = useMutation({
    mutationFn: (data: TCancelPayment) => cancelPayment(data),
    onSuccess: () => {
      router.replace(
        router.pathname
          .replace('[collectionId]', router?.query?.collectionId as string)
          .replace('[nftId]', router?.query?.nftId as string),
        '',
        { shallow: true }
      )
    },
    onError: () => {
      setCheckoutStatus(checkoutStatus.FAIL)
    },
    retry: false,
  })

  const getHeaderModal = (status: string) => {
    if (status === checkoutStatus.IDLE) {
      return trans.payment_confirmation
    }
    return ''
  }
  const [transferStatus, setTransferStatus] = useState<TTransferStatus>()

  // connect wallet using useGetWallet
  const { connectWallet } = useGetWallet()
  const [cookies] = useCookies([STORAGEKEY.USER_ACCESS_TOKEN])
  const isConnectedWallet = useMemo<boolean>(() => {
    return !!cookies[STORAGEKEY.USER_ACCESS_TOKEN]
  }, [cookies[STORAGEKEY.USER_ACCESS_TOKEN]])
  const [mintErrorRetry, setMintErrorRetry] = useState<boolean>(false)

  const handleAmountChange = (value: number | undefined) => {
    if (value !== undefined) {
      setAmount(value)
    }
  }

  // Check if NFT is currently open for sale
  const isNFTOpenForSale = () => {
    const now = new Date()
    const saleStart = new Date(nft?.saleStartAt)
    const saleEnd = new Date(nft?.saleEndAt)

    return (
      now >= saleStart &&
      now <= saleEnd &&
      nft?.mintedNumber < nft?.maximumNumber
    )
  }

  const isNftSoldOut = nft?.mintedNumber === nft?.maximumNumber

  const checkout = () => {
    if (!isConnectedWallet) {
      connectWallet()
      return
    }
    mutate({
      nftId: nft?._id as string,
      mintCount: amount,
    })
  }

  //buy nft (old flow, not used for modal confirm)
  const buyNftByCard = async () => {
    if (!isConnectedWallet) {
      isMobile ? connectWalletMobile() : await connectWallet()
      return
    }
    setOpenModalBuyNft(true)
  }

  const onBuyNftError = () => {
    setOpenModalBuyNft(false)
    router.push(ROUTES.home)
  }

  const handleCloseBuyNFTModal = () => {
    // setTransactionHash('')
    setOpenModalBuyNft(false)
    setTransferStatus('processing')
    router.push(ROUTES.myProfile)
  }

  useEffect(() => {
    if (paymentStatus && paymentStatus === checkoutStatus.CONFIRMING) {
      setOpenModalBuyNft(true)
      setCheckoutStatus(checkoutStatus.PROCESSING)
    }
    if (paymentStatus && paymentStatus === checkoutStatus.CANCEL) {
      setOpenModalBuyNft(true)
      setCheckoutStatus(checkoutStatus.CANCEL)
      mintCount &&
        cancelPaymentMutation({
          nftId: router.query?.nftId as string,
          mintCount: Number(mintCount || 0),
        })
    }
  }, [paymentStatus, mintCount])

  const messsageError = useMemo(() => {
    return mintErrorRetry ? trans.error_retry : ''
  }, [mintErrorRetry, trans])

  useEffect(() => {
    if (socket && socket.connected) {
      const handlePaymentSuccess = (isSuccess: any) => {
        console.log('Socket message')
        if (isSuccess) {
          setCheckoutStatus(checkoutStatus.SUCCESS)
          router.replace(
            router.pathname
              .replace('[collectionId]', router?.query?.collectionId as string)
              .replace('[nftId]', router?.query?.nftId as string),
            '',
            { shallow: true }
          )
          refetchDetail?.()
        }
      }
      const handleMintError = () => {
        setCheckoutStatus(checkoutStatus.FAIL)
        router.replace(
          router.pathname
            .replace('[collectionId]', router?.query?.collectionId as string)
            .replace('[nftId]', router?.query?.nftId as string),
          '',
          { shallow: true }
        )
        setMintErrorRetry(true)
      }

      socket.on(SOCKET_EVENTS.PAYMENT_SUCCESS, handlePaymentSuccess)
      socket.on(SOCKET_EVENTS.MINT_ERROR, handleMintError)

      return () => {
        socket.off(SOCKET_EVENTS.PAYMENT_SUCCESS, handlePaymentSuccess)
        socket.off(SOCKET_EVENTS.MINT_ERROR, handleMintError)
      }
    }
  }, [socket, router, checkoutStatus.SUCCESS])

  useEffect(() => {
    mintCount && setAmount(Number(mintCount))
  }, [mintCount])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-getCardBg text-white p-8 flex-col content-center justify-items-center gap-10">
        <Spinner />
        <p className="mt-4 text-neutral-400">{trans.loading_nfts}</p>
      </div>
    )
  }

  const [formattedPrice, ...currency] = formatWithCurrency(
    Number(nft?.price || 0)
  ).split(' ')
  const [formattedCheckoutPrice, ...checkoutCurrency] = formatWithCurrency(
    Number(nft?.price || 0) * amount
  ).split(' ')
  const priceFontSize = getFontSizeByNumberOfCharacters(formattedPrice, 9, 40)
  const basePriceFontSize = getFontSizeByNumberOfCharacters(
    formattedPrice,
    14,
    14
  )
  const checkoutFontSize = getFontSizeByNumberOfCharacters(
    formattedPrice,
    6,
    40
  )

  return (
    <>
      <div className="min-h-screen bg-getCardBg text-white p-[20px] flex flex-col md:flex-row gap-10 rounded-lg">
        {/* Left - Image */}
        <div className="md:w-1/2 flex justify-center max-h-[550px]">
          {nft?.isVideo ? (
            <VideoPreview
              src={nft.image}
              controls={true}
              className="rounded-lg object-cover aspect-square h-[550px] w-[550px]"
            />
          ) : (
            <Image
              src={nft?.image || IMAGE.defaultImage}
              alt={nft?.name}
              width={550}
              height={550}
              className="rounded-lg object-cover aspect-square"
            />
          )}
        </div>

        {/* Right - Info */}
        <div className="md:w-1/2 sm:space-y-[40px] space-y-[20px]">
          <div className="flex gap-4">
            {/* button toggle tabs*/}
            <TabButton
              onClick={() => setActiveTab('info')}
              title={trans.nft_info}
              isActive={activeTab === 'info'}
            />
            <TabButton
              onClick={() => setActiveTab('attributes')}
              title={trans.nft_attribute}
              isActive={activeTab === 'attributes'}
            />
          </div>

          {/* NFT Title - Always visible outside tabs */}
          <div className="flex items-center gap-2">
            <span className="font-montserrat font-semibold sm:text-[36px] text-[24px] leading-[120%] tracking-[0.02em] text-white text-wrap shrink">
              {nft?.name}
            </span>
            <StandardBadge standard={nft?.collection.standard} />
          </div>

          {/* Tab Content */}
          {activeTab === 'info' && (
            <>
              <div>
                <div className="sm:grid sm:grid-cols-2 justify-items-start font-montserrat sm:space-y-0 space-y-4">
                  <p className="font-normal text-[12px] leading-[170%] tracking-[0.02em] text-secondary sm:pb-[10px]">
                    {trans.nft_price}
                  </p>
                  <p className="font-normal text-[12px] leading-[170%] tracking-[0.02em] text-secondary sm:pb-[10px]">
                    {trans.nft_minted_quantity}
                  </p>
                </div>
                <div className="sm:grid sm:grid-cols-2 justify-items-start font-montserrat sm:space-y-0 space-y-4">
                  <div className="flex items-center flex-grow">
                    <p
                      className="font-normal leading-[140%] text-right tracking-[0.02em] text-white gap-2"
                      style={{ fontSize: `${priceFontSize}px` }}
                    >
                      {formattedPrice}
                      <span className="font-normal text-[14px] h-full align-middle ml-1">
                        {currency.join(' ')}
                      </span>
                    </p>
                  </div>
                  <p className="font-normal text-[40px] leading-[140%] flex items-center text-right tracking-[0.02em] text-white">
                    {nft?.mintedNumber} / {nft?.maximumNumber}
                  </p>
                </div>
              </div>
              <div className="font-montserrat">
                <p className="font-normal text-[12px] leading-[170%] tracking-[0.02em] text-secondary sm:pb-[10px]">
                  {trans.nft_released_date}:
                </p>
                <p className="font-montserrat font-medium text-[14px] leading-[170%] tracking-[0.02em] text-white uppercase">
                  {formatTimeToUTC(nft?.saleStartAt)} (UTC)
                </p>
              </div>

              {/* Sale Status */}
              <div className="text-sm text-gray-400">
                <p className="font-normal text-[12px] leading-[170%] tracking-[0.02em] text-secondary sm:pb-[10px]">
                  {trans.nft_sale_status}:
                </p>
                <p
                  className={clsx('text-sm font-semibold', {
                    'text-green-400': isNFTOpenForSale(),
                    'text-red-400': !isNFTOpenForSale(),
                  })}
                >
                  {isNftSoldOut
                    ? trans.nft_sold_out
                    : isNFTOpenForSale()
                    ? trans.nft_open_for_sale
                    : trans.nft_unreleased}
                </p>
                {!isNFTOpenForSale() && (
                  <p className="text-xs text-gray-500 mt-1 uppercase">
                    {trans.nft_sale_period} (UTC):{' '}
                    {formatTimeToUTC(nft?.saleStartAt)} -{' '}
                    {formatTimeToUTC(nft?.saleEndAt)}
                  </p>
                )}
              </div>

              {/* Amount selector & Buy */}
              <div className="flex items-end gap-4">
                <InputNumber
                  value={amount}
                  onChange={handleAmountChange}
                  min={1}
                  max={isERC721 ? 1 : nft?.maximumNumber - nft?.mintedNumber}
                  step={1}
                  size="lg"
                  variant="default"
                  className="border-gray-500"
                  disabled={isERC721}
                  label={trans.nft_amount}
                  labelClassName="font-normal text-[12px] leading-[170%] tracking-[0.02em] text-secondary mb-[10px]"
                />

                <UiButton
                  title={
                    isNftSoldOut
                      ? trans.nft_sold_out
                      : isNFTOpenForSale()
                      ? trans.nft_buy
                      : trans.nft_unreleased
                  }
                  className={clsx('flex flex-1', {
                    'opacity-50': !isNFTOpenForSale(),
                    'h-[2.75rem]': amount > 0,
                  })}
                  handleClick={buyNftByCard}
                  isDisabled={!isNFTOpenForSale()}
                  isFilled={true}
                />
              </div>
            </>
          )}

          {/* Attributes Tab */}
          {activeTab === 'attributes' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-400">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {trans.nft_traits}
                </h3>
                {nft?.attributes && nft.attributes.length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {nft.attributes.map((attribute, index) => (
                      <div
                        key={index}
                        className="bg-white bg-opacity-5 border border-gray-600 rounded-lg p-4 relative"
                      >
                        <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                          {attribute.traitType}
                        </div>
                        <div className="text-sm font-semibold text-white">
                          {attribute.value}
                        </div>
                        {/* Small triangle indicator in bottom right */}
                        <div className="absolute bottom-2 right-2 w-0 h-0 border-l-[6px] border-l-transparent border-b-[6px] border-b-gray-500"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-400">
                    <p>{trans.nft_no_attributes_available}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Introduction - Always visible outside tabs */}
          <div className="pt-[40px] border-t border-default">
            <h2 className="font-semibold text-lg mb-2">
              {trans.nft_introduction}
            </h2>
            <pre className="font-montserrat font-medium text-[14px] leading-[170%] tracking-[0.02em] text-white mb-2 text-wrap">
              {nft?.description || trans.nft_no_description}
            </pre>
            {/* Tags */}
            {nft?.tags && nft?.tags?.length > 0 && (
              <div className="my-6">
                <h3 className="font-semibold text-base mb-2">
                  {trans.nft_tag}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {nft.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 rounded-full text-sm font-medium bg-gray-800 text-white border border-gray-600"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
            {/* External Link */}
            {nft?.externalLink && (
              <div className="mt-4">
                <h3 className="font-semibold text-base mb-2 hover:underline underline-offset-4">
                  <Link href={nft.externalLink} target="_blank">
                    {trans.external_link} <LinkOutlined rev={'right'} />
                  </Link>
                </h3>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Buy NFT Modal - Payment Confirmation or Result */}
      <UiModal
        isOpen={openModalBuyNft}
        onClose={() => {
          setOpenModalBuyNft(false)
          setCheckoutStatus(checkoutStatus.IDLE)
          setMintErrorRetry(false)
        }}
        header={getHeaderModal(status)}
        cancelButton={
          loading || status !== checkoutStatus.IDLE
            ? { isShow: false, title: '', action: undefined }
            : {
                isShow: true,
                title: trans.cancel,
                action: () => {
                  setOpenModalBuyNft(false)
                  setCheckoutStatus(checkoutStatus.IDLE)
                  setMintErrorRetry(false)
                },
              }
        }
        confirmButton={
          loading || status !== checkoutStatus.IDLE
            ? { isShow: false, title: '', action: undefined }
            : {
                isShow: true,
                title: trans.checkout,
                action:
                  transferStatus === 'success'
                    ? handleCloseBuyNFTModal
                    : transferStatus === 'fail'
                    ? onBuyNftError
                    : checkout,
              }
        }
      >
        {status === checkoutStatus.SUCCESS && (
          <UiStatusContent status={PAYMENT_STATUS.SUCCESS} />
        )}

        {status === checkoutStatus.CANCEL && (
          <UiStatusContent
            status={PAYMENT_STATUS.CANCEL}
            errorMessage={trans.transaction_cancel}
          />
        )}
        {status === checkoutStatus.FAIL && (
          <UiStatusContent
            status={PAYMENT_STATUS.FAILED}
            errorMessage={messsageError || trans.an_error_occurred}
          />
        )}
        {(status === checkoutStatus.SUCCESS ||
          status === checkoutStatus.FAIL) && (
          <UiButton
            title={trans.close}
            size={BUTTON_SIZES.SM}
            isGradient={false}
            isBorder={true}
            className="mt-12 uppercase text-sm font-bold tracking-wider shadow focus:outline-none sm:h-[44px] sm:w-[260px]"
            handleClick={() => {
              setOpenModalBuyNft(false)
              setCheckoutStatus(checkoutStatus.IDLE)
            }}
          />
        )}
        {/* Payment Confirmation Content (only if not success/fail) */}
        {(status === checkoutStatus.IDLE ||
          status === checkoutStatus.PROCESSING) && (
          <div className="w-full">
            <div className="flex flex-col md:flex-row gap-[20px] w-full">
              {/* Left Panel: NFT Image */}
              <div className="flex justify-center items-center">
                {nft?.isVideo ? (
                  <VideoPreview
                    src={nft.image}
                    className="object-cover rounded-md bg-neutral-800 w-[260px] h-[260px]"
                  />
                ) : (
                  <Image
                    src={nft?.image || IMAGE.defaultImage}
                    alt="nft image"
                    width={260}
                    height={260}
                    className="object-cover rounded-md bg-neutral-800 min-w-[260px] min-h-[260px]"
                  />
                )}
              </div>
              {/* Right Panel: Transaction Summary */}
              <div className="flex-1 flex-col content-between grid font-montserrat font-normal text-[14px] leading-[170%] tracking-[0.02em] text-white">
                <div>
                  <div className="flex justify-between py-1">
                    <span>{trans.base_price}</span>
                    <span style={{ fontSize: `${basePriceFontSize}px` }}>
                      {formatWithCurrency(Number(nft?.price || 0))}
                    </span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>{trans.transaction_fee}</span>
                    <span>{formatWithCurrency(0)}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>{trans.num_of_nfts}</span>
                    <span>{amount}</span>
                  </div>
                </div>
                <div className="flex justify-between py-2 border-t border-default mt-2">
                  <span>{trans.total}</span>
                  <p
                    className="font-montserrat font-normal leading-[140%] tracking-[0.02em] text-white flex items-center gap-2"
                    style={{ fontSize: `${checkoutFontSize}px` }}
                  >
                    {formattedCheckoutPrice}
                    <span className="font-montserrat font-normal text-[14px] leading-[140%] tracking-[0.02em] text-white">
                      {checkoutCurrency.join(' ')}
                    </span>
                  </p>
                </div>
              </div>
            </div>
            {/* Status and Transaction Hash */}

            {status === checkoutStatus.PROCESSING && (
              <div className="mt-8">
                <div>
                  <p className="text-red-500 text-sm mt-2 font-bold text-center">
                    {trans.please_do_not_refresh_browser}
                  </p>
                  <div className="flex justify-center items-center py-4">
                    <Spinner className="w-8 h-8" />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </UiModal>
    </>
  )
}
