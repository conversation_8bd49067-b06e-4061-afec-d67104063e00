import { useEffect, useState } from 'react'
import type { AppProps } from 'next/app'
import { Inter } from 'next/font/google'
import { WagmiConfig } from 'wagmi'
import { ToastContainer } from 'react-toastify'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { wagmiClient } from '@/helpers'
import { getRoleParsed } from '@/utils'
import { ROUTES } from '@/constants'
import { useStore } from '@/store'
import { getCookie, STORAGEKEY } from '@/services/cookies'

import '@/styles/main.scss'
import 'react-toastify/dist/ReactToastify.css'
import RootModal from '@/components/rootModal'
import { AdminLayout, NextHead, UserLayout } from '@/components/layouts'
import OverlayLoading from '@/components/common/overlayLoading'
import { ConfigProvider, Empty } from 'antd'
import { customTheme } from '@/constants/theme'

const inter = Inter({ subsets: ['latin'] })
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 2,
    },
  },
})
const LIMIT_TOAST_SHOW = 2

export default function App({ Component, pageProps, router }: AppProps) {
  const [mounted, setMounted] = useState(false)
  const [isRouterLoading, setIsRouterLoading] = useState(false)
  const isLoading = useStore((state) => state.isLoading)
  const { setRole } = useStore()

  const handleGetRole = () => {
    const accessToken = router.asPath.startsWith(ROUTES.admin)
      ? getCookie(STORAGEKEY.ADMIN_ACCESS_TOKEN)
      : getCookie(STORAGEKEY.USER_ACCESS_TOKEN)
    setRole(getRoleParsed(accessToken))
  }

  useEffect(() => {
    const handleStart = () => setIsRouterLoading(true)
    const handleComplete = () => setIsRouterLoading(false)
    window.addEventListener('beforeunload', () => {
      queryClient.clear()
    })

    router.events.on('routeChangeStart', handleStart)
    router.events.on('routeChangeComplete', handleComplete)
    router.events.on('routeChangeError', handleComplete)

    return () => {
      router.events.off('routeChangeStart', handleStart)
      router.events.off('routeChangeComplete', handleComplete)
      router.events.off('routeChangeError', handleComplete)
    }
  })

  const getLayout = (page: React.ReactElement) => {
    return router.pathname.startsWith(ROUTES.adminLogin) ? (
      <>{page}</>
    ) : router.pathname.startsWith(ROUTES.admin) ? (
      <AdminLayout>{page}</AdminLayout>
    ) : (
      <UserLayout>{page}</UserLayout>
    )
  }

  useEffect(() => {
    setMounted(true)
    // eslint-disable-next-line
  }, [])

  useEffect(() => {
    handleGetRole()
    // eslint-disable-next-line
  }, [router.asPath])

  return (
    <ConfigProvider
      renderEmpty={() => <Empty description="データが見つかりません。" />}
      theme={customTheme}
    >
      <WagmiConfig config={wagmiClient}>
        <QueryClientProvider client={queryClient}>
          <NextHead />
          {(isRouterLoading || isLoading) && <OverlayLoading />}
          <style jsx global>{`
            html {
              font-family: ${inter.style.fontFamily};
            }
          `}</style>

          {mounted && getLayout(<Component {...pageProps} />)}

          <RootModal />
          <ToastContainer
            className="!top-[var(--height-header)] max-sm:!w-[90%] max-sm:!m-auto"
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            limit={LIMIT_TOAST_SHOW}
          />
        </QueryClientProvider>
      </WagmiConfig>
    </ConfigProvider>
  )
}
