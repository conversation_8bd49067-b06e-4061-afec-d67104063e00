import { useStore } from '@/store'
import { useTranslate } from './useTranslate'
import { META_MASK_ERROR_NAMES } from '@/constants'
import { MODAL_TYPES } from '@/components/rootModal'

type ShowMetaMaskErrorProps = {
  errorName: string
  defaultMessage?: string
}

export const useMetaMaskError = () => {
  const trans = useTranslate()
  const { setModalType } = useStore()

  const showMetaMaskError = (props: ShowMetaMaskErrorProps) => {
    const { errorName, defaultMessage } = props
    let message = ''
    switch (errorName) {
      case META_MASK_ERROR_NAMES.connectorNotFoundError:
        message = 'コネクターが見つかりませんでした。'
        break
      case META_MASK_ERROR_NAMES.userRejectedRequestError:
        message = 'リクエストが拒否されました。'
        break
      case META_MASK_ERROR_NAMES.default:
        message = 'エラーが発生しました。'
        break

      default:
        message = defaultMessage ?? trans['エラーが発生しました。']
        break
    }
    setModalType({
      type: MODAL_TYPES.commonError,
      message,
    })
  }

  return { showMetaMaskError }
}
