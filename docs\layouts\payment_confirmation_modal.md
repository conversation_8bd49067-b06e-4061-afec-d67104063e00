# Payment Confirmation Modal

This component is a **Payment Confirmation Modal** used in NFT purchasing flows. It presents key transaction details to the user before proceeding with payment.

## 🧩 Features

- Displays NFT preview image
- Shows transaction breakdown:
  - Base price
  - Transaction fee
  - Creator fee
  - Number of NFTs
  - Total amount
- Two action buttons:
  - **Cancel** to abort the transaction
  - **Proceed to payment** to confirm and initiate the payment

## 📸 Layout Overview

The modal consists of two main sections:

1. **Left Panel**

   - NFT image preview (placeholder if no image is available)

2. **Right Panel**
   - Text box displaying the transaction summary

At the bottom, the modal includes two buttons:

- A **Cancel** button (left)
- A **Proceed to payment** button (right), highlighted to indicate the primary action

## 🧱 Technologies Used

- React (or similar frontend framework)
- Styled with CSS or TailwindCSS (depending on implementation)
- Optional integration with:
  - Web3 / blockchain SDKs
  - NFT marketplace APIs

## 🚀 Usage

This modal should be triggered when the user initiates a purchase flow. Values like price, fees, and quantity must be dynamically injected before rendering.

```tsx
<PaymentConfirmationModal
  price="100"
  transactionFee="2"
  creatorFee="3"
  numberOfNFTs={1}
  total="105"
  onCancel={handleCancel}
  onConfirm={handlePayment}
/>
```
