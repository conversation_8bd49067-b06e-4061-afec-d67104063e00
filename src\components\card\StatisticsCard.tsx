import React from 'react'

export const StatisticsCard = ({
  className,
  title,
  stat,
  icon,
  flexStart,
}: {
  className?: string
  title: string
  stat: number | string | React.ReactNode
  icon?: React.ReactNode
  flexStart?: boolean
}) => (
  <div
    className={`flex flex-col bg-[var(--bg-card)] rounded-[8px] pt-4 pb-5 px-2 text-white ${
      className || ''
    }`}
  >
    <div
      className={`flex items-center ${
        flexStart ? 'flex-start' : 'justify-center'
      } gap-2 mb-1`}
    >
      {icon && <span className="text-[18px]">{icon}</span>}
      <p className="text-[18px] tracking-[0.02em] text-nowrap">{title}</p>
    </div>
    <p className="flex flex-1 items-center justify-center text-[2.5rem] tracking-[0.02em]">
      {stat}
    </p>
  </div>
)
