import React from 'react'

type IconProps = {
  size?: number | string
  color?: string
  className?: string
}

const IconDelete: React.FC<IconProps> = ({
  size = 18,
  color = '#F2C157',
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 12 15"
      fill="none"
      className={className}
    >
      <path
        d="M1.5 4.25V12.5C1.5 13.3284 2.17157 14 3 14H9C9.82843 14 10.5 13.3284 10.5 12.5V4.25M1.5 4.25H0.75M1.5 4.25H3M10.5 4.25H11.25M10.5 4.25H9M4.5 7.25V11M7.5 7.25V11M3 4.25V2.75C3 1.92157 3.67157 1.25 4.5 1.25H7.5C8.32843 1.25 9 1.92157 9 2.75V4.25M3 4.25H9"
        stroke={color}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default React.memo(IconDelete)
