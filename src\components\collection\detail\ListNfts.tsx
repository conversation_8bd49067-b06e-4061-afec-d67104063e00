import Spinner from '@/components/ui/Spinner'
import { NftCard } from '@/components/card'
import { NftDetail } from '@/interfaces'
import { useTranslate } from '@/hooks/useTranslate'
import { COLLECTION_DETAIL_CONFIG } from '@/constants/collection-detail'
import { UiPagination } from '@/components/ui/Pagination'

type Props = {
  nfts: NftDetail[]
  total?: number
  pageSize?: number
  current?: number
  onPageChange?: (page: number) => void
  loading: boolean
  collectionId: string
  isManagement?: boolean
}

export default function ListNfts({
  nfts,
  total,
  pageSize,
  current,
  onPageChange,
  loading,
  collectionId,
  isManagement,
}: Props) {
  const t = useTranslate()
  return (
    <div className="max-w-screen-xl mx-auto font-montserrat">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4">
        {loading ? (
          <div className="col-span-full flex justify-center">
            <div className="text-center">
              <Spinner />
              <p className="mt-4 text-secondary">
                {t[COLLECTION_DETAIL_CONFIG.loadingNfts]}
              </p>
            </div>
          </div>
        ) : nfts.length > 0 ? (
          nfts.map((nft) => (
            <div key={nft._id}>
              <NftCard
                name={nft.name}
                {...(nft.isVideo
                  ? { videoUrl: nft.image }
                  : { imageUrl: nft.image })}
                href={
                  isManagement
                    ? `/admin/nfts/detail/${nft._id}`
                    : `/collections/${collectionId}/${nft._id}`
                }
                price={nft?.price.toString() || '0'}
              />
            </div>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <div className="text-secondary text-lg mb-4">
              {t[COLLECTION_DETAIL_CONFIG.noNftsFound]}
            </div>
            <p className="text-secondary">
              {t[COLLECTION_DETAIL_CONFIG.noNftsSubtext]}
            </p>
          </div>
        )}
      </div>
      {total && (
        <div className="flex justify-center mt-6">
          <UiPagination
            current={current}
            pageSize={pageSize}
            total={total}
            onChange={onPageChange}
            locale={{
              items_per_page: '',
              jump_to:
                t[COLLECTION_DETAIL_CONFIG.paginationJumpTo] || 'Jump to',
              jump_to_confirm:
                t[COLLECTION_DETAIL_CONFIG.paginationGoTo] || 'Go to',
              page: t[COLLECTION_DETAIL_CONFIG.paginationPage] || 'Page',
            }}
          />
        </div>
      )}
    </div>
  )
}
