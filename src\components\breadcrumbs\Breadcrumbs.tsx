import React from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Breadcrumb } from 'antd'

// Accept dynamicNames for dynamic segments (e.g., collection name)
type BreadcrumbsProps = {
  dynamicNames?: string[]
  className?: string
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  dynamicNames = [],
  className = '',
}) => {
  const pathname = usePathname()

  if (!pathname || typeof pathname !== 'string') return null
  const pathSegments = pathname.split('/').filter(Boolean)

  let dynamicIndex = 0
  const items = [
    ...pathSegments.map((segment, idx) => {
      const href = '/' + pathSegments.slice(0, idx + 1).join('/')
      let label: React.ReactNode = segment
      // Heuristic: treat any segment that looks like an id as dynamic
      if (/^[0-9a-fA-F]{16,}$/.test(segment) && dynamicNames[dynamicIndex]) {
        label = dynamicNames[dynamicIndex]
        dynamicIndex++
      } else {
        // Capitalize for static segments
        label = segment.charAt(0).toUpperCase() + segment.slice(1)
      }

      // All segments should be clickable links
      return {
        title: <Link href={href}>{label}</Link>,
      }
    }),
  ]

  return (
    <Breadcrumb
      items={items}
      style={{ color: 'white' }}
      className={className}
    />
  )
}

export default Breadcrumbs
