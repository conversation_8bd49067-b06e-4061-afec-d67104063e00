import { CommonErrorModal, ConnectorNotFoundErrorModal } from './modals'

import { useStore } from '@/store'

export const MODAL_TYPES = {
  commonError: 'common-error',
  connectorNotFound: 'connector-not-found',
} as const

type ModalData = {
  [key: string]: React.ReactElement
}

const RootModal: React.FC = () => {
  const { modalType } = useStore()

  const modalList: ModalData = {
    [MODAL_TYPES.commonError]: <CommonErrorModal />,
    [MODAL_TYPES.connectorNotFound]: <ConnectorNotFoundErrorModal />,
  }

  return modalType ? modalList[modalType] : <></>
}

export default RootModal

export const CLOSE_MODAL = { type: null }
