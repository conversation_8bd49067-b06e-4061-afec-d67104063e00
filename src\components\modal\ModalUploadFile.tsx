import { Modal, Upload, Input, Progress } from 'antd'
import Link from 'next/link'
import clsx from 'clsx'
import { Editor as TinyMCEEditor } from 'tinymce'
import { useState } from 'react'
import { toast } from 'react-toastify'

import ButtonComponent from '../buttons/Button'
import { IconUpload } from '@/icons'
import {
  MESSAGE_FORM,
  TYPES_FILE_ALLOWED,
  TYPES_IMAGE_ALLOWED,
  TYPES_VIDEO_ALLOWED,
} from '@/utils/string'
import TextError from '../texts/TextError'
import { LENGTH_BIT_IMAGE, MAX_SIZE_FILE } from '@/constants'

const { Dragger } = Upload

interface ModalProps {
  isModalOpen: boolean
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  editorRef: {
    current: TinyMCEEditor | null
  }
  okText?: string
  cancelText?: string
  containerClass?: string
  contentClass?: string
  buttonContainerClass?: string
  funcOk?: () => void | false
  funcUpload: (formData: FormData, configParams?: object) => Promise<string>
}
export const customRequestUpload = (options) => {
  setTimeout(() => {
    options.onSuccess('ok')
  }, 0)
}
function ModalUploadFile(props: ModalProps) {
  const {
    isModalOpen,
    setModalOpen,
    okText = 'アップロード',
    cancelText = 'キャンセル',
    containerClass,
    contentClass,
    buttonContainerClass,
    funcOk = false,
    editorRef,
    funcUpload,
    ...rest
  } = props
  const handleCancel = () => {
    setModalOpen(false)
  }
  const handleOk = () => {
    if (funcOk) {
      funcOk()
    } else {
      setModalOpen(!isModalOpen)
    }
  }

  const [linkUpload, setLinkUpload] = useState<string>('')
  const [urlTitle, setUrlTitle] = useState<string>('')
  const [isUploadFile, setIsUploadFile] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number>(0)

  const onUploadProgress = (event) => {
    // convert to percent
    const percentCompleted = Math.round((event.loaded * 100) / event.total)
    setUploadProgress(percentCompleted)
  }

  const clearData = () => {
    setUrlTitle('')
    setLinkUpload('')
    setUploadProgress(0)
  }

  const beforeUploadFile = (file) => {
    if (
      !TYPES_VIDEO_ALLOWED.includes(file.type) &&
      !TYPES_FILE_ALLOWED.includes(file.type) &&
      !TYPES_IMAGE_ALLOWED.includes(file.type)
    ) {
      toast.error(MESSAGE_FORM.formatIsIncorrect)
      return false
    }
    if (file.size / LENGTH_BIT_IMAGE > MAX_SIZE_FILE) {
      toast.error(MESSAGE_FORM.upload10MB)
      return false
    }
    return true
  }

  const onUploadImage = async (info) => {
    clearData()
    if (info.file.status === 'done') {
      setIsUploadFile(true)
      try {
        const fileUpload = info.file.originFileObj

        const formData = new FormData()
        formData.append('file', fileUpload)
        const fileUrl = await funcUpload(formData, {
          onUploadProgress,
        })

        //set default
        setIsUploadFile(false)
        setModalOpen(true)
        setUploadProgress(0)
        //update data
        setLinkUpload(fileUrl)
        setUrlTitle(info?.file?.name)
      } catch (error) {
        setIsUploadFile(false)
      }
    }
  }

  const onInertLink = async () => {
    if (editorRef.current) {
      editorRef?.current.insertContent(
        `<a href="${linkUpload}" target="_blank" >${urlTitle}</a>`
      )
    }
    clearData()
    setModalOpen(false)
  }

  return (
    <div>
      <Modal
        closeIcon={null}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={okText}
        cancelText={cancelText}
        centered
        footer={null}
        width={600}
        {...rest}
      >
        <div
          className={clsx(
            'flex items-center justify-center flex-col gap-4 p-10',
            containerClass
          )}
        >
          <div className={clsx('flex flex-col w-full gap-8', contentClass)}>
            <h2 className="text-center font-bold text-2xl">
              ファイルアップロード
            </h2>

            <div className="">
              <Dragger
                name="file"
                listType="picture"
                showUploadList={false}
                multiple={false}
                maxCount={1}
                className="group"
                onChange={(info) => onUploadImage(info)}
                beforeUpload={beforeUploadFile}
                disabled={isUploadFile}
                customRequest={customRequestUpload}
              >
                <div className="flex flex-col h-[100px] items-center justify-center">
                  {isUploadFile ? (
                    <Progress
                      type="circle"
                      percent={uploadProgress}
                      size="small"
                    />
                  ) : (
                    <div className="flex items-center justify-center flex-col">
                      <IconUpload />
                      <p className="font-normal text-base text-tailwindNeutral3">
                        クリック、又はドラッグしてください。
                      </p>
                    </div>
                  )}
                </div>
              </Dragger>
              <Link href={linkUpload} target="_blank">
                <div className="text-blue-500 truncate mt-1">{linkUpload}</div>
              </Link>
            </div>
            <div className="">
              <div className=" text-base font-bold mb-2">タイトル</div>
              <Input
                disabled={isUploadFile}
                maxLength={255}
                showCount
                defaultValue=""
                value={urlTitle}
                onChange={(event) => setUrlTitle(event.target.value)}
                required={true}
              />
              <div className="min-h-[1.25rem] ">
                {!urlTitle && linkUpload && (
                  <TextError title={MESSAGE_FORM.titleIsRequire} />
                )}
              </div>
            </div>
          </div>
          <div className={clsx('flex gap-8 px-4 w-full', buttonContainerClass)}>
            <ButtonComponent
              title={okText}
              type="primary"
              className=" h-12 w-full  font-bold"
              onClick={onInertLink}
              disabled={isUploadFile || !urlTitle || !linkUpload}
            />
            <ButtonComponent
              type="secondary"
              title={cancelText}
              className="text-black !h-12 border-tailwindNeutral3 w-full  !font-bold"
              onClick={handleCancel}
            />
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default ModalUploadFile
