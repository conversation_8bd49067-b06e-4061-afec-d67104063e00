import React, { useState, useEffect } from 'react'
import { Form, Radio, Input, Select, Row, Col } from 'antd'
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES } from '@/constants/enum'
import Text from '@/components/texts/Text'
import UploadImage from '@/components/uploadImages/UploadImages'
import { useRouter } from 'next/router'
import { useGet } from '@/hooks/useGet'
import { usePatch } from '@/hooks/usePatch'
import {
  getCollection,
  approveCollection,
} from '@/services/apiCall/collections'
import { getListCategories } from '@/services/apiCall/categories'
import {
  ApproveCollectionPayload,
  ApproveStatus,
} from '@/interfaces/collection'
import { APPROVAL_STATUS, CONTRACT_TYPES } from '@/constants/common'
import { ApproverRole } from '@/services/apiCall/collections'
import { useStore } from '@/store'

const { TextArea } = Input

const TEXT_LABELS = {
  REJECT: '却下',
  APPROVE: '承認',
}

export default function ApproveCollectionForm() {
  const role = useStore((state) => state.role)
  const { canApprove } = useStore((state) => state)

  const [form] = Form.useForm()
  const router = useRouter()
  const { id } = router.query
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [bgFile, setBgFile] = useState<File | null>(null)
  const [logoImageUrl, setLogoImageUrl] = useState<string>('')
  const [bgImageUrl, setBgImageUrl] = useState<string>('')
  const { setLoading } = useStore()

  // Fetch collection data
  const { data: collection } = useGet({
    queryKey: ['collection', id],
    callback: () => {
      if (id) {
        return getCollection(id as string)
      }
      return Promise.resolve(null)
    },
  })

  // Fetch categories
  const { data: categoriesData, isLoading: loadingCategories } = useGet({
    queryKey: ['categories'],
    callback: () => getListCategories(),
  })
  const categories =
    categoriesData?.items?.map((cat: any) => ({
      value: cat._id,
      label: cat.name,
    })) || []

  // Patch hook for update
  const { mutate, isPending } = usePatch({
    queryKey: ['collection', id],
    callback: async ({
      id,
      payload,
    }: {
      id: string
      payload: ApproveCollectionPayload
    }) => {
      return approveCollection(id, payload, role as ApproverRole)
    },
  })

  // Populate form when collection is loaded
  useEffect(() => {
    if (collection) {
      form.setFieldsValue({
        contractType: collection?.standard,
        name: collection?.name,
        category: collection?.category,
        description: collection?.description,
      })
      setLogoImageUrl(collection?.logoImage)
      setBgImageUrl(collection?.backgroundImage)
    }
  }, [collection, form])

  // Form submit handler
  const handleSubmit = async (approvalStatus: ApproveStatus) => {
    setLoading(true)

    // Prepare payload
    const payload: ApproveCollectionPayload = {
      approvalStatus,
    }

    // Submit collection
    mutate(
      { id: id as string, payload },
      {
        onSuccess: () => {
          form.resetFields()
          setLogoFile(null)
          setBgFile(null)
          setLoading(false)
          router.back()
        },
        onError: () => {
          setLogoFile(null)
          setBgFile(null)
          setLoading(false)
        },
      }
    )
  }

  const onApprove = async () => {
    // Default behavior - update collection (not draft)
    await handleSubmit(ApproveStatus.APPROVED)
  }

  const onReject = async () => {
    // Save as draft
    await form.validateFields()
    await handleSubmit(ApproveStatus.REJECTED)
  }

  return (
    <Row className="justify-around">
      <Col xs={24} md={10}>
        <UploadImage
          width="w-[60%]"
          height="h-1/2"
          label="ロゴ画像"
          recommendedSize="350 x 350"
          required={true}
          value={logoFile}
          imageUrl={logoImageUrl}
          onChange={setLogoFile}
          maxSize={2}
          disabled={true}
        />
        <UploadImage
          height="h-1/2"
          label="背景画像"
          recommendedSize="1920 x 1080"
          required
          value={bgFile}
          imageUrl={bgImageUrl}
          onChange={setBgFile}
          maxSize={5}
          disabled={true}
        />
      </Col>
      <Col xs={24} md={12}>
        <Form
          disabled={true}
          requiredMark={false}
          form={form}
          layout="vertical"
          onFinish={onApprove}
          className="flex flex-row gap-8 w-full text-white"
        >
          <Row>{loadingCategories && <span>Loading categories...</span>}</Row>
          <div className="flex flex-col flex-1 gap-6">
            <div>
              <Form.Item
                name="contractType"
                initialValue={CONTRACT_TYPES[0]}
                rules={[
                  { required: true, message: 'Choose your contract type' },
                ]}
                className="mb-2"
                label={
                  <Text require={true}>コレクションの標準をお選びください</Text>
                }
              >
                <Radio.Group className="flex gap-8">
                  {CONTRACT_TYPES.map((type) => (
                    <Radio key={type} value={type}>
                      {type}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </div>
            <Form.Item
              name="name"
              label={<Text require={true}>コレクション名</Text>}
              rules={[
                { required: true, message: 'Contract name is required!' },
              ]}
              className="mb-2"
            >
              <Input size="large" placeholder="Name your NFT" required />
            </Form.Item>
            <Form.Item
              name="category"
              label={<Text require={true}>カテゴリー</Text>}
              rules={[{ required: true, message: 'Category is required!' }]}
              className="mb-2"
            >
              <Select
                options={categories}
                placeholder="Category"
                size="large"
                loading={loadingCategories}
                disabled={true}
              />
            </Form.Item>
            <Form.Item
              name="description"
              label={<Text require={true}>コレクション紹介</Text>}
              rules={[{ required: true, message: 'Description is required!' }]}
              className="mb-2"
            >
              <TextArea
                rows={4}
                placeholder="Description..."
                className="w-full border border-[#333] p-4"
                required
              />
            </Form.Item>
            {canApprove &&
              collection?.approvalStatus === APPROVAL_STATUS.waiting && (
                <div className="flex flex-col gap-3 mt-6">
                  <UiButton
                    title={TEXT_LABELS.REJECT}
                    size={BUTTON_SIZES.LG}
                    isGradient={false}
                    handleClick={onReject}
                    className="w-full border border-[#fff] !bg-transparent text-white"
                    isDisabled={isPending}
                  />
                  <UiButton
                    title={TEXT_LABELS.APPROVE}
                    size={BUTTON_SIZES.LG}
                    isGradient={true}
                    handleClick={form.submit}
                    className="w-full"
                    isLoading={isPending}
                    isDisabled={isPending}
                  />
                </div>
              )}
          </div>
        </Form>
      </Col>
    </Row>
  )
}
