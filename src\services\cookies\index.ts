import moment from 'moment'
import { Cookies } from 'react-cookie'

const cookies = new Cookies()

export const STORAGEKEY = {
  ADMIN_ACCESS_TOKEN: 'admin_access_token',
  USER_ACCESS_TOKEN: 'user_access_token',
  WALLET_ADDRESS: 'wallet_address',
  USER: 'user',
  ADMIN: 'admin',
  CONNECT_SESSION: 'connect_session',
  CONNECT_CLIENT: 'connect_client',
  CONNECT_TOPIC: 'connect_topic',
}

export const setCookie = (key: string, value: string, expires?: string) =>
  cookies.set(key, value, {
    expires: expires
      ? new Date(moment(expires).format('YYYY-MM-DD HH:mm'))
      : undefined,
    path: '/',
  })

export const getCookie = (key: string) => cookies.get(key)

export const removeCookie = (key: string) => cookies.remove(key, { path: '/' })
