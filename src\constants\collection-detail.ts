export const COLLECTION_DETAIL_CONFIG = {
  // Page
  pageTitle: 'page_title',
  breadcrumbCollections: 'breadcrumb_collections',

  // Search & Filter
  searchPlaceholder: 'search_placeholder',
  searchButton: 'search_button',

  // Content
  collectionNotFound: 'collection_not_found',
  loadingCollection: 'loading_collection',
  loadingNfts: 'loading_nfts',
  noNftsFound: 'no_nfts_found',
  noNftsSubtext: 'no_nfts_subtext',

  // Statistics
  floorPrice: 'floor_price',
  totalVolume: 'total_volume',
  totalSupply: 'total_supply',
  owners: 'owners',
  items: 'items',

  // Actions
  viewNft: 'view_nft',
  mintNft: 'mint_nft',
  backToCollections: 'back_to_collections',

  // Collection info
  collectionStandard: 'collection_standard',
  collectionDescription: 'collection_description',

  // Pagination
  paginationPrevious: 'pagination_previous',
  paginationNext: 'pagination_next',
  paginationPage: 'pagination_page',
  paginationOf: 'pagination_of',
  paginationGoTo: 'pagination_go_to',
  paginationJumpTo: 'pagination_jump_to',
} as const
