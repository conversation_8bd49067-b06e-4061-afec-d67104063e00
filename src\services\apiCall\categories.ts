import { API_URLS, ROLE, DEFAULT_CONFIG_PARAMS } from '../../constants'
import { Category, CategoryPaginated } from '../../interfaces'
import { get, post, patch, del } from './baseApi'

export const getListCategories = (
  query?: object
): Promise<CategoryPaginated> => {
  return get(API_URLS.adminCategories, query, ROLE.systemAdmin)
}

export const getCategoryDetail = (id: string): Promise<Category> => {
  return get(`${API_URLS.adminCategories}/${id}`, {}, ROLE.systemAdmin)
}

export const createCategory = (data: Category): Promise<Category> => {
  return post(
    API_URLS.adminCategories,
    data,
    DEFAULT_CONFIG_PARAMS,
    ROLE.systemAdmin
  )
}

export const updateCategory = (
  id: string,
  data: Category
): Promise<Category> => {
  return patch(`${API_URLS.adminCategories}/${id}`, data, ROLE.systemAdmin)
}

export const deleteCategory = (ids: string | string[]): Promise<void> => {
  return del(API_URLS.adminCategories, { ids }, ROLE.systemAdmin)
}
