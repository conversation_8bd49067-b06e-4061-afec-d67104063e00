import { useEffect, useState } from 'react'
export const useViewport = () => {
  const [windowWidth, setWindowWidth] = useState<number>(window.innerWidth || 0)
  const [windowHeight, setWindowHeight] = useState<number>(
    window.innerHeight || 0
  )

  const hasWindow = typeof window !== 'undefined'
  useEffect(() => {
    const handleWindowResize = () => {
      setWindowWidth(window.innerWidth)
      setWindowHeight(window.innerHeight)
    }
    window.addEventListener('resize', handleWindowResize)
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [hasWindow])

  return { windowWidth, windowHeight }
}
