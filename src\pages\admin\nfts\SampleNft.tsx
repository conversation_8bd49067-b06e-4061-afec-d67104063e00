import Image from 'next/image'
import { Col, Row } from 'antd'

import { TYPE_NFT } from '@/constants/type'
import { ENFT_TYPE, renderTypeNft } from '@/constants'
import { IMAGE } from '@/utils/string'
import clsx from 'clsx'

interface NFTComponentProps {
  src?: string
  nameNft: string
  typeNft: TYPE_NFT
  token?: number
  creatorName: string
}
export default function SampleNft(props: NFTComponentProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow  ease-linear duration-200 max-w-[300px] max-lg:max-w-[265px] max-lg:max-h-[320px] overflow-hidden cursor-pointer">
      <div className=" lg:h-[300px] h-[140px] mb-4 relative">
        <Image
          width={300}
          height={300}
          className="h-full w-full object-contain"
          src={props.src || IMAGE.defaultImage}
          alt="nft image"
        />
      </div>
      <div className="px-3 sm:pb-5 pb-2">
        <Row align="middle" justify="space-between" className="sm:mb-5">
          <Col>
            <h5
              className={clsx(
                'sm:text-base font-semibold tracking-tight text-tailwindNeutral6 sm:text-tailwindNeutral1 break-words line-clamp-1',
                {
                  'max-sm:max-w-[70px] max-w-[120px]':
                    props.typeNft === ENFT_TYPE.ふるさと納税版 ||
                    'max-sm:max-w-[70px] max-w-[180px]',
                }
              )}
            >
              {props.nameNft}
            </h5>
          </Col>
          <Col>
            <div className="text-sm py-1 px-1 bg-tailwindBrand2 text-tailwindNeutral2 rounded-md text-center max-sm:text-[10px]">
              {renderTypeNft[props.typeNft]}
            </div>
          </Col>
        </Row>
        <Row justify={'space-between'} align={'middle'}>
          <div className="gap-2 items-center sm:flex">
            <div className="max-w-[170px] break-words line-clamp-1 text-sm">
              {props.creatorName}
            </div>
          </div>
        </Row>
      </div>
    </div>
  )
}
