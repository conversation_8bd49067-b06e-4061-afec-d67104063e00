import NewsForm from '../NewsForm'
import React, { useState } from 'react'
import { Form } from 'antd'
import { usePost } from '@/hooks/usePost'
import { createNews } from '@/services/apiCall/news'
import { ROLE, API_URLS } from '@/constants'
import { toast } from 'react-toastify'
import { useStore } from '@/store'
import { post } from '@/services/apiCall/baseApi'
import { TNewsPayload } from '@/interfaces'
import { NewStatus } from '@/interfaces'
import { useMutation } from '@tanstack/react-query'
import Router from 'next/router'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'

const TEXT = {
  PLEASE_UPLOAD_THUMBNAIL: 'サムネイル画像をアップロードしてください',
  FAILED_TO_UPLOAD_THUMBNAIL: 'サムネイル画像のアップロードに失敗しました。',
}

export default function AddNewsPage() {
  const role = useStore((state) => state.role)
  const { canCreate } = useStore((state) => state)
  const [form] = Form.useForm()
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [editorValue, setEditorValue] = useState('')

  const { mutate } = usePost({
    queryKey: ['createNews'],
    callback: (params: TNewsPayload) =>
      createNews(params, role as ROLE.systemAdmin | ROLE.operator),
  })

  // React Query mutation for image upload
  const { mutateAsync: uploadImageMutation, isPending: isUploadingImage } =
    useMutation({
      mutationFn: async (file: File) => {
        const formData = new FormData()
        formData.append('file', file)
        const res = await post(
          API_URLS.adminNewsImage,
          formData,
          {
            headers: { 'Content-Type': 'multipart/form-data' },
          },
          role as ROLE.systemAdmin | ROLE.operator
        )
        return res
      },
      onError: () => {
        toast.error(TEXT.FAILED_TO_UPLOAD_THUMBNAIL)
      },
    })

  const handleThumbnailImage = async (
    logoFile: File | null
  ): Promise<string | null> => {
    if (logoFile) {
      try {
        const uploadedId = await uploadImageMutation(logoFile)
        if (!uploadedId) {
          toast.error(TEXT.FAILED_TO_UPLOAD_THUMBNAIL)
          setImageFile(null)
          return null
        }
        return uploadedId
      } catch {
        setImageFile(null)
        return null
      }
    }
    toast.error(TEXT.PLEASE_UPLOAD_THUMBNAIL)
    return null
  }

  const handleSubmit = async (values: any, isDraft: boolean) => {
    setIsLoading(true)

    // If image is required but not provided, show error and do not proceed
    if (!imageFile) {
      toast.error(TEXT.PLEASE_UPLOAD_THUMBNAIL)
      setIsLoading(false)
      return
    }

    let imageId = ''
    imageId = (await handleThumbnailImage(imageFile)) || ''

    // If image upload failed, do not send payload
    if (!imageId) {
      setIsLoading(false)
      return
    }

    const payload = {
      ...values,
      imageId,
      context: editorValue,
    }

    if (isDraft) {
      payload.approvalStatus = NewStatus.DRAFT
    }

    mutate(payload, {
      onSuccess: () => {
        form.resetFields()
        setImageFile(null)
        setEditorValue('')
        setIsLoading(false)
        Router.back()
      },
      onError: () => {
        setImageFile(null)
        setIsLoading(false)
      },
    })
  }

  const onFinish = async (values: any) => {
    await handleSubmit(values, false)
  }

  const onSaveDraft = async () => {
    const values = await form.getFieldsValue()
    await handleSubmit(values, true)
  }

  return (
    <>
      <TextAdminHeader title="ニュース作成" />
      <div className="pb-12">
        <NewsForm
          form={form}
          loading={isLoading || isUploadingImage}
          imageFile={imageFile}
          setImageFile={setImageFile}
          editorValue={editorValue}
          setEditorValue={setEditorValue}
          canCreate={canCreate}
          onFinish={onFinish}
          onSaveDraft={onSaveDraft}
        />
      </div>
    </>
  )
}
