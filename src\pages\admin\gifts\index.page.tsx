import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { toast } from 'react-toastify'

import HeaderSearch from './HeaderSearch'
import GiftTableList, { GiftTableListProps } from './GiftTableList'
import { get } from '@/services/apiCall/baseApi'
import { API_URLS, ROLE } from '@/constants'
import { MESSAGE_FORM } from '@/utils/string'

const GiftPage = () => {
  const { query } = useRouter()
  const [dataTable, setDataTable] = useState<GiftTableListProps>()
  const [loadingData, setLoadingData] = useState<boolean>(false)

  useEffect(() => {
    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query])

  const getData = async () => {
    setLoadingData(true)
    try {
      const response = await get(API_URLS.adminGetGift, query, ROLE.systemAdmin)
      setDataTable(response)
    } catch (err) {
      toast.error(MESSAGE_FORM.noData)
    }
    setLoadingData(false)
  }

  return (
    <div className="min-w-[1000px]">
      <HeaderSearch />
      <GiftTableList
        loadingTable={loadingData}
        items={dataTable?.items || []}
        totalItems={dataTable?.totalItems || 0}
        fetchData={getData}
      />
    </div>
  )
}

export default GiftPage
