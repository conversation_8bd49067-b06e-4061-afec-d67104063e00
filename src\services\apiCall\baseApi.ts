import axios, { AxiosResponse } from 'axios'

import { STORAGEKEY, getCookie } from '../cookies'
import { ROL<PERSON>, ROUTES, STATUS_CODE } from '@/constants'
import { logout } from '@/utils'

const SLASH = '/'

const getToken = (role?: ROLE) => {
  if (!role) return ''
  return role === ROLE.user
    ? getCookie(STORAGEKEY.USER_ACCESS_TOKEN)
    : getCookie(STORAGEKEY.ADMIN_ACCESS_TOKEN)
}

const AXIOS_SIGNATURE = process.env.NEXT_PUBLIC_GLOBAL_API_SIGNATURE

const instance = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}`,
  headers: {
    'x-signature': AXIOS_SIGNATURE,
  },
})

export const get = async (url: string, params?: object, role?: ROLE) => {
  const token = getToken(role)

  if (token) {
    instance.defaults.headers.common['Authorization'] = token
  }

  const configHeaders = {
    headers: {
      Authorization: token,
    },
  }

  try {
    const config = {
      ...configHeaders,
      params: params,
    }
    const response: AxiosResponse = await instance.get(SLASH + url, config)
    return response.data
  } catch (error) {
    console.log(error)
    return _errorHandler(error)
  }
}

export const post = async (
  url: string,
  data?: object | FormData,
  configParams?: object,
  role?: ROLE
) => {
  const token = getToken(role)

  if (token) {
    instance.defaults.headers.common['Authorization'] = token
  }

  const configHeaders = {
    headers: {
      Authorization: token,
    },
  }

  try {
    const config = {
      ...configHeaders,
      ...configParams,
    }
    const response: AxiosResponse = await instance.post(
      SLASH + url,
      data,
      config
    )
    return response.data
  } catch (error) {
    return _errorHandler(error)
  }
}

export const patch = async (
  url: string,
  data: FormData | object,
  role?: ROLE
) => {
  const token = getToken(role)

  if (token) {
    instance.defaults.headers.common['Authorization'] = token
  }

  const configHeaders = {
    headers: {
      Authorization: token,
    },
  }

  try {
    const response: AxiosResponse = await instance.patch(
      SLASH + url,
      data,
      configHeaders
    )
    return response.data
  } catch (error) {
    return _errorHandler(error)
  }
}
export const del = async (
  url: string,
  data: { [key: string]: string | number } | object,
  role?: ROLE
) => {
  const token = getToken(role)

  if (token) {
    instance.defaults.headers.common['Authorization'] = token
  }

  const configHeaders = {
    headers: {
      Authorization: token,
    },
  }

  try {
    const { ids } = data as { ids: string }

    if (typeof ids === 'string') {
      url = `${url}/${ids}`
    }
    const response = await instance.delete(SLASH + url, {
      data,
      ...configHeaders,
    })
    return response.data
  } catch (error) {
    return _errorHandler(error)
  }
}

const _errorHandler = (error: any) => {
  const errorCode = error?.response?.data?.statusCode
  console.log('errorCode: ', errorCode)
  if (errorCode === STATUS_CODE.unauthorized) {
    const roleAdmin = window.location.pathname.includes(ROUTES.admin)
    logout(roleAdmin ? ROLE.systemAdmin : ROLE.user)
    setTimeout(() => {
      return (window.location.href = roleAdmin
        ? ROUTES.adminLogin
        : ROUTES.home)
    }, 1000)
  }
  throw error
}
