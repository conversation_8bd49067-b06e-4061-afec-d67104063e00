import Image from 'next/image'
import { Col, Row } from 'antd'
import clsx from 'clsx'

import { renderTypeNft } from '@/constants'
import { INFTComponentProps } from '@/interfaces'
import { formatCurrency } from '@/utils'

export default function NftComponent({
  wrapperClassName = '',
  nft,
}: {
  wrapperClassName?: string
  nft: INFTComponentProps
}) {
  return (
    <div
      className={clsx(
        'bg-white border border-gray-200 rounded-lg shadow ease-linear duration-200 w-[23vw] max-w-[300px] max-sm:w-[46vw] max-lg:max-w-[265px] max-lg:max-h-[320px] overflow-hidden cursor-pointer',
        wrapperClassName
      )}
    >
      <div className=" lg:h-[300px] h-[140px] mb-4 relative">
        <Image
          width={300}
          height={300}
          className="h-full w-full object-contain"
          src={nft?.image}
          alt="nft image"
        />
      </div>
      <div className="px-3 sm:pb-5 pb-2 min-h-[52px]">
        <Row align="middle" justify="space-between" className="sm:mb-5">
          <Col>
            <div
              className={clsx(
                'text-base max-sm:text-xs font-semibold tracking-tight text-tailwindNeutral6 sm:text-tailwindNeutral1 break-all line-clamp-1'
              )}
            >
              {nft?.name}
            </div>
          </Col>
          <Col>
            <div
              className={clsx(
                'text-sm py-1 px-1 bg-tailwindBrand2 border-[2px] border-[#EA652D] rounded-md text-center max-sm:text-[10px] max-sm:py-0'
              )}
            >
              {renderTypeNft[nft?.itemType]}
            </div>
          </Col>
        </Row>
        <Row justify={'space-between'} align={'middle'}>
          <div className="gap-2 items-center sm:flex">
            <div className="max-w-[70px] lg:max-w-[130px] xl:max-w-[150px] break-all line-clamp-1 text-sm max-sm:text-xs min-h-[20px]">
              {nft?.creatorName}
            </div>
          </div>
          <h5 className="md:text-sm w-fit text-base max-sm:text-sm font-semibold tracking-tight text-tailwindNeutral1">
            {formatCurrency(nft?.saleInformation?.priceByFiat as string)}
          </h5>
        </Row>
      </div>
    </div>
  )
}
