import { StaticPageCard } from '@/components/card'
import { StaticPageLayout } from '@/components/layouts/staticPageLayout'
import { getCurrentLanguage } from '@/utils'
import { IMAGE } from '@/utils/string'

const EnglishContent = () => (
  <div className="static-page">
    <p>
      The world of Web3 offers incredible potential for creators and collectors
      alike, but it also comes with new risks.
    </p>
    <p>
      NFTs, wallets, and decentralized platforms give users freedom—but also
      place greater responsibility on each individual.
    </p>
    <p>
      This guide outlines essential practices for navigating Web3 securely and
      confidently.
    </p>

    <h3>Protecting Your Crypto Wallet</h3>
    <p>To buy, own, or trade NFTs, you need a crypto wallet.</p>
    <p>
      Think of it as your “address” on the blockchain—it stores your digital
      assets and lets you send and receive items.
    </p>
    <p>There are two major types of wallets:</p>
    <ul>
      <li>Custodial wallets: Managed by a third-party provider</li>
      <li>Non-custodial wallets: You manage your own private keys</li>
    </ul>
    <p>
      If you&apos;re using a non-custodial wallet, you&apos;ll be given a seed
      phrase—a string of words that serves as your one and only recovery key.
      This phrase is critically important.
    </p>

    <h3>What Never to Do:</h3>
    <ul>
      <li>Never share your seed phrase with anyone</li>
      <li>
        Never enter your seed phrase on a suspicious site or via a direct
        message
      </li>
      <li>Never upload, store, or screenshot your seed phrase online</li>
    </ul>
    <p>
      If someone obtains your seed phrase, they gain full access to your wallet
      and its contents.
    </p>
    <p>Write it down, store it offline, and keep it in a safe location.</p>

    <h3>Common NFT Scams and How to Spot Them</h3>
    <p>Scams in Web3 come in many forms:</p>
    <ul>
      <li>Fake NFT collections that look legitimate</li>
      <li>Impersonators posing as support or celebrities via social media</li>
      <li>Airdropped NFTs designed to lure you to malicious websites</li>
    </ul>
    <p>
      Since blockchain transactions are irreversible, prevention is your best
      defense.
    </p>
    <p>The more you research and verify before acting, the safer you are.</p>
    <h3>How to Vet an NFT Project</h3>
    <ol>
      <li>
        Check official links and social accounts
        <ul>
          <li>
            Ensure that links on social media match those on verified NFT
            platforms.
          </li>
        </ul>
      </li>
      <li>
        Investigate the creator
        <ul>
          <li>
            Look at their previous works, transaction history, and community
            engagement.
          </li>
        </ul>
      </li>
      <li>
        Review transaction patterns
        <ul>
          <li>
            Sudden spikes in trading volume, especially among a small group of
            accounts, could signal artificial price manipulation.
          </li>
        </ul>
      </li>
      <li>
        Search community chatter
        <ul>
          <li>
            Use platforms like X (formerly Twitter) or Discord to look for
            genuine feedback—and pay attention to mentions of scams or “rug
            pulls.”
          </li>
        </ul>
      </li>
    </ol>

    <h3>Tips for Sellers</h3>
    <ul>
      <li>No marketplace will contact you via DMs first</li>
      <li>
        If someone claiming to be OpenSea support messages you directlyTips for
        Sellers, it&apos;s likely a scam.
      </li>
      <li>
        No platform will ask you to send ETH for “support” reasons
        <br />
        Requests like this are red flags—block and report them.
      </li>
      <li>
        When in doubt, go to the official Help Center
        <br />
        Use only the support tools on verified websites to contact NFT
        platforms.
      </li>
    </ul>

    <h3>Tips for Buyers</h3>
    <ul>
      <li>
        Be cautious of unsolicited airdropped NFTs
        <br />
        If you don&apos;t recognize the sender, don&apos;t click on any links or
        files.
      </li>
      <li>
        Always verify an NFT&apos;s authenticity and contract address
        <br />
        Never rely on visuals alone—check the metadata and provenance.
      </li>
      <li>
        Watch for pricing manipulation scams
        <br />
        Look out for inflated bids in less-valuable currencies, or NFTs being
        transferred to well-known wallets to create fake legitimacy.
      </li>
    </ul>

    <h3>Securing Your Wallet Information</h3>
    <ul>
      <li>
        Ignore “free NFT” offers on social media unless confirmed official
      </li>
      <li>
        Never click suspicious links sent via DMs, tweets, or unknown email
        campaigns
      </li>
      <li>
        Use unique, strong passwords and enable two-factor authentication where
        possible
      </li>
      <li>
        Store valuable NFTs in a hardware wallet (cold storage) for long-term
        protection
      </li>
    </ul>

    <h3>Be Cautious with Information Sharing</h3>
    <p>
      Some scams involve fake emails, malicious ads, or urgent messages asking
      you to connect your wallet or provide personal info.
    </p>
    <p>These tactics are designed to rush you into giving up control.</p>
    <ul>
      <li>Always confirm website URLs via trusted sources</li>
      <li>
        Verify a project’s website via its official Twitter or NFT collection
        page
      </li>
      <li>
        Glitters or similar services will never ask for your seed phrase, wallet
        screenshots, or remote access via screen share
      </li>
      <li>
        Never click links from unknown senders, even if they appear legitimate
      </li>
    </ul>

    <h3>Final Thought: Trust Your Judgment</h3>
    <p>Web3 is still evolving, and there’s no universal manual yet.</p>
    <p>
      Security in this space depends on thoughtful decisions, a critical eye,
      and good habits.
    </p>
    <p>If something feels off—pause and investigate.</p>
    <p>And remember: if it seems too good to be true, it probably is.</p>
    <p>
      At Glitters, we believe that a safe and transparent ecosystem is the
      foundation for creativity and connection.
    </p>
    <p>Your awareness is what makes this space stronger.</p>
  </div>
)

const JapaneseContent = () => (
  <div className="static-page">
    <p>NFTや暗号資産を扱うWeb3の世界は、魅力と可能性に満ちています。</p>
    <p>しかし同時に、それを悪用しようとする詐欺やリスクも存在します。</p>
    <p>
      ここでは、NFTを安心して楽しむために押さえておきたい「Web3での自己防衛の基本」をまとめました。
    </p>

    <h3>暗号資産ウォレットを守るために知っておくべきこと</h3>
    <p>NFTを購入・保有・取引するには、暗号資産ウォレットが不可欠です。</p>
    <p>
      ウォレットとは、あなたの「ブロックチェーン上の住所」であり、資産を保管し、やりとりを行うためのアプリケーションです。
    </p>
    <br />
    <p>ウォレットには大きく2種類あります。</p>
    <ul>
      <li>カストディアル（管理型）ウォレット：第三者が管理する形式</li>
      <li>
        ノンカストディアル（自己管理型）ウォレット：自分で秘密鍵（シードフレーズ）を管理する形式
      </li>
    </ul>
    <p>
      ノンカストディアル型の場合、シードフレーズ（復元フレーズ）が最重要情報になります。
      これは「12〜24語の単語列」で構成され、ウォレットへの唯一のアクセス手段です。
    </p>

    <h3>絶対にしてはいけないこと</h3>
    <ul>
      <li>シードフレーズを誰かに「教える」「送る」「見せる」こと</li>
      <li>
        不明なリンクやDM内で、シードフレーズの入力を求められた場合に応じること
      </li>
    </ul>
    <p>
      シードフレーズを知られた瞬間、あなたのウォレットの中身は盗まれる可能性があります。
    </p>
    <p>
      この情報は紙に書いて保管するなど、オフラインかつ安全な場所での保管が推奨されます。
    </p>

    <h3>NFT詐欺の代表的なパターンと見分け方</h3>
    <p>Web3上の詐欺にはさまざまな形があります：</p>
    <ul>
      <li>偽物のNFTプロジェクトを本物のように見せかけて販売する</li>
      <li>有名人になりすましてDMを送り、秘密情報を引き出そうとする</li>
      <li>無料配布（エアドロップ）を装って悪質なサイトに誘導する</li>
    </ul>
    <p>ブロックチェーン上の取引は原則として取り消せません。</p>
    <p>だからこそ、事前の「見極め」が最大の防衛手段です。</p>

    <h3>NFTプロジェクトを見極めるポイント</h3>
    <ol>
      <li>
        公式サイト・SNSの一致を確認する
        <ul>
          <li>
            SNSに記載されたリンクと、NFTマーケットプレイス上のリンクが一致しているか確認しましょう。
          </li>
        </ul>
      </li>
      <li>
        出品者の信頼性を調べる
        <ul>
          <li>
            コレクションの説明欄から出品者名をたどり、過去に出品した作品や取引履歴を確認できます。
          </li>
        </ul>
      </li>
      <li>
        販売履歴や取引パターンを見る
        <ul>
          <li>
            不自然に高額で売買されたNFT、急激に増える取引量などは、価格操作の可能性があります。
          </li>
        </ul>
      </li>
      <li>
        プロジェクト周辺の声を探す
        <ul>
          <li>
            X（旧Twitter）やDiscordなどで話題になっているか、逆に「詐欺」「ラグプル」などの警告が出ていないかを確認しましょう。
          </li>
        </ul>
      </li>
    </ol>

    <h3>出品者向け：詐欺対策の基本</h3>
    <ul>
      <li>
        Glittersや取引所を名乗るアカウントがDMで連絡してくることはありません
      </li>
      <li>サポートを装ってETHやADAの送金を要求するのは詐欺です</li>
      <li>問い合わせは必ず公式サイトの「ヘルプセンター」から行いましょう</li>
    </ul>

    <h3>購入者向け：NFTを購入する際の注意点</h3>
    <ul>
      <li>知らない相手から送られてきたNFTを安易に開かない</li>
      <li>
        「ロック解除」「ダウンロード可能なコンテンツ」などと書かれたNFTは、詐欺やウイルスの可能性あり
      </li>
      <li>「本物かどうか」はコントラクトアドレスや出品者情報から確認する</li>
      <li>
        初期販売価格と大きく乖離したオファーや、異常な取引量の増加には注意
      </li>
    </ul>

    <h3>ウォレット情報を守るために</h3>
    <ul>
      <li>ソーシャルメディア上の「無料NFT」キャンペーンには慎重に対応</li>
      <li>不審なリンク、広告、DMに含まれるURLは絶対にクリックしない</li>
      <li>パスワードは他サービスと使いまわさない</li>
      <li>
        長期保有するNFTはハードウェアウォレットでオフライン保管（コールドストレージ）するのが最も安全
      </li>
    </ul>

    <h3>最後に：判断力が最大のセキュリティ</h3>
    <p>
      Web3は自由で可能性に満ちた世界ですが、それと同じだけの「自己責任」が求められます。
    </p>
    <p>「怪しい」と思ったら、一度立ち止まり、情報を確かめましょう。</p>
    <p>“良すぎて信じられない話”は、たいていの場合、信じないほうが正解です。</p>
    <p>
      Glittersは、クリエイターとファンが信頼を持ってつながる空間であると同時に、ユーザー自身が安全にNFTを楽しめる文化を共に育てていきます。
    </p>
  </div>
)

function HowToStayProtectedInWeb() {
  const lang = getCurrentLanguage()
  const title =
    lang === 'en'
      ? 'Staying Safe in Web3 – You Are Your Best Security'
      : 'Web3で安全にNFTを楽しむために ― あなた自身が最大のセキュリティです'

  return (
    <StaticPageLayout title={title}>
      <StaticPageCard
        title={title}
        imageUrl={IMAGE.howToStayProtectedInWeb3}
        createdAt="2025-08-01"
      >
        {lang === 'en' ? <EnglishContent /> : <JapaneseContent />}
      </StaticPageCard>
    </StaticPageLayout>
  )
}

export default HowToStayProtectedInWeb
