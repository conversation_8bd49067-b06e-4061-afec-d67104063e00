import { useEffect, useMemo } from 'react'
import { SOCKET_EVENT } from '@/constants/socket'
import socketUser from 'socket'
import { TRANSACTION_STATUS } from '@/constants'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'
import { walletStore } from '@/store/slices/wallet'

interface UseSocketUserBuyByGetPlatformProps {
  setCheckoutStatus: (status: TRANSACTION_STATUS) => void
  setErrStatus: (status: string) => void
  paymentCode: string
  nftId: string
}

const useSocketUserBuyByGetPlatform = ({
  setCheckoutStatus,
  setErrStatus,
  paymentCode,
  nftId,
}: UseSocketUserBuyByGetPlatformProps) => {
  const [cookies] = useCookies([STORAGEKEY.WALLET_ADDRESS])
  const wallet = cookies[STORAGEKEY.WALLET_ADDRESS]
  const increaseSuccessPayment = walletStore(
    (store) => store.increaseSuccessPayment
  )

  const isConnectSocket = nftId && wallet

  const socket = useMemo(
    () => (isConnectSocket ? socketUser() : null),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isConnectSocket, paymentCode]
  )

  useEffect(() => {
    if (!socket) return
    socket.emit('user:joinPrivateRoom', { paymentCode })
    socket.on(
      SOCKET_EVENT.GET_PLATFORM_PAYMENT_SUCCESS,
      ({ nftId: paymentSuccessNftId }) => {
        if (nftId !== paymentSuccessNftId) return
        setCheckoutStatus(TRANSACTION_STATUS.success)
        increaseSuccessPayment()
      }
    )
    socket.on(SOCKET_EVENT.GET_PLATFORM_PAYMENT_FAILED, () => {
      setCheckoutStatus(TRANSACTION_STATUS.failed)
      setErrStatus('Payment failed')
    })
    socket.on(SOCKET_EVENT.CHECKOUT_SESSION_EXPIRED, () => {
      setCheckoutStatus(TRANSACTION_STATUS.failed)
      setErrStatus('Checkout session expired')
    })

    return () => {
      socket.disconnect()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentCode, wallet])
}

export default useSocketUserBuyByGetPlatform
