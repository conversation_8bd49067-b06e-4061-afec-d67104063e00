import { useEffect, RefObject } from 'react'

type Event = MouseEvent | TouchEvent

export const useClickOutside = <T extends HTMLElement = HTMLElement>(
  ref: RefObject<T>,
  handler: (event: Event) => void
) => {
  useEffect(() => {
    const listener = (event: Event) => {
      const el = ref?.current
      if (!el || el.contains((event?.target as Node) || null)) {
        return
      }
      handler(event)
    }

    document.addEventListener('mouseup', listener)
    // document.addEventListener('touchend', listener)

    return () => {
      document.removeEventListener('mouseup', listener)
      // document.removeEventListener('touchend', listener)
    }
  }, [ref, handler])
}
