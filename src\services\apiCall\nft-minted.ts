import { API_URLS, DEFAULT_CONFIG_PARAMS, ROL<PERSON> } from '@/constants'
import { get } from './baseApi'
import { PaginatedNftMintedResponse, NftMetadata } from '@/interfaces'

// Get user owned NFTs by wallet address
export const getUserOwnedNfts = (
  walletAddress: string,
  params: {
    pageIndex?: number
    pageSize?: number
    searchWord?: string
    category?: string
  },
  role?: ROLE
): Promise<PaginatedNftMintedResponse> => {
  return get(
    `${API_URLS.myNftList}/${walletAddress}`,
    params,
    role || ROLE.user
  )
}

export const getUserNfts = (
  params: {
    pageIndex?: number
    pageSize?: number
    searchWord?: string
    category?: string
  },
  role?: ROLE
): Promise<PaginatedNftMintedResponse> => {
  return get(`${API_URLS.myNftList}`, params, role || ROLE.user)
}

// Get NFT metadata by token ID (external endpoint)
export const getNftMetadata = async (tokenId: string): Promise<NftMetadata> => {
  return await get(
    `${API_URLS.userDetailNft}/${tokenId}`,
    DEFAULT_CONFIG_PARAMS,
    ROLE.user
  )
}
