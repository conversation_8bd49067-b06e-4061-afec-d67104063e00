import {
  COLLECTION_SORT_BY,
  COLLECTION_SORT_ORDER,
} from '@/constants/collection'
import { Paginated } from '.'
import { APPROVAL_STATUS } from '@/constants'

export enum ApproveStatus {
  APPROVED = APPROVAL_STATUS.approved,
  REJECTED = APPROVAL_STATUS.rejected,
}

export type Collection = {
  _id?: string
  name: string
  standard: string
  description: string
  category: string
  logoImage: string
  backgroundImage?: string
  createdAt: Date
  // Statistics fields
  floorPrice?: number | null
  totalVolume?: number
  totalApprovedNfts?: number
  totalNfts?: number
}

export type CollectionPayload = {
  name: string
  standard: string
  description: string
  category: string
  logoImageId: string
  backgroundImageId?: string
  isDraft?: boolean
  isPriority?: boolean
  approvalStatus?: APPROVAL_STATUS
}

export interface CollectionSearch {
  pageIndex?: number // default 1
  pageSize?: number // default 12
  searchWord?: string
  approvalStatus?: APPROVAL_STATUS // draft, waiting, approved, rejected
  category?: string
  isPriority?: boolean | null
  sortBy?: COLLECTION_SORT_BY // createdAt, name, priority
  sortOrder?: COLLECTION_SORT_ORDER // asc, desc
}

export type CollectionStats = {
  totalCollections: number
  waitingForApprove: number
  rejected: number
  draft: number
}

export type ApproveCollectionPayload = {
  approvalStatus: ApproveStatus
}

export type PaginatedCollection = Paginated<Collection>
