import { toast } from 'react-toastify'

import { LENGTH_BIT_IMAGE, MAX_SIZE_FILE } from '@/constants'
import { MESSAGE_FORM } from '@/utils/string'
import { ALLOWED_FILE_TYPE } from '@/components/uploadImages'

const ALLOWED_MIME_TYPE_MAP = new Map<string, string>([
  [ALLOWED_FILE_TYPE.GIF, 'image/gif'],
  [ALLOWED_FILE_TYPE.JPG, 'image/jpeg'],
  [ALLOWED_FILE_TYPE.MP4, 'video/mp4'],
  [ALLOWED_FILE_TYPE.PNG, 'image/png'],
  [ALLOWED_FILE_TYPE.SVG, 'image/svg+xml'],
])

export const isCheckCorrectType = (
  acceptTypes: ALLOWED_FILE_TYPE[],
  mimeType: string
) => {
  const arrayFileAccept = acceptTypes.map((item) =>
    ALLOWED_MIME_TYPE_MAP.get(item)
  )
  if (!arrayFileAccept.includes(mimeType)) {
    toast.error(MESSAGE_FORM.imageLogoDifferentType)
    return false
  }
  return arrayFileAccept.includes(mimeType)
}

export const isCheckCorrectSizeImage = (size: number) => {
  const isLt10M = size / LENGTH_BIT_IMAGE < MAX_SIZE_FILE
  if (!isLt10M) {
    toast.error(MESSAGE_FORM.upload10MB)
    return false
  }
  return isLt10M
}

export const isCheckSizeImage = (size: number, maxSize: number) => {
  const isLtSize = size / LENGTH_BIT_IMAGE < maxSize
  if (!isLtSize) {
    toast.error(
      `アップロードファイルは${maxSize}MB以下のものでなければなりません。`
    )
    return false
  }
  return isLtSize
}

export const getImageIdFromUrl = (url: string) => {
  const match = url.match(/^https:\/\/[^/]+\/(.+)$/)
  const imageId = match ? match[1] : null
  return imageId
}
