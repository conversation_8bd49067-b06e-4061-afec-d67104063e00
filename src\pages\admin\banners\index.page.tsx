import React, { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'

import { TBanner, TBannerData } from '@/interfaces'
import BannerTableList from './BannerTableList'
import { get } from '@/services/apiCall/baseApi'
import {
  API_URLS,
  DEFAULT_PAGE_SIZE,
  NO_CACHING_TIME,
  pageConfig,
  ROLE,
} from '@/constants'

const TEXT_LABELS = {
  BANNER_MANAGER: 'バナーマネージャー',
}

const Banners = () => {
  const [pageIndex, setPageIndex] = useState<number>(pageConfig.pageOne)
  const [banners, setBanners] = useState<TBanner[] | []>([])
  const [totalItems, setTotalItems] = useState<number | null>(0)
  const [searchWord, setSearchWord] = useState<string>('')

  const getBanners = (): Promise<TBannerData> => {
    return get(
      API_URLS.adminGetBanners,
      {
        pageIndex,
        pageSize: DEFAULT_PAGE_SIZE,
        searchWord: searchWord || undefined,
      },
      ROLE.systemAdmin
    )
  }

  const {
    data: bannersData,
    refetch: refetchBannerData,
    isLoading,
  } = useQuery({
    queryKey: ['get_banner', pageIndex, searchWord],
    queryFn: getBanners,
    gcTime: NO_CACHING_TIME,
  })

  useEffect(() => {
    bannersData?.items && setBanners([...banners, ...bannersData.items])
    setTotalItems(bannersData?.totalItems || 0)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bannersData])

  useEffect(() => {
    setBanners([])
    setPageIndex(pageConfig.pageOne)
  }, [searchWord])

  const refetchBanner = () => {
    setBanners([])
    setPageIndex(pageConfig.pageOne)
    refetchBannerData()
  }

  return (
    <div className="min-w-md overflow-x-auto">
      <div className="text-white my-4 font-semibold text-2xl pb-3 border-b border-[#FFFFFF26]">
        {TEXT_LABELS.BANNER_MANAGER}
      </div>
      <BannerTableList
        bannersData={bannersData}
        pageSize={DEFAULT_PAGE_SIZE}
        refetchBanner={refetchBanner}
        banners={banners}
        setPageIndex={setPageIndex}
        pageIndex={pageIndex}
        setBanners={setBanners}
        isLoading={isLoading}
        totalItems={totalItems}
        searchWord={searchWord}
        setSearchWord={setSearchWord}
      />
    </div>
  )
}

export default Banners
