import { NextApiRequest, NextApiResponse } from 'next'
import { getMarketCollections } from '@/services/apiCall'
import { getListNewsForUser } from '@/services/apiCall/news'
import {
  generateSitemapUrl,
  generateSitemapXML,
  STATIC_ROUTES,
  SITEMAP_CACHE_CONFIG
} from '@/utils/sitemap'

// Get dynamic collections data
const getCollectionUrls = async () => {
  try {
    const collectionsData = await getMarketCollections({
      pageIndex: 1,
      pageSize: 1000, // Get all collections
      searchWord: '',
      category: '',
    })

    return collectionsData.items.map((collection: any) =>
      generateSitemapUrl({
        url: `/collections/${collection._id}`,
        lastmod: collection.updatedAt ? new Date(collection.updatedAt).toISOString().split('T')[0] : undefined,
        changefreq: 'weekly',
        priority: '0.8'
      })
    )
  } catch (error) {
    console.error('Error fetching collections for sitemap:', error)
    return []
  }
}

// Get dynamic news data
const getNewsUrls = async () => {
  try {
    const newsData = await getListNewsForUser({
      pageIndex: 1,
      pageSize: 1000, // Get all news
    })

    return newsData.items.map((news: any) =>
      generateSitemapUrl({
        url: `/news/${news._id}`,
        lastmod: news.updatedAt ? new Date(news.updatedAt).toISOString().split('T')[0] : undefined,
        changefreq: 'monthly',
        priority: '0.6'
      })
    )
  } catch (error) {
    console.error('Error fetching news for sitemap:', error)
    return []
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Generate static URLs
    const staticUrls = STATIC_ROUTES.map((route) => generateSitemapUrl(route))

    // Get dynamic URLs
    const [collectionUrls, newsUrls] = await Promise.all([
      getCollectionUrls(),
      getNewsUrls(),
    ])

    // Combine all URLs
    const allUrls = [...staticUrls, ...collectionUrls, ...newsUrls]

    // Generate sitemap XML
    const sitemap = generateSitemapXML(allUrls)

    // Set headers
    res.setHeader('Content-Type', 'text/xml')
    res.setHeader('Cache-Control', `public, s-maxage=${SITEMAP_CACHE_CONFIG.static}, stale-while-revalidate`)

    return res.status(200).send(sitemap)
  } catch (error) {
    console.error('Error generating sitemap:', error)
    return res.status(500).json({ message: 'Error generating sitemap' })
  }
}
