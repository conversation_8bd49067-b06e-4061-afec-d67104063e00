import React, { useState } from 'react'
import { HiSearch } from 'react-icons/hi'

import { Search } from '@/components/common'
import { BannerHeaderPropsType } from '@/interfaces'
import ButtonComponent from '@/components/buttons/Button'
import { BUTTON } from '@/utils/string'
import { pageConfig } from '@/constants'

function HeaderSearch(props: BannerHeaderPropsType) {
  const { setBanners, setPageIndex } = props
  const [searchValue, setSearchValue] = useState('')

  const handleSearch = (searchWord = searchValue) => {
    !!searchWord?.trim() && setBanners([])

    setPageIndex(pageConfig.pageOne)
  }
  const handleClearAll = () => {
    handleSearch('')
    setSearchValue('')
    setBanners([])
  }

  return (
    <div>
      <div className="flex gap-8 mx-2 border-b border-b-tailwindNeutral4 pb-6 ">
        <Search
          handleSearch={handleSearch}
          setSearchValue={setSearchValue}
          handleClearAll={handleClearAll}
          searchValue={searchValue}
          placeholder="タグで検索する"
          className=" border-tailwindNeutral3 rounded-md  flex-1 max-w-[576px]"
        />

        <ButtonComponent
          className="flex justify-center items-center gap-2 !bg-black text-white !h-10 !w-[128px]"
          type="secondary"
          title={BUTTON.search}
          onClick={() => handleSearch()}
          afterIcon={<HiSearch className="w-6 h-6 text-white" />}
        />
      </div>
    </div>
  )
}

export default HeaderSearch
