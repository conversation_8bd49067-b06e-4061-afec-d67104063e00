import { API_URLS, ETYPE_TIME_OPTION, ROLE } from '@/constants'
import { get } from './baseApi'
import {
  ListTransaction,
  PaginationOption,
  TransactionStats,
} from '@/interfaces'

// Get user transactions by wallet address
export const getUserTransactions = async (params: {
  pageIndex?: number
  pageSize?: number
  sortCondition?: string
}): Promise<ListTransaction> => {
  return await get(`${API_URLS.userTransactions}`, params, ROLE.user)
}

// Get transaction statistics (admin only)
export const getTransactionStatistics = (
  timeOption: ETYPE_TIME_OPTION
): Promise<TransactionStats> => {
  return get(API_URLS.transactionStatistics, { timeOption }, ROLE.systemAdmin)
}

// Get all transactions (admin only)
export const getListTransaction = (
  query: PaginationOption & { timeOption?: ETYPE_TIME_OPTION }
): Promise<ListTransaction> => {
  return get(API_URLS.adminListTransaction, query, ROLE.systemAdmin)
}
