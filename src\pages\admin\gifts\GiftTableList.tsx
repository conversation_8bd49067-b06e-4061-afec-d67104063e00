import React, { useState } from 'react'
import { Empty, Table } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { UploadOutlined } from '@ant-design/icons'
import Image from 'next/image'
import { useRouter } from 'next/router'
import moment from 'moment'

import ButtonComponent from '@/components/buttons/Button'
import Text from '@/components/texts/Text'
import ModalImportCsv from '@/components/modal/ModalImportCsv'
import { ETYPE_GIFT, renderStatusGift } from '@/constants'
import { MESSAGE_FORM } from '@/utils/string'
import { formatNumberWithComma } from '@/utils'
import { useTableHeight } from '@/hooks'

interface DataType {
  key: React.ReactNode
  image: string
  name: string
  email: string
  intermediaryId: string
  productId: string
  createdAt: string
  status: string
}

export interface GiftTableListProps {
  items: DataType[]
  totalItems: number
  fetchData: () => void
  loadingTable?: boolean
}

const classColorStatusText = {
  [ETYPE_GIFT.received]: 'text-tailwindCompleted',
  [ETYPE_GIFT.notReceived]: 'text-tailwindFailed',
}

const GiftTableList = (props: GiftTableListProps) => {
  const [openModalDeleteAccount, setOpenModalDeleteAccount] =
    useState<boolean>(false)
  const { query, push } = useRouter()
  const totalHeightMinus = 400
  const { tableHeight } = useTableHeight(totalHeightMinus)

  const columns: ColumnsType<DataType> = [
    {
      title: 'イメージ',
      dataIndex: 'image',
      key: 'image',
      width: 150,
      render: (data) => (
        <div className="w-[56px] h-[56px]  rounded-md shadow ">
          <Image
            width={56}
            height={56}
            className="h-full w-full object-contain rounded-lg shadow"
            src={data}
            alt="gift image"
          />
        </div>
      ),
    },
    {
      title: 'タイトル',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'メールアドレス',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '中間業者ID',
      dataIndex: 'intermediaryId',
      key: 'intermediaryId',
    },
    {
      title: '商品ID',
      dataIndex: 'productId',
      key: 'productId',
    },
    {
      title: 'メール送信時点',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time: string) => (
        <span>{moment(time).format('YYYY-MM-DD')}</span>
      ),
    },
    {
      title: 'ステータス',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span className={`${classColorStatusText[status]}`}>
          {renderStatusGift[status]}
        </span>
      ),
    },
  ]

  return (
    <>
      <div className="mt-[18px]">
        <div className="flex  items-center justify-between">
          <div className="flex items-center gap-4 mb-6 mx-2">
            <ButtonComponent
              className="flex justify-center items-center gap-2 !bg-black rounded-lg  text-white"
              type="default"
              title="インポート"
              onClick={() => setOpenModalDeleteAccount(true)}
              afterIcon={<UploadOutlined className="h-6 w-6 " rev={'icon'} />}
            />
            <a
              className="text-tailwindBlue underline"
              href={process.env.NEXT_PUBLIC_LINK_CSV_SAMPLE}
            >
              CSVファイルのサンプル
            </a>
          </div>
          <Text className="text-base pr-2">
            {formatNumberWithComma(props.totalItems) || 0} 件
          </Text>
        </div>

        <Table
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={MESSAGE_FORM.noData}
              />
            ),
          }}
          loading={props.loadingTable}
          className="mx-2 cursor-pointer"
          pagination={{
            position: ['bottomCenter'],
            total: props.totalItems,
            pageSize: Number(query?.pageSize) || 1,
            showSizeChanger: false,
            current: Number(query?.pageIndex) || 1,
            onChange: (page) => push({ query: { ...query, pageIndex: page } }),
          }}
          columns={columns}
          dataSource={props.items}
          scroll={{ y: tableHeight }}
          rowKey="_id"
        />
      </div>
      <ModalImportCsv
        isModalOpen={openModalDeleteAccount}
        content={'このニュースを削除してもよろしいですか。'}
        setModalOpen={setOpenModalDeleteAccount}
        onSuccess={() => props.fetchData()}
      />
    </>
  )
}

export default GiftTableList
