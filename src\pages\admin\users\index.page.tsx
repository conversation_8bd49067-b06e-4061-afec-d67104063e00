import React, { useEffect, useState } from 'react'
import { useViewport } from '@/hooks'

import { UserSearchInput, Users } from '@/interfaces'
import HeaderSearch from './HeaderSearch'
import UserTableList from './UserTableList'
import { getUsers } from '@/services/apiCall'
import { DEFAULT_PAGE_USERS_MANAGER } from '@/constants'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'

export const defaultSearch = {
  search: '',
  page: DEFAULT_PAGE_USERS_MANAGER.defaultPage,
}
const User = () => {
  const [search, setSearch] = useState<UserSearchInput>(defaultSearch)
  const [users, setUsers] = useState<Users>()
  const { windowHeight } = useViewport()

  const getListUser = async () => {
    //get pageSize  according to screen resolution
    const pageSize =
      windowHeight > 1500
        ? Math.floor(windowHeight / 50)
        : DEFAULT_PAGE_USERS_MANAGER.defaultPageSize
    const params = {
      pageIndex: search.page,
      searchWord: search.search?.trim(),
      pageSize,
    }
    const data = await getUsers(params)
    setUsers(data)
  }

  useEffect(() => {
    getListUser()
    // eslint-disable-next-line
  }, [search])

  const text = {
    userManager: 'ユーザー管理',
  }

  return (
    <div className="min-w-md">
      <TextAdminHeader title={text.userManager} />
      <HeaderSearch setSearch={setSearch} />
      <UserTableList
        dataUsers={users}
        valueSearch={search}
        setSearch={setSearch}
      />
    </div>
  )
}

export default User
