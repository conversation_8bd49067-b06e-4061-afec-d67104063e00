import { StateCreator } from 'zustand'
import { ROLE } from '@/constants'
import { ICommonState, ModalState } from '@/interfaces'

export const commonSlice: StateCreator<ICommonState> = (set) => ({
  role: null,
  modalType: null,
  modalTitle: null,
  message: null,
  status: null,
  isLoading: false,
  canCreate: false,
  canEdit: false,
  canDelete: false,
  canApprove: false,

  setRole(role) {
    const permissions = {
      canCreate: role === ROLE.systemAdmin || role === ROLE.operator,
      canEdit: role === ROLE.systemAdmin || role === ROLE.operator,
      canDelete: role === ROLE.systemAdmin || role === ROLE.operator,
      canApprove: role === ROLE.approver || role === ROLE.systemAdmin,
    }
    set(() => ({
      role,
      ...permissions,
    }))
  },
  setLoading(type) {
    set(() => ({ isLoading: type }))
  },
  setModalType(data: ModalState) {
    set(() => ({
      modalType: data.type,
      modalTitle: data.title,
      message: data?.message,
      status: data?.status,
      event: data?.event,
    }))
  },
})
