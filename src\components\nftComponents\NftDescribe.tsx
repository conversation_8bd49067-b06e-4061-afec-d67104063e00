import React from 'react'

import TextTitle from '../texts/TextTitle'
import { sanitizedHTML } from '@/utils'

const NftDescribe = ({ data }: { data?: string }) => {
  return (
    <div className="my-10 flex flex-col items-center">
      <div className="mb-5">{!!data && <TextTitle title="紹介文" />}</div>
      <div
        className="break-words xl:w-[850px] 2xl:w-[900px] px-4"
        dangerouslySetInnerHTML={{
          __html: sanitizedHTML(data || ''),
        }}
      />
    </div>
  )
}

export default NftDescribe
