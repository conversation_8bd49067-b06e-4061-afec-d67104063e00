import { Image } from 'antd'
import { DOMAttributes, useEffect, useRef } from 'react'

type PreviewNftProps = {
  src: string
  alt?: string
  isVideo?: boolean
  className?: string
  controls?: boolean
}

export const VideoPreview = ({
  src,
  className = '',
  controls = false,
}: Omit<PreviewNftProps, 'alt'>) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const handleMouseInOut: DOMAttributes<HTMLVideoElement> = controls
    ? {}
    : {
        onMouseOver: (e) => e.currentTarget.play(),
        onMouseOut: (e) => {
          e.currentTarget.pause()
          e.currentTarget.currentTime = 0.1 // reset to "thumbnail"
        },
      }

  useEffect(() => {
    const video = videoRef.current
    if (video) {
      // Load the first frame
      video.currentTime = 0.1 // skip 0 to avoid black frame
    }
  }, [])

  return (
    <video
      ref={videoRef}
      src={src}
      className={className}
      controls={controls}
      muted={!controls}
      loop={!controls}
      preload="metadata"
      {...handleMouseInOut}
    />
  )
}

export function PreviewNft({
  src,
  alt = 'preview',
  isVideo = false,
  className = '',
  controls = false,
}: PreviewNftProps) {
  if (isVideo) {
    return <VideoPreview src={src} className={className} controls={controls} />
  }

  return <Image src={src} alt={alt} className={className} />
}
