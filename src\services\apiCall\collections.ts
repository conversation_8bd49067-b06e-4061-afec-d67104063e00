import { API_URLS, ROLE } from '@/constants'
import {
  Collection,
  CollectionStats,
  PaginatedCollection,
  CollectionSearch,
  CollectionPayload,
  ApproveCollectionPayload,
} from '@/interfaces'
import { get, post, patch, del } from './baseApi'

export type ApproverRole = ROLE.systemAdmin | ROLE.approver

export const getCollectionStats = (): Promise<CollectionStats> => {
  return get(API_URLS.adminCollectionStats, {}, ROLE.systemAdmin)
}

export const getListCollections = (
  query: CollectionSearch
): Promise<PaginatedCollection> => {
  return get(API_URLS.adminCollections, query, ROLE.systemAdmin)
}

export const deleteCollection = async (
  ids: string | string[],
  role: ROLE.systemAdmin | ROLE.operator
): Promise<void> => {
  return await del(API_URLS.adminCollections, { ids }, role)
}

export const createCollection = async (
  data: CollectionPayload,
  role: ROLE.systemAdmin | ROLE.operator
): Promise<void> => {
  return await post(API_URLS.adminCollections, data, {}, role)
}

export const getCollection = (id: string): Promise<Collection> => {
  return get(`${API_URLS.adminCollections}/${id}`, {}, ROLE.systemAdmin)
}

export const updateCollection = (
  id: string,
  data: Partial<CollectionPayload>,
  role: ROLE.systemAdmin | ROLE.operator
): Promise<CollectionPayload> => {
  return patch(`${API_URLS.adminCollections}/${id}`, data, role)
}

export const updateCollectionPriority = (
  id: string,
  data: Partial<CollectionPayload>,
  role: ROLE.systemAdmin | ROLE.approver
): Promise<CollectionPayload> => {
  return patch(`${API_URLS.adminCollections}/${id}/priority`, data, role)
}

export const getCreatorsOnCollection = (
  query: CollectionSearch
): Promise<PaginatedCollection> => {
  return get(API_URLS.adminCreatorsOfCollection, query, ROLE.systemAdmin)
}

export const approveCollection = async (
  id: string,
  payload: ApproveCollectionPayload,
  role: ApproverRole
): Promise<void> => {
  return await patch(
    `${API_URLS.adminCollections}/${id}/approval`,
    payload,
    role
  )
}

export const getCollections = async (
  params: CollectionSearch
): Promise<PaginatedCollection> => {
  return await get(API_URLS.adminCollections, params, ROLE.systemAdmin)
}

export const getMarketCollections = async (params: {
  pageIndex?: number
  pageSize?: number
  searchWord?: string
  category?: string
}): Promise<PaginatedCollection> => {
  return await get('v1/market/collections', params)
}

export const getCollectionsForUser = (
  query: CollectionSearch
): Promise<PaginatedCollection> => {
  return get(API_URLS.collections, query)
}

export const getCollectionForUserById = (id: string): Promise<Collection> => {
  return get(`${API_URLS.collections}/${id}`, {})
}

export const getListCollectionsCSV = () => {
  return get(
    `${API_URLS.adminCollections}/export-csv/list-collections`,
    {},
    ROLE.systemAdmin
  )
}
