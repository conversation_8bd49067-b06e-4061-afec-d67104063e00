import moment from 'moment'
import { ShareToXButton, ShareToFacebookButton } from '../buttons'
import Image from 'next/image'
import { useTranslate } from '@/hooks'
import { ReactElement } from 'react'

export function StaticPageCard({
  imageUrl,
  title,
  createdAt,
  classNames,
  children,
}: {
  imageUrl?: string
  title: string
  createdAt?: string | Date
  children: ReactElement | string
  classNames?: string
}) {
  const trans = useTranslate()
  return (
    <div
      className={`bg-getCardBg rounded-lg mx-auto sm:p-8 p-6 flex flex-col sm:flex-row sm:gap-10 gap-6 ${classNames}`}
    >
      {/* Left Column: Metadata & Share */}
      <div className="w-full sm:w-1/4 flex sm:flex-col justify-between sm:justify-start flex-row gap-6 font-montserrat text-white">
        <div>
          <div className="font-semibold text-[14px] tracking-[0.02em] leading-[140%] mb-2">
            {trans.date}
          </div>
          <div className="font-normal text-[12px] tracking-[0.02em] leading-[140%]">
            {createdAt ? moment(createdAt).format('YYYY-MM-DD') : '-'}
          </div>
        </div>
        <div>
          <div className="font-semibold text-[14px] tracking-[0.02em] leading-[140%] mb-2">
            {trans.share}
          </div>
          <div className="flex gap-3 items-center mt-1">
            <ShareToXButton />
            <ShareToFacebookButton />
          </div>
        </div>
      </div>
      {/* Right Column: Main Content */}
      <div className="w-full md:w-3/4 flex flex-col sm:gap-10 gap-6">
        {imageUrl && (
          <Image
            src={imageUrl}
            alt={title}
            width={940}
            height={470}
            className="rounded-lg object-cover w-full h-64"
          />
        )}
        <div className="font-montserrat font-semibold sm:text-[24px] text-[16px] leading-[140%] tracking-[0.02em] text-white">
          {title}
        </div>
        {children}
      </div>
    </div>
  )
}
