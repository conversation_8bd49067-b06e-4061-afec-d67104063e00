import React from 'react'
import { Input, Select } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { Category } from '@/interfaces'
import { useTranslate } from '@/hooks'
import UiButton from '@/components/ui/Button'

interface SearchFilterBarProps {
  searchValue: string
  categoryValue: string
  categories: Category[]
  onSearchChange: (value: string) => void
  onCategoryChange: (value: string) => void
  onSearch: () => void
  isLoading?: boolean
  searchPlaceholder?: string
  categoryPlaceholder?: string
  searchButtonText?: string
}

const SearchFilterBar: React.FC<SearchFilterBarProps> = ({
  searchValue,
  categoryValue,
  categories,
  onSearchChange,
  onCategoryChange,
  onSearch,
  isLoading = false,
  searchPlaceholder,
  categoryPlaceholder,
  searchButtonText,
}) => {
  const t = useTranslate()
  const categoryOptions = [
    {
      value: '',
      label: categoryPlaceholder || t.search_category,
    },
    ...categories.map((category) => ({
      value: category._id || category.name,
      label: category.name,
    })),
  ]

  return (
    <div className="flex flex-col sm:flex-row w-full gap-4 items-center justify-center mx-auto pb-8">
      {/* Search Input */}
      <div className="w-full sm:flex-1 min-w-0">
        <Input
          size="large"
          style={{ height: 44 }}
          placeholder={searchPlaceholder || t.homepage_search_placeholder}
          value={searchValue}
          onChange={(e) => onSearchChange(e.target.value)}
          suffix={<SearchOutlined rev={undefined} />}
        />
      </div>

      {/* Category Dropdown */}
      <div className="w-full sm:w-[155px] flex-shrink-0">
        <Select
          size="large"
          style={{ height: 44 }}
          value={categoryValue}
          onChange={onCategoryChange}
          options={categoryOptions}
          placeholder={categoryPlaceholder || t.search_category}
          className="w-full"
        />
      </div>

      {/* Search Button */}
      <div className="w-full sm:w-[150px] flex-shrink-0">
        <UiButton
          title={searchButtonText || t.search_button}
          isDisabled={isLoading}
          isFilled={true}
          handleClick={onSearch}
          className="h-[44px] sm:w-[150px] w-full"
        />
      </div>
    </div>
  )
}

export default SearchFilterBar
