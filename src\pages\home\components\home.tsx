import React, { memo } from 'react'
import Head from 'next/head'
import { pageHeadConfigDefault } from '@/constants'
import HomePage from './HomePage'

const Home = () => {
  const googleSiteVerification =
    process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION

  return (
    <>
      <Head>
        <title>GLITTERS - NFT Marketplace</title>
        <meta name="description" content={pageHeadConfigDefault.description} />
        <meta name="keywords" content={pageHeadConfigDefault.keywords} />
        <meta property="og:title" content="GLITTERS - NFT Marketplace" />
        <meta
          property="og:description"
          content={pageHeadConfigDefault.description}
        />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="/images/og-image.jpg" />
        {googleSiteVerification && (
          <meta
            name="google-site-verification"
            content={googleSiteVerification}
          />
        )}
      </Head>
      <HomePage />
    </>
  )
}

export default memo(Home)
