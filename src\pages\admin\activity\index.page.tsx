import React, { useState } from 'react'
import { ListActivityItem } from '@/components/list-item/ListActivityItem'
import { getListActivity } from '@/services/apiCall/activity'
import { getActivityContent } from '@/utils/activity'
import { useQuery, keepPreviousData } from '@tanstack/react-query'
import type { Activity, PaginatedActivity } from '@/interfaces'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import { TextAdminTitle } from '@/components/texts/TextAdminTitle'
import { DEFAULT_PAGE_SIZE } from '@/constants'
import { UiPagination } from '@/components/ui/Pagination'

export default function Activity() {
  const [pageIndex, setPageIndex] = useState<number>(1)

  const { data, isLoading, isError } = useQuery<PaginatedActivity>({
    queryKey: ['admin-activity', pageIndex, DEFAULT_PAGE_SIZE],
    queryFn: () => getListActivity({ pageSize: DEFAULT_PAGE_SIZE, pageIndex }),
    placeholderData: keepPreviousData,
  })

  const text = {
    adminAccountManagement: 'アカウント管理',
    allActivity: 'すべてのアクティビティ',
    failedToGetActivity: 'アクティビティの取得に失敗しました',
    noActivity: 'アクティビティがありません',
  }

  return (
    <div>
      <TextAdminHeader title={text.adminAccountManagement} />
      <TextAdminTitle title={text.allActivity} />
      <div>
        {isLoading ? (
          <div className="text-center py-4">Loading...</div>
        ) : isError ? (
          <div className="text-center py-4">{text.failedToGetActivity}</div>
        ) : !data || !data.items || data.items.length === 0 ? (
          <div className="text-center py-4">{text.noActivity}</div>
        ) : (
          data.items.map((item) => (
            <ListActivityItem
              key={item._id}
              title={getActivityContent(item) || ''}
              createdAt={item.createdAt}
              className="mb-6 text-white"
            />
          ))
        )}
      </div>
      <div className="mt-8 flex justify-center mb-10">
        <UiPagination
          className="custom-pagination"
          current={pageIndex}
          pageSize={DEFAULT_PAGE_SIZE}
          total={data?.totalItems || 0}
          onChange={(page) => setPageIndex(page)}
          showSizeChanger={false}
        />
      </div>
    </div>
  )
}
