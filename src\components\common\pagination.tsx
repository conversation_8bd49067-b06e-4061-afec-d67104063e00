import { useRouter } from 'next/router'
import { memo, useEffect, useState } from 'react'
import { HiChevronLeft, HiChevronRight } from 'react-icons/hi'
import ReactPaginate from 'react-paginate'

interface IPaginationProps {
  totalCount: number
  onPageChange: (page: number) => void
}

const Pagination: React.FC<IPaginationProps> = ({
  onPageChange,
  totalCount,
}) => {
  const { query } = useRouter()
  const [page, setPage] = useState<string | string[] | undefined>('1')

  useEffect(() => {
    if (query.page) {
      setPage(query.page)
    } else {
      setPage('1')
    }
  }, [query.page])

  return (
    <>
      {totalCount > 1 && (
        <div className="w-full flex justify-center items-center text-tailwindNeutral1 max-sm:text-sm ">
          <ReactPaginate
            className="flex gap-2"
            previousLabel={<HiChevronLeft className="h-6 w-6" />}
            nextLabel={<HiChevronRight className="h-6 w-6 " />}
            pageClassName="page-item "
            pageLinkClassName="relative  block py-2 px-3 bg-white border border-tailwindNeutral3 hover:text-tailwindBrand1  hover:opacity-80 rounded-md w-11 h-11 text-center cursor-pointer"
            previousClassName="page-item"
            previousLinkClassName="relative h-full flex items-center justify-center block py-2 px-3 bg-white border border-tailwindNeutral3 hover:text-tailwindBrand1  hover:opacity-80 rounded-md w-11 h-11 text-center cursor-pointer"
            nextClassName="page-item "
            nextLinkClassName="relative h-full flex items-center justify-center block py-2 px-3 bg-white border border-tailwindNeutral3 hover:text-tailwindBrand1  hover:opacity-80 rounded-md w-11 h-11 text-center cursor-pointer"
            breakLabel="..."
            breakClassName="page-item"
            breakLinkClassName="relative block py-2 px-3 bg-white border border-tailwindNeutral3 hover:text-tailwindBrand1  hover:opacity-80 rounded-md w-11 h-11 text-center cursor-pointer"
            pageCount={totalCount}
            marginPagesDisplayed={2}
            pageRangeDisplayed={3}
            onPageChange={(e) => onPageChange(e.selected)}
            containerClassName="flex list-reset pl-0 rounded-md"
            activeLinkClassName="!border-[#F2B516] text-tailwindBrand1 border-2"
            forcePage={Number(page) - 1}
          />
        </div>
      )}
    </>
  )
}

export default memo(Pagination)
