import React from 'react'
import { TNewsItem } from '@/interfaces'
import NewsCard from '@/components/homepage/NewsCard'
import { useTranslate } from '@/hooks'
import Link from 'next/link'

interface MarketplaceInsightsProps {
  news: TNewsItem[]
  isLoading?: boolean
  className?: string
}

const MarketplaceInsights: React.FC<MarketplaceInsightsProps> = ({
  news,
  isLoading = false,
  className = '',
}) => {
  const t = useTranslate()
  const MAX_NEWS_VISIBLE = 3

  if (isLoading) {
    return (
      <div className={`p-6 ${className}`}>
        <h3 className="font-montserrat font-semibold leading-[170%] tracking-[0.02em] text-white text-lg mb-6">
          {t.insights_title}
        </h3>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="flex gap-4 p-4 rounded-lg animate-pulse"
            >
              <div className="w-20 h-20 bg-neutral-600 rounded-md flex-shrink-0" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-neutral-600 rounded w-3/4" />
                <div className="h-3 bg-neutral-600 rounded w-full" />
                <div className="h-3 bg-neutral-600 rounded w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`pt-2 ${className}`}>
      <h3 className="text-white font-semibold text-xl mb-6 border-1 border-b border-[#2f2f2f] pb-3">
        {t.insights_title}
      </h3>

      {news.length > 0 ? (
        <div className="flex flex-col space-y-4">
          {news.slice(0, 3).map((item) => (
            <NewsCard key={item._id} news={item} />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="text-gray-400 text-sm mb-2">
            {t.insights_noInsights}
          </div>
          <p className="text-gray-500 text-xs">
            {t.insights_noInsightsSubtext}
          </p>
        </div>
      )}
      {news.length > MAX_NEWS_VISIBLE && (
        <div className="mt-4 text-primary text-sm font-montserrat text-center">
          <Link href="/news">{t.homepage_view_all_insights}</Link>
        </div>
      )}
    </div>
  )
}

export default MarketplaceInsights
