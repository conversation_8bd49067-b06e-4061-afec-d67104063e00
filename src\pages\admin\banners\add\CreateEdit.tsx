import React, { useEffect, useMemo, useState } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { useRouter } from 'next/router'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-toastify'
import { useMutation, useQuery } from '@tanstack/react-query'
import TextInformation from '@/components/texts/TextInformation'
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES } from '@/constants/enum'
import TextError from '@/components/texts/TextError'
import {
  createBanner,
  uploadBannerImage,
  getBannerDetail,
  updateBanner,
  approvedRejectBanner,
} from '@/services/apiCall/banner'
import {
  NO_CACHING_TIME,
  ROUTES,
  ROLE,
  ACTION_TYPE,
  APPROVAL_STATUS,
} from '@/constants'
import { BUTTON, MESSAGE_FORM } from '@/utils/string'
import InputImage from './InputImage'
import { bannerSchema, defaultValues } from './until'
import { TBanner } from '@/interfaces/banner'
import { getPermission, removeValueFromObject } from '@/utils'
import { useStore } from '@/store'
import AntdFormItem from '@/components/antdCustomComponent/formItem'
import { Form, Input, Select } from 'antd'
import { getCollectionsForUser } from '@/services/apiCall'

type TCreateEditAccountProps = {
  type: 'createBanner' | 'editBanner' | 'approveBanner'
}

const { TextArea } = Input

const TEXT_LABELS = {
  BANNER: 'バナー',
  TITLE: 'タイトル',
  INTRODUCTION: '紹介文',
  COLLECTION: 'コレクション',
  EXTERNAL_LINK: '外部リンク',
  CREATE: '作成',
  SAVE_DRAFT: 'ドラフトを保存',
  BANNER_TITLE: 'バナータイトル',
  ENTER_INTRODUCTION: 'はじめに入力',
  ENTER_EXTERNAL_LINK: 'はじめに入力',
  APPROVE: '承認',
  REJECT: '却下済み',
  MUST_BE_PROVIDED_BUT_NOT_BOTH:
    '外部リンクまたはコレクションのいずれかを提供する必要がありますが、両方を提供することはできません。',
  CREATE_FAILED: 'バナーを作成できませんでした。もう一度お試しください。',
  UPDATE_SUCCESS: '更新成功',
  CREATE_NEW_BANNER: '新しいバナーを作成',
  EDIT_BANNER: 'バナーを編集',
  BANNER_DETAIL: 'バナー詳細',
}

function CreateEditBanner(props: TCreateEditAccountProps) {
  const { type } = props
  const [imageUrl, setImageUrl] = useState<string>('')
  const { setLoading, role } = useStore()
  const [isLoadingUploadBanner, setLoadingUploadBanner] =
    useState<boolean>(false)
  const router = useRouter()
  const bannerId = router?.query?.id as string

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
    setValue,
    clearErrors,
    setFocus,
  } = useForm({
    defaultValues,
    resolver: yupResolver(bannerSchema),
    mode: 'onChange',
  })

  const { isPending: isLoadingCreateBanner, mutate: mutateCreateBanner } =
    useMutation({
      mutationFn: (data: TBanner) =>
        createBanner(data, (role as ROLE.systemAdmin) || ROLE.operator),
      onSuccess: () => {
        toast.success(MESSAGE_FORM.successCreateBanner)
        router.push(ROUTES.adminBanners)
        setLoading(false)
        reset()
      },
      onError: () => {
        setLoading(false)
        toast.error(TEXT_LABELS.CREATE_FAILED)
      },
    })

  const { isPending: isLoadingEditBanner, mutate: mutateEditBanner } =
    useMutation({
      mutationFn: (data: TBanner) =>
        updateBanner({
          body: data,
          bannerId,
          role: (role as ROLE.systemAdmin) || ROLE.operator,
        }),
      onSuccess: () => {
        toast.success(MESSAGE_FORM.successCreateBanner)
        router.push(ROUTES.adminBanners)
        setLoading(false)
      },
    })

  const { data: bannerData, isSuccess: getBannerSuccess } = useQuery({
    queryKey: ['get_banner_detail'],
    queryFn: (): Promise<TBanner> => getBannerDetail(bannerId),
    enabled: !!bannerId && (type === 'editBanner' || type === 'approveBanner'),
    gcTime: NO_CACHING_TIME,
  })

  const { isPending: isApprovalStatus, mutate: mutateApprovalStatus } =
    useMutation({
      mutationFn: (
        approvalStatus: ACTION_TYPE.APPROVED | ACTION_TYPE.REJECTED
      ) =>
        approvedRejectBanner({
          approvalStatus,
          bannerId,
          role: (role as ROLE.systemAdmin) || ROLE.approver,
        }),
      onSuccess: () => {
        toast.success(TEXT_LABELS.UPDATE_SUCCESS)
        router.push(ROUTES.adminBanners)
        setLoading(false)
      },
    })

  const { data: listCollections = { items: [], totalItems: 0 } } = useQuery({
    queryKey: ['list-collections'],
    queryFn: () =>
      getCollectionsForUser({
        pageIndex: 1,
        pageSize: 100,
      }),
  })
  const collectionOptions = useMemo(() => {
    const mapData = listCollections?.items?.map((item) => {
      return { label: item?.name, value: item?._id }
    })
    return mapData
  }, [listCollections])

  const uploadBanner = async () => {
    try {
      setLoadingUploadBanner(true)
      const data = await uploadBannerImage(imageUrl, ROLE.systemAdmin)
      setLoadingUploadBanner(false)
      return data
    } catch (error) {
      setLoadingUploadBanner(false)
      setLoading(false)
    }
  }

  const onCreateBanner = async (data, callBack, status) => {
    try {
      setLoading(true)

      const newBannerImage = imageUrl.includes('base64')
        ? await uploadBanner()
        : ''

      const newData = {
        ...data,
        imageId: newBannerImage,
        approvalStatus: status,
      } as TBanner

      await callBack(removeValueFromObject({ obj: newData, valueRemove: '' }))
    } catch (error) {
      console.error('Banner creation error:', error)
      toast.error(TEXT_LABELS.CREATE_FAILED)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (getBannerSuccess && collectionOptions.length > 0) {
      reset({
        collection: (bannerData?.collection as string) || '',
        externalLink: bannerData?.externalLink || '',
        description: bannerData?.description || '',
        imageId: bannerData?.image || '',
        title: bannerData?.title || '',
      })

      setImageUrl(bannerData?.image)
    }
  }, [bannerData, getBannerSuccess, collectionOptions, reset])

  const handleChangeImage = (bannerImage) => {
    setImageUrl(bannerImage)
    setValue('imageId', bannerImage, { shouldDirty: true })
    clearErrors('imageId')
  }

  useEffect(() => {
    const [firstError] = Object?.keys(errors) || null
    if (firstError) {
      document
        .getElementById(firstError)
        ?.scrollIntoView({ behavior: 'smooth' })
    }
  }, [errors, setFocus])

  const onSubmitWithStatus = async (status?: APPROVAL_STATUS) => {
    await handleSubmit((bannerData) => {
      onCreateBanner(bannerData, mutateCreateBanner, status)
    })()
  }

  const { canCreate, canEdit, canApprove } = getPermission({
    approvalStatus: bannerData?.approvalStatus as APPROVAL_STATUS,
    creatorId: (bannerData?.createdBy as string) || '',
  })

  const isEdit = type === 'editBanner' && canEdit
  const isCreate = type === 'createBanner' && canCreate
  const isApprove = type === 'approveBanner' && canApprove
  const isDisabled = !isEdit && !isCreate

  return (
    <>
      <div className="min-w-md overflow-auto">
        <div className="text-white my-4 font-semibold text-2xl pb-2 border-b border-[#FFFFFF26]">
          {type === 'editBanner'
            ? TEXT_LABELS.EDIT_BANNER
            : type === 'createBanner'
            ? TEXT_LABELS.CREATE_NEW_BANNER
            : TEXT_LABELS.BANNER_DETAIL}
        </div>
        <Form className="flex gap-12 mt-6 mb-16">
          <div className="flex-1">
            <TextInformation title={TEXT_LABELS.BANNER} require />
            <div className="mt-0">
              <Controller
                name="imageId"
                control={control}
                render={() => (
                  <>
                    <InputImage
                      disabled={isDisabled}
                      handleChangeImage={handleChangeImage}
                      imageUrl={imageUrl}
                    />
                    <TextError title={errors?.imageId?.message} />
                  </>
                )}
              />
            </div>
          </div>
          {/* left side */}
          <div className="flex-1 flex flex-col gap-4">
            <div className="mt-1">
              <div>
                <TextInformation title={TEXT_LABELS.TITLE} require />
              </div>
              <AntdFormItem control={control} name="title" className="mt-6">
                <Input
                  type="text"
                  placeholder={TEXT_LABELS.BANNER_TITLE}
                  className="border !border-customColor4 !h-[44px]"
                  disabled={isDisabled}
                  maxLength={100}
                  showCount
                  allowClear
                />
              </AntdFormItem>
            </div>
            <div>
              <TextInformation title={TEXT_LABELS.INTRODUCTION} require />
              <AntdFormItem control={control} name="description">
                <TextArea
                  placeholder={TEXT_LABELS.INTRODUCTION}
                  className="border !border-customColor4"
                  rows={4}
                  disabled={isDisabled}
                  maxLength={1000}
                  showCount
                  allowClear
                />
              </AntdFormItem>
            </div>
            <div>
              {(type === 'createBanner' || type === 'editBanner') && (
                <TextError title={TEXT_LABELS.MUST_BE_PROVIDED_BUT_NOT_BOTH} />
              )}
              <TextInformation title={TEXT_LABELS.COLLECTION} require />
              <AntdFormItem control={control} name="collection">
                <Select
                  placeholder="Select collection"
                  options={collectionOptions || []}
                  className="!h-[44px] truncate-select"
                  allowClear
                  disabled={isDisabled}
                />
              </AntdFormItem>
            </div>
            <div>
              <TextInformation title={TEXT_LABELS.EXTERNAL_LINK} require />
              <AntdFormItem control={control} name="externalLink">
                <Input
                  type="text"
                  placeholder={
                    type !== 'approveBanner'
                      ? TEXT_LABELS.ENTER_EXTERNAL_LINK
                      : ''
                  }
                  className="border !border-customColor4 !h-[44px]"
                  disabled={isDisabled}
                  maxLength={255}
                  showCount
                  allowClear
                />
              </AntdFormItem>
            </div>

            <div className="flex flex-col gap-3">
              {isCreate && (
                <>
                  <UiButton
                    title={TEXT_LABELS.SAVE_DRAFT}
                    size={BUTTON_SIZES.LG}
                    handleClick={() =>
                      onSubmitWithStatus(APPROVAL_STATUS.draft)
                    }
                    className="w-full"
                    isDisabled={isLoadingCreateBanner || isLoadingUploadBanner}
                    isGradient={false}
                    isBorder={true}
                  />
                  <UiButton
                    title={TEXT_LABELS.CREATE}
                    size={BUTTON_SIZES.LG}
                    isGradient={true}
                    handleClick={() => onSubmitWithStatus()}
                    className="w-full"
                    isLoading={isLoadingCreateBanner || isLoadingUploadBanner}
                    isDisabled={isLoadingCreateBanner || isLoadingUploadBanner}
                  />
                </>
              )}
              {isEdit && (
                <>
                  <UiButton
                    title={TEXT_LABELS.SAVE_DRAFT}
                    size={BUTTON_SIZES.LG}
                    handleClick={handleSubmit((data) =>
                      onCreateBanner(data, mutateEditBanner, 'draft')
                    )}
                    className="w-full"
                    isDisabled={isLoadingEditBanner || isLoadingUploadBanner}
                    isGradient={false}
                    isBorder={true}
                  />
                  <UiButton
                    title={BUTTON.save}
                    size={BUTTON_SIZES.LG}
                    isGradient={true}
                    handleClick={handleSubmit((data) =>
                      onCreateBanner(
                        data,
                        mutateEditBanner,
                        APPROVAL_STATUS.waiting
                      )
                    )}
                    className="w-full"
                    isLoading={isLoadingEditBanner || isLoadingUploadBanner}
                    isDisabled={isLoadingEditBanner || isLoadingUploadBanner}
                  />
                </>
              )}
              {isApprove && (
                <>
                  <UiButton
                    title={TEXT_LABELS.REJECT}
                    size={BUTTON_SIZES.LG}
                    handleClick={() =>
                      mutateApprovalStatus(ACTION_TYPE.REJECTED)
                    }
                    className="w-full"
                    isDisabled={isApprovalStatus}
                    isGradient={false}
                    isBorder={true}
                  />
                  <UiButton
                    title={TEXT_LABELS.APPROVE}
                    size={BUTTON_SIZES.LG}
                    isGradient={true}
                    handleClick={() =>
                      mutateApprovalStatus(ACTION_TYPE.APPROVED)
                    }
                    className="w-full"
                    isDisabled={isApprovalStatus}
                  />
                </>
              )}
            </div>
          </div>
        </Form>
      </div>
    </>
  )
}

export default CreateEditBanner
