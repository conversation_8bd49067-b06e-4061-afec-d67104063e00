# NFT Marketplace Dashboard

This dashboard provides a visual overview of key performance metrics for an NFT marketplace.  
It is designed in a **dark mode** style with a clean and minimal layout.

---

## **Dashboard Components**

The layout is organized into a **2x2 grid**, each card containing a line chart with labels and descriptions.

### **1. Items Sold**

- **Title:** `Items Sold`
- **Description:** Number of NFTs sold over time.
- **Data:** Yellow line chart showing the number of NFTs sold between 5/27 and 5/31.
- **X-axis:** Dates (`5/27` – `5/31`)
- **Y-axis:** Quantity of NFTs (`0 – 10k`)

### **2. Total Revenue**

- **Title:** `Total Revenue`
- **Description:** Revenue generated from NFT sales.
- **Data:** Yellow line chart showing revenue performance over time.
- **X-axis:** Dates (`5/27` – `5/31`)
- **Y-axis:** Revenue (`0 – 10k`)

### **3. Conversion Rate (CVR)**

- **Title:** `Conversion Rate (CVR)`
- **Description:** User conversion rate to buyers.
- **Data:** Yellow line chart showing conversion percentage.
- **X-axis:** Dates (`5/27` – `5/31`)
- **Y-axis:** Conversion rate (`0% – 2%`)

### **4. Number of Users**

- **Title:** `Number of Users`
- **Description:** Total active users on the marketplace.
- **Data:** Yellow line chart showing active user count.
- **X-axis:** Dates (`5/27` – `5/31`)
- **Y-axis:** Users (`0 – 10k`)

---

## **Design & Styling**

### **Color Scheme**

| Element              | Color Code              | Notes                              |
| -------------------- | ----------------------- | ---------------------------------- |
| Dashboard background | `#0D0D0D`               | Dark mode background               |
| Card background      | `#121212`               | Slightly lighter than dashboard BG |
| Chart line           | `#F5B83B`               | Bright yellow for high contrast    |
| Title text           | `#FFFFFF`               | White                              |
| Description text     | `#A1A1A1`               | Light gray                         |
| Axis & grid lines    | `rgba(255,255,255,0.2)` | Subtle grid lines                  |

### **Typography**

- **Font:** Sans-serif (e.g., `Inter`, `Roboto`, or `Arial`)
- **Title font size:** `16px – 18px`, bold
- **Description font size:** `12px – 14px`, normal

### **Layout**

- **Grid:** 2 rows × 2 columns
- **Gap between cards:** `16px – 20px`
- **Card border radius:** `12px – 16px`
- **Card padding:** `16px`
- **Chart height:** Around `250px`

---

## **CSS Example**

```css
.dashboard {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  background-color: #0d0d0d;
  padding: 20px;
}

.card {
  background-color: #121212;
  border-radius: 16px;
  padding: 16px;
  color: #fff;
}

.card h2 {
  font-size: 18px;
  font-weight: bold;
}

.card p {
  font-size: 14px;
  color: #a1a1a1;
}

.chart-line {
  stroke: #f5b83b;
  stroke-width: 2px;
}

.axis line,
.axis path {
  stroke: rgba(255, 255, 255, 0.2);
}
```
