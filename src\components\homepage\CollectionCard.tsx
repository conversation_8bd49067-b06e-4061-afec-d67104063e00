import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Category, Collection } from '@/interfaces'
import { FALLBACK_IMAGE } from '@/constants'

interface CollectionCardProps {
  collection: Collection
  categories?: Category[]
  className?: string
}

const CollectionCard: React.FC<CollectionCardProps> = ({
  collection,
  // categories,
  className = '',
}) => {
  // const categoryMap = new Map<string, string>(
  //   categories.map(category => [category._id || '', category.name || ''])
  // )
  return (
    <Link href={`/collections/${collection._id}`}>
      <div
        className={`group overflow-hidden bg-getCardBg rounded-lg ${
          className || ''
        }`}
      >
        {/* Collection Image */}
        <div className="relative flex-shrink-0 aspect-square overflow-hidden">
          <Image
            src={collection.logoImage || FALLBACK_IMAGE}
            alt={collection.name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110 p-6 rounded-md"
          />
        </div>

        {/* Collection Info */}
        <div className="px-6 pb-6">
          <h3 className="text-white font-semibold text-lg mb-2 truncate tracking-[0.02em]">
            {collection.name}
          </h3>
          <div className="flex items-center justify-between">
            <span className="text-neutral-300 font-medium text-sm">
              Floor Price: ${collection?.floorPrice || 0} GET
            </span>
            {/* <span className="text-neutral-500 text-xs">
              {categoryMap.get(collection.category) || ''}
            </span> */}
          </div>
        </div>
      </div>
    </Link>
  )
}

export default CollectionCard
