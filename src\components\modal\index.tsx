import { Col, Modal, Modal<PERSON>rops, Row } from 'antd'
import Image from 'next/image'
import React from 'react'
import Link from 'next/link'
import { LoadingOutlined } from '@ant-design/icons'
import clsx from 'clsx'

import ButtonComponent from '../buttons/Button'
import { IconCompleted, IconFailed, IconWarning } from '@/icons'
import { formatTransaction } from '@/utils'
import { IMAGE } from '@/utils/string'

export type TTransferStatus = 'processing' | 'fail' | 'completed'

interface ModalComponentProps extends ModalProps {
  title?: string
  setOpenModal: (boolean: boolean) => void
  type?: 'nft' | 'warning'
  params?: {
    imageSrc?: string
    type?: TTransferStatus
    transactionHash?: string
  }
  onComplete: () => void
  onError: () => void
  successTitle?: string
  objTypeNftButtonFailTitle?: string
  errorText?: string
}

const ModalComponent = (props: ModalComponentProps) => {
  const {
    onComplete,
    onError,
    title,
    params,
    successTitle,
    errorText,
    objTypeNftButtonFailTitle = 'Claim nftの参照',
    setOpenModal,
    type,
  } = props

  const objTypeNft = {
    processing: {
      icon: (
        <LoadingOutlined
          rev={'loading'}
          className="text-tailwindBlue text-base mr-3"
        />
      ),
      text: (
        <span className="text-tailwindBlue text-base font-bold">処理中</span>
      ),
      button: '処理中',
      func: () => onCancel,
    },
    completed: {
      icon: IconCompleted({}),
      text: (
        <span className="text-tailwindCompleted text-base font-bold">完了</span>
      ),
      button: 'My NFTsの参照',
      func: () => onComplete(),
    },
    fail: {
      icon: IconFailed({}),
      text: (
        <span className="text-tailwindFailed text-base font-bold">失敗</span>
      ),
      button: objTypeNftButtonFailTitle,
      func: () => onError(),
    },
  }

  const renderNft = () => (
    <div className="flex flex-col items-center 2xl:px-[55px] px-[20px]">
      <h2 className="text-tailwindNeutral1 text-2xl font-bold mb-5">{title}</h2>
      <Image
        src={params?.imageSrc || IMAGE.defaultImage}
        alt="nft image"
        width={360}
        height={200}
        className="object-contain rounded-md h-[200px] w-[360px]"
      />
      <Row align={'middle'} justify={'space-between'} className="w-full mt-5">
        <Col xxl={11} xs={10}>
          <span className="text-tailwindNeutral3 text-xs font-normal">
            ステータス
          </span>
          <Row align={'middle'}>
            {objTypeNft[params?.type || 'processing'].icon}
            {objTypeNft[params?.type || 'processing'].text}
          </Row>
        </Col>
        <Col xxl={10} xs={14}>
          <span className="text-tailwindNeutral3 text-xs font-normal">
            トランザクションハッシュ
          </span>
          {params?.transactionHash ? (
            <Link
              href={`${process.env.NEXT_PUBLIC_POLYGON_BLOCK_EXPLORER_URL}/tx/${params?.transactionHash}`}
              target="_blank"
            >
              <span className="text-tailwindNeutral1 text-base font-bold hover:text-blue-500">
                {formatTransaction(params?.transactionHash as string)}
              </span>
            </Link>
          ) : (
            <div className="py-[2px]">―</div>
          )}
        </Col>
      </Row>
      {params?.type === 'processing' && (
        <span className="text-tailwindFailed text-[14px] mt-2 font-bold">
          ※処理中のため、ブラウザを更新しないでください。
        </span>
      )}
      {params?.type === 'completed' && (
        <span className="text-tailwindFailed text-[11px] mt-2 font-bold">
          {successTitle}
        </span>
      )}
      {params?.type === 'fail' && (
        <span className="text-tailwindFailed text-[11px] mt-2 font-bold">
          {errorText}
        </span>
      )}
      <div className="mt-5 ">
        <ButtonComponent
          title={objTypeNft[params?.type || 'processing'].button}
          type="primary"
          className={clsx('w-[172px] text-white', {
            '!opacity-70': params?.type === 'processing',
          })}
          onClick={objTypeNft[params?.type || 'processing'].func}
          disabled={params?.type === 'processing'}
        />
      </div>
    </div>
  )

  const renderWarning = () => (
    <div className="flex flex-col items-center px-[55px] mt-8">
      {<IconWarning />}
      <h2 className="text-tailwindNeutral1 text-2xl font-bold font-sans text-center mt-8 mb-3">
        リンクをクリックしてメタマスクをインストールしてください。!
      </h2>
      <Link
        href={'https://metamask.io/'}
        className="text-tailwindBlue text-base underline mb-5"
      >
        https://metamask.io/
      </Link>
      <ButtonComponent title="Ok" type="primary" />
    </div>
  )

  const onCancel = () => {
    setOpenModal(false)
  }

  const objModal = {
    nft: {
      element: renderNft,
    },
    warning: {
      element: renderWarning,
    },
  }

  return (
    <Modal open={props.open} footer={false} closable={false}>
      {type ? objModal[type || 'warning']?.element() : props.children}
    </Modal>
  )
}

export default ModalComponent
