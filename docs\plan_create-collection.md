# Admin Create Collection Page – Implementation Plan

## 1. Overview

**Purpose:**  
Implement an admin page for creating a new NFT collection. This page allows admins to input collection details (logo, background, contract type, name, category, description), upload images, and save or create the collection.

**Scope:**

- Affects the admin section of the app (`/admin/create-collection`)
- Uses existing UI components (Button, Input, Select, etc.) from `@/components/ui`
- Integrates Ant Design’s Form for validation and layout
- Follows the design in `docs/layouts/create-collection-layout.png`

---

## 2. Example

**Wireframe:**  
See `docs/layouts/create-collection-layout.png` for the intended UI.

**Example JSX Skeleton:**

```tsx
import AdminLayout from '@/components/layouts/adminLayout'
import { Form, Input, Button, Upload, Select, Radio } from 'antd'
import UiButton from '@/components/ui/Button'
import UiInput from '@/components/ui/Input'
import UiSelect from '@/components/ui/Select'

export default function CreateCollectionPage() {
  return <AdminLayout>{/* Form layout here */}</AdminLayout>
}
```

---

## 3. Flow

- **Layout:**  
  Uses `AdminLayout` for consistent admin UI.
- **Form:**  
  Ant Design’s `Form` for state, validation, and layout.  
  Custom UI components (`@/components/ui`) for inputs, selects, and buttons.
- **Image Upload:**  
  Use Ant Design’s `Upload` for logo/background images.
- **Contract Type:**  
  Radio group for selecting contract type (ERC-721, ERC-1155).
- **Category:**  
  Dropdown select (fetch or static options).
- **Actions:**  
  “Save Draft” and “Create” buttons.
- **State Management:**  
  Local state for form, images, and submission status.
- **API:**  
  On submit, call backend API to create collection.
- **Routing:**  
  Page at `/admin/create-collection`.
- **Validation:**  
  Required fields: logo, contract name, category, description.

---

## 4. Version Note

- **Next.js:** 13.x (Pages Router)
- **TypeScript:** Yes
- **UI:** Tailwind CSS, Ant Design Form, custom UI components
- **State:** Local (form), Zustand (if needed for global state)
- **Plan Action:**  
  Create a new admin page for collection creation, using Ant Design Form and existing UI components. No breaking changes.

---

## 5. API Spec

**Endpoint:**  
`POST /v1/collections`

**Request:**

```ts
interface CreateCollectionRequest {
  logo: File | string // image file or URL
  background?: File | string
  contractType: 'ERC-721' | 'ERC-1155'
  name: string
  category: string
  description: string
  // ...other fields as needed
}
```

**Response:**

```ts
interface CreateCollectionResponse {
  id: string
  success: boolean
  message?: string
}
```

---

## 6. Summary List of Files

### Files to Create

- [ ] `src/pages/admin/create-collection/index.page.tsx` – Main page
- [ ] `src/pages/admin/create-collection/CreateCollectionForm.tsx` – Form component

### Files to Modify

- [ ] `src/constants/routes.ts` – Add `/admin/create-collection`
- [ ] `src/services/apiCall/collections.ts` – Add `createCollection` API call
- [ ] `public/locales/ja/common.json` – Add translations
- [ ] `public/locales/en/common.json` – (if English supported)

---

## Implementation Steps

1. **Create Form Component:**
   - `CreateCollectionForm.tsx` with Ant Design Form, using UI components for fields.
2. **Build Page:**
   - `index.page.tsx` wraps form in `AdminLayout`.
3. **Add Route:**
   - Update `src/constants/routes.ts` with new route.
4. **API Integration:**
   - Add `createCollection` function in `src/services/apiCall/collections.ts`.
5. **Image Upload:**
   - Use Ant Design `Upload` for logo/background, handle file state.
6. **Validation:**
   - Set required fields, show errors.
7. **Actions:**
   - Implement “Save Draft” and “Create” (API call, feedback).
8. **Translations:**
   - Add new text to `public/locales/ja/common.json` (and `en/common.json` if needed).
9. **Styling:**
   - Use Tailwind CSS for layout, match design in `create-collection-layout.png`.
10. **Accessibility:**
    - Ensure ARIA, alt text, keyboard navigation.
11. **Testing:**
    - Add/modify tests if applicable.
12. **Docs:**
    - Update `architecture.md` if needed.

---

## 7. Other Notes

- **Internationalization:**  
  Add all new UI text to translation files.
- **Styling:**  
  Use Tailwind CSS and Ant Design’s layout utilities.
- **Accessibility:**  
  Ensure all form fields are labeled, images have alt text, and navigation is keyboard-friendly.
- **Testing:**  
  Add tests for form validation and API integration if test framework is present.

---

## 8. Checklist

- [ ] Plan reviewed and approved
- [ ] All new/modified files listed
- [ ] API contracts confirmed
- [ ] UI/UX reviewed (matches `create-collection-layout.png`)
- [ ] Code follows project conventions
