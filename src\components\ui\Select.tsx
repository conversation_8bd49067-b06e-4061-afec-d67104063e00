import { INPUT_SIZES, INPUT_VARIANTS } from '@/constants'
import React, {
  forwardRef,
  useState,
  useRef,
  useEffect,
  ReactNode,
} from 'react'
import { UseFormRegisterReturn } from 'react-hook-form'

export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  icon?: ReactNode
  description?: string
}

interface SelectProps
  extends Omit<
    React.SelectHTMLAttributes<HTMLSelectElement>,
    'size' | 'onChange' | 'value'
  > {
  // Options
  options: SelectOption[]
  value?: any
  onChange?: (value: string | number | (string | number)[]) => void

  // Visual customization
  variant?: INPUT_VARIANTS
  size?: INPUT_SIZES

  // Content
  label?: string
  helperText?: string
  error?: string
  placeholder?: string

  // Features
  isMultiple?: boolean
  isSearchable?: boolean
  isClearable?: boolean

  // Icons and addons
  leftIcon?: ReactNode
  rightIcon?: ReactNode

  // Styling
  className?: string
  selectClassName?: string
  labelClassName?: string
  containerClassName?: string
  optionClassName?: string
  dropdownClassName?: string

  // States
  isLoading?: boolean
  isDisabled?: boolean
  isRequired?: boolean

  // React Hook Form integration
  registration?: UseFormRegisterReturn

  // Dropdown behavior
  maxHeight?: string
  searchPlaceholder?: string
  noOptionsText?: string
  loadingText?: string
}

const UiSelect = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      // Options and value
      options = [],
      value,
      onChange,

      // Variants and sizing
      variant = 'default',
      size = 'md',

      // Content
      label,
      helperText,
      error,
      placeholder = 'Select an option...',

      // Features
      isMultiple = false,
      isSearchable = false,
      isClearable = false,

      // Icons
      leftIcon,
      rightIcon,

      // Styling
      className = '',
      selectClassName = '',
      labelClassName = '',
      containerClassName = '',
      optionClassName = '',
      dropdownClassName = '',

      // States
      isLoading = false,
      isDisabled = false,
      isRequired = false,

      // React Hook Form
      registration,

      // Dropdown behavior
      maxHeight = '200px',
      searchPlaceholder = 'Search options...',
      noOptionsText = 'No options found',
      loadingText = 'Loading...',

      ...rest
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState('')
    const [selectedValues, setSelectedValues] = useState<any>(
      Array.isArray(value) ? value : value ? [value] : []
    )

    const dropdownRef = useRef<HTMLDivElement>(null)
    const searchInputRef = useRef<HTMLInputElement>(null)

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node)
        ) {
          setIsOpen(false)
          setSearchTerm('')
        }
      }

      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])

    // Focus search input when dropdown opens
    useEffect(() => {
      if (isOpen && isSearchable && searchInputRef.current) {
        searchInputRef.current.focus()
      }
    }, [isOpen, isSearchable])

    // Filter options based on search term
    const filteredOptions =
      isSearchable && searchTerm
        ? options.filter((option) =>
            option.label.toLowerCase().includes(searchTerm.toLowerCase())
          )
        : options

    const getSizeClasses = () => {
      switch (size) {
        case INPUT_SIZES.MD:
          return 'h-[2.5rem] px-3 text-sm'
        case INPUT_SIZES.LG:
          return 'h-[2.75rem] px-4 text-lg'
        default:
          return 'h-[2rem] px-3 text-base'
      }
    }

    const getVariantClasses = () => {
      const baseClasses =
        'w-full transition-all duration-200 focus:outline-none focus:ring-2 cursor-pointer'

      switch (variant) {
        case 'search':
          return `${baseClasses} bg-gray-900/50 border border-gray-700/50 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/20`
        case 'outline':
          return `${baseClasses} bg-transparent border-2 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20`
        case 'filled':
          return `${baseClasses} bg-gray-100 border border-transparent text-gray-900 placeholder-gray-500 focus:bg-white focus:border-blue-500 focus:ring-blue-500/20`
        default:
          return `${baseClasses} bg-gray-900/30 border border-gray-700/30 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/20`
      }
    }

    const getDropdownClasses = () => {
      const baseClasses =
        'absolute z-50 w-full mt-1 rounded-lg shadow-lg border max-h-60 overflow-auto'

      switch (variant) {
        case 'search':
        case 'default':
          return `${baseClasses} bg-gray-900 border-gray-700`
        case 'outline':
        case 'filled':
          return `${baseClasses} bg-white border-gray-300`
      }
    }

    const getOptionClasses = (
      option: SelectOption,
      isSelected: boolean,
      isHovered: boolean
    ) => {
      const baseClasses =
        'px-3 py-2 cursor-pointer transition-colors duration-150 flex items-center gap-2'
      const textColor =
        variant === 'outline' || variant === 'filled'
          ? 'text-gray-900'
          : 'text-white'
      const hoverColor =
        variant === 'outline' || variant === 'filled'
          ? 'hover:bg-gray-100'
          : 'hover:bg-gray-800'
      const selectedColor = isSelected
        ? variant === 'outline' || variant === 'filled'
          ? 'bg-blue-100 text-blue-900'
          : 'bg-blue-900/50 text-blue-300'
        : ''
      const disabledColor = option.disabled
        ? 'opacity-50 cursor-not-allowed'
        : ''
      const classeHover = isHovered && ''
      return `${classeHover} ${baseClasses} ${textColor} ${hoverColor} ${selectedColor} ${disabledColor} ${optionClassName}`
    }

    const handleOptionClick = (option: SelectOption) => {
      if (option.disabled) return

      let newValue: string | number | (string | number)[]

      if (isMultiple) {
        const newSelectedValues = selectedValues.includes(option.value)
          ? selectedValues.filter((v) => v !== option.value)
          : [...selectedValues, option.value]

        setSelectedValues(newSelectedValues)
        newValue = newSelectedValues
      } else {
        setSelectedValues([option.value])
        newValue = option.value
        setIsOpen(false)
        setSearchTerm('')
      }

      onChange?.(newValue)
    }

    const handleClear = (e: React.MouseEvent) => {
      e.stopPropagation()
      setSelectedValues([])
      onChange?.(isMultiple ? [] : '')
    }

    const getDisplayValue = () => {
      if (selectedValues.length === 0) return placeholder

      if (isMultiple) {
        if (selectedValues.length === 1) {
          const option = options.find((opt) => opt.value === selectedValues[0])
          return option?.label || ''
        }
        return `${selectedValues.length} selected`
      }

      const option = options.find((opt) => opt.value === selectedValues[0])
      return option?.label || ''
    }

    const selectClasses = `
    ${getSizeClasses()}
    ${getVariantClasses()}
    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : ''}
    ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${leftIcon ? 'pl-10' : ''}
    ${rightIcon || isClearable || isLoading ? 'pr-10' : 'pr-8'}
    ${selectClassName}
    flex items-center
  `.trim()

    // Icons
    const ChevronDownIcon = () => (
      <svg
        className={`w-4 h-4 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 9l-7 7-7-7"
        />
      </svg>
    )

    const SearchIcon = () => (
      <svg
        className="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
        />
      </svg>
    )

    const ClearIcon = () => (
      <svg
        className="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    )

    return (
      <div className={`w-full ${containerClassName}`}>
        {/* Label */}
        {label && (
          <label
            className={`block text-sm font-medium mb-2 ${
              variant === 'outline' || variant === 'filled'
                ? 'text-gray-700'
                : 'text-gray-300'
            } ${labelClassName}`}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Select Container */}
        <div className={`relative ${className}`} ref={dropdownRef}>
          {/* Hidden select for form compatibility */}
          <select
            ref={ref}
            multiple={isMultiple}
            value={selectedValues}
            disabled={isDisabled}
            className="sr-only"
            {...registration}
            {...rest}
          >
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none z-10">
              {leftIcon}
            </div>
          )}

          {/* Select Display */}
          <div
            className={selectClasses}
            onClick={() => !isDisabled && setIsOpen(!isOpen)}
          >
            <span
              className={`block truncate text-xs ${
                selectedValues.length === 0 ? 'text-gray-400' : ''
              }`}
            >
              {getDisplayValue()}
            </span>
          </div>

          {/* Right Icons */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 pointer-events-none">
            {/* Loading Spinner */}
            {isLoading && (
              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
            )}

            {/* Clear Button */}
            {isClearable && selectedValues.length > 0 && !isLoading && (
              <button
                type="button"
                className="text-gray-400 hover:text-gray-600 pointer-events-auto"
                onClick={handleClear}
              >
                <ClearIcon />
              </button>
            )}

            {/* Custom Right Icon or Chevron */}
            {!isLoading && (
              <div className="text-gray-400">
                {rightIcon || <ChevronDownIcon />}
              </div>
            )}
          </div>

          {/* Dropdown */}
          {isOpen && (
            <div
              className={`${getDropdownClasses()} ${dropdownClassName}`}
              style={{ maxHeight }}
            >
              {/* Search Input */}
              {isSearchable && (
                <div className="p-2 border-b border-gray-700">
                  <div className="relative">
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder={searchPlaceholder}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className={`w-full pl-8 pr-3 py-2 text-sm rounded border-0 focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                        variant === 'outline' || variant === 'filled'
                          ? 'bg-gray-50 text-gray-900 placeholder-gray-500'
                          : 'bg-gray-800 text-white placeholder-gray-400'
                      }`}
                    />
                    <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                      <SearchIcon />
                    </div>
                  </div>
                </div>
              )}

              {/* Options */}
              <div className="py-1">
                {isLoading ? (
                  <div className="px-3 py-2 text-gray-400 text-center">
                    {loadingText}
                  </div>
                ) : filteredOptions.length === 0 ? (
                  <div className="px-3 py-2 text-gray-400 text-center">
                    {noOptionsText}
                  </div>
                ) : (
                  filteredOptions.map((option) => {
                    const isSelected = selectedValues.includes(option.value)

                    return (
                      <div
                        key={option.value}
                        className={getOptionClasses(option, isSelected, false)}
                        onClick={() => handleOptionClick(option)}
                      >
                        {/* Multiple select checkbox */}
                        {isMultiple && (
                          <div
                            className={`w-4 h-4 border rounded flex items-center justify-center ${
                              isSelected
                                ? 'bg-blue-600 border-blue-600'
                                : 'border-gray-400'
                            }`}
                          >
                            {isSelected && (
                              <svg
                                className="w-3 h-3 text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                          </div>
                        )}

                        {/* Option Icon */}
                        {option.icon && (
                          <div className="flex-shrink-0">{option.icon}</div>
                        )}

                        {/* Option Content */}
                        <div className="flex-1 min-w-0">
                          <div className="truncate font-medium">
                            {option.label}
                          </div>
                          {option.description && (
                            <div className="text-xs text-gray-500 truncate">
                              {option.description}
                            </div>
                          )}
                        </div>

                        {/* Single select checkmark */}
                        {!isMultiple && isSelected && (
                          <svg
                            className="w-4 h-4 text-blue-600"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                      </div>
                    )
                  })
                )}
              </div>
            </div>
          )}
        </div>

        {/* Helper Text or Error */}
        {(helperText || error) && (
          <div
            className={`mt-1 text-sm ${
              error ? 'text-red-500' : 'text-gray-500'
            }`}
          >
            {error || helperText}
          </div>
        )}
      </div>
    )
  }
)

UiSelect.displayName = 'Select'

export default React.memo(UiSelect)
