const IS_SERVER = typeof window === 'undefined'

export const setCookie = (name: string, val: string, expireDays?: number) => {
  if (!IS_SERVER) {
    const value = val
    let expireTime = ''
    if (expireDays) {
      const date = new Date()
      date.setTime(date.getTime() + expireDays * (24 * 60 * 60 * 1000))
      expireTime = date.toUTCString()
    }

    document.cookie = `${name}=${value}; expires=${expireTime ?? ''}; path=/`
  }
}

export const getCookie = (name: string) => {
  if (!IS_SERVER) {
    const nameLenPlus = name.length + 1
    return (
      document.cookie
        .split(';')
        .map((c) => c.trim())
        .filter((cookie) => {
          return cookie.substring(0, nameLenPlus) === `${name}=`
        })
        .map((cookie) => {
          return decodeURIComponent(cookie.substring(nameLenPlus))
        })[0] || null
    )
  }
}
export const deleteCookie = (names: string[]) => {
  if (!IS_SERVER) {
    const date = new Date()

    // Set data expire in -1 day
    date.setTime(date.getTime() + -1 * 24 * 60 * 60 * 1000)

    names.map((name) => {
      document.cookie = `${name}=; expires=${date.toUTCString()}; path=/`
    })
  }
}
