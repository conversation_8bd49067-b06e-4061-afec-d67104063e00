import { IconAccount } from '@/icons'
import moment from 'moment'
import 'moment/locale/ja'

moment.locale('ja')

export const ListActivityItem = ({
  className,
  title,
  createdAt,
}: {
  className?: string
  title: string
  createdAt: Date | string
}) => (
  <div className={`${className}`}>
    <div className="flex space-x-3">
      <IconAccount width={32} height={32} />
      <p>{title}</p>
    </div>
    <p className="ml-[32px] pl-3 text-neutral-400">
      {moment(createdAt).fromNow()}
    </p>
  </div>
)
