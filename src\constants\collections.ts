export const COLLECTIONS_CONFIG = {
  // Page
  pageTitle: 'list_collections_page_title',
  breadcrumbCollections: 'breadcrumb_collections',

  // Search & Filter
  searchPlaceholder: 'collections_search_placeholder',
  categoryAll: 'category_all',
  searchButton: 'search_button',

  // Content
  noCollections: 'no_collections',
  noCollectionsSubtext: 'no_collections_subtext',
  loadingCollections: 'loading_collections',
  loadingCategories: 'loading_categories',
  collectionsCount: 'collections_count',

  // Pagination
  paginationPrevious: 'pagination_previous',
  paginationNext: 'pagination_next',
  paginationPage: 'pagination_page',
  paginationOf: 'pagination_of',
  paginationGoTo: 'pagination_go_to',
  paginationJumpTo: 'pagination_jump_to',

  // Collection details
  floorPrice: 'floor_price',
  viewCollection: 'view_collection',

  // Pagination settings
  defaultPageSize: 12,
  defaultPageIndex: 1,
} as const
