import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

import { UserDetail } from '@/interfaces'
interface UserDetailStore {
  user: UserDetail | undefined
  setUser: (user: UserDetail) => void
  removeStoreState: () => void
  isBlocked: boolean | undefined
}

export const userStateStore = create<UserDetailStore>()(
  devtools(
    persist(
      (set) => ({
        user: undefined,
        setUser: (user: UserDetail) =>
          set(() => ({
            user,
          })),
        removeStoreState: () => set(() => ({ user: undefined })),
        isBlocked: undefined,
      }),
      { name: 'userStateStore' }
    )
  )
)
