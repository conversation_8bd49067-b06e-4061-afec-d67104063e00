import NewsForm from '@/pages/admin/news/NewsForm'
import React, { useState, useEffect } from 'react'
import { Form } from 'antd'
import { updateNews, getNewsById } from '@/services/apiCall/news'
import { ROLE, API_URLS } from '@/constants'
import { toast } from 'react-toastify'
import { useStore } from '@/store'
import { post } from '@/services/apiCall/baseApi'
import { TNewsPayload, NewStatus } from '@/interfaces'
import { useMutation } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { getImageIdFromUrl } from '@/utils/uploadImages'
import { useGet } from '@/hooks/useGet'
import { usePatch } from '@/hooks/usePatch'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import { getPermission } from '@/utils'

const TEXT = {
  EDIT: 'ニュース編集',
  SAVE_DRAFT: 'ドラフトを保存',
  PLEASE_UPLOAD_THUMBNAIL: 'サムネイル画像をアップロードしてください',
  FAILED_TO_UPLOAD_THUMBNAIL: 'サムネイル画像のアップロードに失敗しました。',
}

export default function EditNews() {
  const role = useStore((state) => state.role)
  const [form] = Form.useForm()
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [imageUrl, setImageUrl] = useState<string>('')
  const [editorValue, setEditorValue] = useState('')
  const router = useRouter()
  const { id } = router.query

  // Fetch news data by ID
  const { data: newsData, isLoading: loadingNews } = useGet({
    queryKey: ['news', id],
    callback: async () => {
      if (id) {
        return await getNewsById(id)
      }
      return null
    },
  })

  const { canEdit } = getPermission({
    approvalStatus: newsData?.approvalStatus,
    creatorId: newsData?.createdBy?._id || '',
  })

  // React Query mutation for image upload
  const { mutateAsync: uploadImageMutation, isPending: isUploadingImage } =
    useMutation({
      mutationFn: async (file: File) => {
        const formData = new FormData()
        formData.append('file', file)
        const res = await post(
          API_URLS.adminNewsImage,
          formData,
          {
            headers: { 'Content-Type': 'multipart/form-data' },
          },
          role as ROLE.systemAdmin | ROLE.operator
        )
        return res
      },
      onError: () => {
        toast.error(TEXT.FAILED_TO_UPLOAD_THUMBNAIL)
      },
    })

  // usePost for update
  const { mutate } = usePatch({
    queryKey: ['updateNews', id],
    callback: (params: TNewsPayload) =>
      updateNews(
        id as string,
        params,
        role as ROLE.systemAdmin | ROLE.operator
      ),
  })

  // Populate form when newsData is loaded
  useEffect(() => {
    if (newsData) {
      form.setFieldsValue({
        title: newsData.title,
        context: newsData.context,
      })
      setEditorValue(newsData.context || '')
      setImageUrl(newsData.imageUrl || '')

      // If imageId exists, set imageFile to null and show image preview
      // NewsForm only supports imageFile, so we need to handle preview via imageUrl
      // But NewsForm does not have imageUrl prop, so we need to patch NewsForm to support imageUrl
    }
  }, [newsData, form])

  // Handle image upload
  const handleThumbnailImage = async (
    file: File | null
  ): Promise<string | null> => {
    if (file) {
      try {
        const uploadedId = await uploadImageMutation(file)
        if (!uploadedId) {
          toast.error(TEXT.FAILED_TO_UPLOAD_THUMBNAIL)
          setImageFile(null)
          return null
        }
        return uploadedId
      } catch {
        setImageFile(null)
        return null
      }
    }
    // If no new file, use existing imageId from url
    if (imageUrl) {
      return getImageIdFromUrl(imageUrl) || ''
    }
    toast.error(TEXT.PLEASE_UPLOAD_THUMBNAIL)
    return null
  }

  // Submit handler
  const handleSubmit = async (values: any, isDraft: boolean) => {
    setIsLoading(true)

    let imageId = ''
    imageId = (await handleThumbnailImage(imageFile)) || ''

    if (!imageId) {
      setIsLoading(false)
      return
    }

    if (isDraft) {
      values.approvalStatus = NewStatus.DRAFT
    }

    const payload: TNewsPayload = {
      ...values,
      imageId,
      context: editorValue,
    }

    mutate(payload, {
      onSuccess: () => {
        form.resetFields()
        setImageFile(null)
        setEditorValue('')
        setIsLoading(false)
        router.back()
      },
      onError: () => {
        setImageFile(null)
        setIsLoading(false)
      },
    })
  }

  const onFinish = async (values: any) => {
    await handleSubmit(values, false)
  }

  const onSaveDraft = async () => {
    const values = await form.getFieldsValue()
    await handleSubmit(values, true)
  }

  // Patch NewsForm to support imageUrl prop for preview
  // If NewsForm does not support imageUrl, patch it to accept imageUrl and pass newsData.imageUrl

  return (
    <>
      <TextAdminHeader title="ニュースを編集する"></TextAdminHeader>
      <div>
        <NewsForm
          form={form}
          loading={isLoading || isUploadingImage || loadingNews}
          imageFile={imageFile}
          setImageFile={setImageFile}
          editorValue={editorValue}
          setEditorValue={setEditorValue}
          onFinish={onFinish}
          onSaveDraft={onSaveDraft}
          imageUrl={newsData?.imageUrl || ''}
          setImageUrl={setImageUrl}
          saveButtonLabel={TEXT.SAVE_DRAFT}
          publishButtonLabel={TEXT.EDIT}
          disabled={!canEdit}
        />
      </div>
    </>
  )
}
