export enum ENFT_TYPE {
  all = 0,
  ふるさと納税版 = 2,
}

export enum METADATA_STATUS {
  all = '0',
  notSell = 'notSell',
  selling = 'selling',
  soldOut = 'soldOut',
}

export enum SORT_TYPE {
  lastCreated = 'LATEST_CREATED',
  oldCreated = 'OLDEST_CREATED',
}

export enum ACTION_FORM {
  add = 'add',
  edit = 'edit',
}

export enum ETYPE_INPUT {
  number = 'number',
  alphabet = 'alphabet',
  number_alphabet = 'number_alphabet',
  ban_japanese = 'ban_japanese',
}

export enum ETYPE_GIFT {
  received = 'received', //受け取った
  notReceived = 'notReceived', // 受信していない
}

export enum ETYPE_TIME_OPTION {
  yesterday = 'yesterday',
  lastWeek = 'lastWeek',
  threeMonths = 'threeMonths',
  sixMonths = 'sixMonths',
}

export enum BUTTON_SIZES {
  FIT = 'fit',
  SM = 'sm',
  NORMAL = 'normal',
  MD = 'md',
  LG = 'lg',
  XL = 'xl',
}

export enum CARD_SIZES {
  SM = 'sm',
  NORMAL = 'normal',
  MD = 'md',
  LG = 'lg',
}

export enum CARD_RATIOS {
  landscape = 'landscape',
  square = 'square',
  portrait = 'portrait',
}

export enum INPUT_VARIANTS {
  DEFAULT = 'default',
  SEARCH = 'search',
  OUTLINE = 'outline',
  FILLED = 'filled',
}
export enum INPUT_SIZES {
  SM = 'sm',
  MD = 'md',
  LG = 'lg',
}

export enum PAYMENT_STATUS {
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCEL = 'cancel',
}
