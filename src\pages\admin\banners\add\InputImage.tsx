import React from 'react'
import { DeleteOutlined } from '@ant-design/icons'
import { Button, Upload } from 'antd'
import type { UploadChangeParam } from 'antd/es/upload'
import type { RcFile, UploadFile } from 'antd/es/upload/interface'
import Image from 'next/image'
import { toast } from 'react-toastify'

import { LENGTH_BIT_IMAGE, MAX_SIZE_FILE } from '@/constants'
import { getBase64 } from '@/components/inputs'
import { MESSAGE_FORM, TYPES_IMAGE_ALLOWED } from '@/utils/string'
import { customRequestUpload } from '@/components/modal/ModalUploadFile'

const { Dragger } = Upload

type TInputImageProps = {
  handleChangeImage: (url: string) => void
  imageUrl: string
  className?: string
  disabled?: boolean
}

export const checkImageSize = (img: RcFile | File) => {
  const isMaxImageSize = img.size > MAX_SIZE_FILE * LENGTH_BIT_IMAGE

  isMaxImageSize && toast.warning(MESSAGE_FORM.upload10MB)
  return !isMaxImageSize
}
export const checkImageType = (img: RcFile | File) => {
  const isTypeImage = TYPES_IMAGE_ALLOWED.includes(img.type)
  !isTypeImage && toast.warning(MESSAGE_FORM.imageLogoDifferentType)

  return isTypeImage
}

export default function InputImage(props: TInputImageProps) {
  const { handleChangeImage, imageUrl, className, disabled = false } = props

  const showImage = imageUrl
  // const showUploadImage = !imageUrl

  const beforeUpload = (file: RcFile) => {
    return checkImageType(file) && checkImageSize(file)
  }
  const onChangeDragger = async (info: UploadChangeParam<UploadFile>) => {
    if (info.file.originFileObj) {
      const image = await getBase64(info.file.originFileObj as RcFile)
      handleChangeImage(image)
    }
  }
  return (
    <div className={`w-full h-full ${className || ''}`}>
      <Dragger
        name="file"
        listType="picture"
        className={`bg-transparent border border-[#535353] rounded-md min-h-[220px] flex items-center justify-center ${
          disabled ? 'opacity-50 pointer-events-none cursor-not-allowed' : ''
        }`}
        showUploadList={false}
        beforeUpload={beforeUpload}
        onChange={(info) => onChangeDragger(info)}
        customRequest={customRequestUpload}
      >
        {showImage ? (
          <div
            className="flex items-center justify-center"
            onClick={(e) => {
              e.stopPropagation()
              e.preventDefault()
            }}
          >
            <div className="relative group">
              <Image
                alt="banner"
                src={imageUrl}
                width={564}
                height={160}
                priority
                unoptimized
                className="max-w-full max-h-[200px] object-contain rounded-md"
              />
              {!disabled && (
                <Button
                  type="primary"
                  shape="circle"
                  icon={<DeleteOutlined rev={'delete'} />}
                  onClick={(e) => {
                    e.stopPropagation()
                    e.preventDefault()
                    handleChangeImage('')
                  }}
                  className={`
                    absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity
                    ${disabled ? 'pointer-events-none opacity-30' : ''}
                  `}
                  style={{ zIndex: 10 }}
                />
              )}
            </div>
          </div>
        ) : (
          <div>
            <p className="text-center text-white-400">
              ドラッグアンドドロップ <br />
              <span className="text-yellow-500 cursor-pointer">
                またはクリックしてアップロード
              </span>
            </p>
            <p className="mt-2 text-xs text-gray-500">
              推奨サイズ: 1200x350px. ファイルタイプ:JPG、PNG、GIF形式
            </p>
          </div>
        )}
      </Dragger>
    </div>
  )
}
