import { DataTable } from './DataTable'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import NftStatistics from './NftStatistics'
import { useState } from 'react'
import { NFT_FILTER_DATA } from '@/utils/type'

const MyNFTs = () => {
  const [nftFilterData, setNftFilterData] = useState<NFT_FILTER_DATA | null>(
    null
  )
  return (
    <div className="space-y-12">
      <TextAdminHeader title="NFT管理" />
      <NftStatistics filterData={nftFilterData} />
      <DataTable setNftFilterData={setNftFilterData} />
    </div>
  )
}

export default MyNFTs
