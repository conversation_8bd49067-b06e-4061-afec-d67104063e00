export const META_MASK_ERROR_NAMES = {
  default: 'Error',
  userRejectedRequestError: 'UserRejectedRequestError',
  connectorNotFoundError: 'ConnectorNotFoundError',
}

export const nftBuyStatus = {
  nftSoldOut: 'このNFTは既に売り切れました。',
  success: 'の購入が成功しました。',
  error: 'の購入が失敗しました。',
  default: 'エラーが発生しました',
}

export const nftErrors = {
  'Metadata is not selling': nftBuyStatus.nftSoldOut,
  'Internal Server Error': nftBuyStatus.default,
  'Reach limit: Amount must be less than 9999999': nftBuyStatus.default,
}

export const ADMIN_ERR_MESSAGE = {
  'Admin with email already exists':
    'このメールアドレスの管理者は既に存在します。',
  'Admin not found': '管理者が見つかりません',
  'Admin has been blocked': '管理者はブロックされました',
}

export const ERR_MESSAGE = {
  'Internal server error': '内部サーバーエラー',
  // collection
  'Collection with this name already exists':
    'この名前のコレクションは既に存在します。',
  collection_existed: 'この名前のコレクションは既に存在します。',
  'Collection not found': 'コレクションが見つかりません',
  [`You don't have permission to edit this collection`]:
    'このコレクションを編集する権限がありません',
  'Collection cannot be edited in its current status':
    '現在の状態ではコレクションを編集できません',
  'Collection not found or cannot be approved':
    'コレクションが見つからないか承認できません',
  // news
  [`You don't have permission to edit this news`]:
    'このニュースを編集する権限がありません',
  [`Cannot update when news has been approved or is pending approval`]:
    'ニュースが承認済みまたは承認待ちの場合は更新できません',
  'News not found': 'ニュースが見つかりません',
  'News not found or cannot be approved':
    'ニュースが見つからないか承認できません',
  'Cannot delete news that is waiting for approval':
    '承認待ちまたは承認済みのニュースは削除できません',
}
