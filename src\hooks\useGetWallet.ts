import { useState, useC<PERSON>back, useEffect, useMemo } from 'react'
import { walletStore } from '@/store/slices/wallet'
import { useChromeExtension } from './useChromeExtenstion'
import { getUserNonce, getUserAccessToken } from '@/services/apiCall'
import { STORAGEKEY } from '@/services/cookies'
import { LoginParams } from '@/interfaces'
import { useCookies } from 'react-cookie'
import { CHROME_ERROR } from '@/constants'
import { useQueryClient } from '@tanstack/react-query'

export interface Signature {
  signature: string
  key: string
}

export interface ExtensionMessageResponse {
  addressEVM: string
  address: string
  signature: Signature
}

export interface UserNonceResponse {
  key_nonce: string
}

export enum CHOME_EXTENSION_EVENTS {
  CONNECT_SIGN = 'CONNECT_SIGN',
  CLOSE_SIGN = 'CLOSE_SIGN',
  SIGN_TX = 'signTx',
}

interface TransactionInputs {
  datum: InputDatum
  scriptAddress: string
  redeemer: string
}

interface InputDatum {
  amount: bigint | number
  to: string
  token: string
}

interface TransactionOutputs {
  datum: OutputDatum
  scriptAddress: string
}

interface OutputDatum {
  amount: bigint | number
  to: string
  token: string
  amount1: bigint | number
}

interface SendingAsset {
  symbol: string
  amount: bigint | number
}

export interface TransactionObject {
  inputs: TransactionInputs
  outputs: TransactionOutputs
  cbor: string
  partialSign: boolean
  gasFee: bigint | number
  sending: SendingAsset[]
}

export const WALLET_EXTENSION_RESPONSE = {
  WALLET_ADDRESS: 'WALLET_ADDRESS',
  CLOSE_SIGN: 'CLOSE_SIGN',
  SIGN_TX: 'SIGN_TX',
}

export enum WALLET_EXTENSION_EVENTS {
  CONNECT_SIGN = 'CONNECT_SIGN',
  CLOSE_SIGN = 'CLOSE_SIGN',
  SIGN_TX = 'signTx',
}

export const useGetWallet = () => {
  const extensionId = localStorage.getItem('EXTENSION_ID') || '' // build for testnet
  const isConnected = walletStore((state) => state.isConnected)
  const setIsConnected = walletStore((state) => state.setIsConnected)
  const queryClient = useQueryClient()
  const [extensionConnectionError, setExtensionConnectionError] = useState<{
    message: string
  } | null>(null)
  const [, setCookie, removeCookie] = useCookies([
    STORAGEKEY.WALLET_ADDRESS,
    STORAGEKEY.USER_ACCESS_TOKEN,
    STORAGEKEY.USER,
  ])

  const getNonce = useCallback(async (): Promise<string> => {
    try {
      const { nonce } = await getUserNonce()
      if (typeof localStorage !== 'undefined' && nonce) {
        localStorage.setItem('KEY_NONCE', nonce)
      }
      return nonce
    } catch (error) {
      const err =
        error instanceof Error ? error : new Error('Failed to get nonce')
      throw err
    }
  }, [])

  const { sendToExtension, error: chromeError } = useChromeExtension({
    extensionId,
    onMessage: (message: { type: string; payload: any }) => {
      if (message.type === WALLET_EXTENSION_RESPONSE.WALLET_ADDRESS) {
        handleWalletConnection(message.payload)
      }
    },
  })

  const setCookieUserInfo = useCallback(
    (dataAccessCookie: any, walletAddress: string) => {
      setCookie(
        STORAGEKEY.WALLET_ADDRESS,
        walletAddress,
        dataAccessCookie.accessTokenExp
      )
      setCookie(
        STORAGEKEY.USER_ACCESS_TOKEN,
        dataAccessCookie.accessToken,
        dataAccessCookie.accessTokenExp
      )
      setTimeout(() => {
        const userProfile = queryClient.getQueryData([
          'userProfile',
          walletAddress,
        ])
        setCookie(STORAGEKEY.USER, userProfile, dataAccessCookie.accessTokenExp)
      }, 300)
    },
    []
  )

  const clearStore = () => {
    localStorage?.removeItem('KEY_NONCE')
    removeCookie(STORAGEKEY.WALLET_ADDRESS)
    removeCookie(STORAGEKEY.USER_ACCESS_TOKEN)
    removeCookie(STORAGEKEY.USER)
  }

  const handleWalletConnection = useCallback(
    async (walletData: ExtensionMessageResponse) => {
      try {
        // Get nonce for the wallet address
        const nonce = localStorage.getItem('KEY_NONCE') as string
        // Prepare login parameters
        const loginParams: LoginParams = {
          walletAddress: walletData.address.toLowerCase(),
          signature: walletData.signature.signature,
          message: walletData.signature.key, // Using nonce as the message
          nonce,
          otherWallet: walletData.addressEVM,
        }
        // Get user access token
        const data = await getUserAccessToken(loginParams)
        if (data.accessToken) {
          setCookieUserInfo(data, walletData.address)
        } else {
          console.error('Failed to get access token')
          clearStore()
        }
      } catch (error) {
        console.error('Error during wallet authentication:', error)
        clearStore()
      }
    },
    []
  )

  const connectWallet = useCallback(async (): Promise<void> => {
    try {
      const nonce = await getNonce()
      await sendToExtension(WALLET_EXTENSION_EVENTS.CONNECT_SIGN, nonce)
    } catch (error) {
      console.error('Wallet connection failed:', error)
      setExtensionConnectionError({ message: error.message })
    }
  }, [])

  const sendTransaction = useCallback(async (payload: TransactionObject) => {
    await sendToExtension(WALLET_EXTENSION_EVENTS.SIGN_TX, payload)
  }, [])

  const disconnectWallet = useCallback(() => {
    setIsConnected(false)
    sendToExtension(WALLET_EXTENSION_EVENTS.CLOSE_SIGN, null)
    localStorage?.removeItem('KEY_NONCE')
    removeCookie(STORAGEKEY.WALLET_ADDRESS)
    removeCookie(STORAGEKEY.USER_ACCESS_TOKEN)
    removeCookie(STORAGEKEY.USER)
  }, [])

  const isErrorChromMessage = useMemo(() => {
    return (
      chromeError &&
      !chromeError?.message.includes(
        'The message port closed before a response'
      )
    )
  }, [chromeError])

  useEffect(() => {
    isErrorChromMessage &&
      setExtensionConnectionError({ message: CHROME_ERROR })
  }, [isErrorChromMessage])

  return {
    isConnected,
    connectWallet,
    disconnectWallet,
    sendTransaction,
    extensionConnectionError,
  }
}
