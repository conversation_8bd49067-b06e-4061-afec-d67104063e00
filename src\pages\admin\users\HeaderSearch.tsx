import React, { useState, useEffect } from 'react'
import { Input } from 'antd'
import { SearchOutlined } from '@ant-design/icons'

import { UserSearchInput } from '@/interfaces'
import { DEFAULT_PAGE_USERS_MANAGER } from '@/constants'

function HeaderSearch({
  setSearch,
}: {
  setSearch: (data: UserSearchInput) => void
}) {
  const [searchInput, setSearchInput] = useState<string>('')

  const text = {
    searchByUserNameOrWalletAddress: 'ユーザー名またはウォレットアドレスで検索',
  }

  // Handle search on input change with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearch({
        search: searchInput,
        page: DEFAULT_PAGE_USERS_MANAGER.defaultPage,
      })
    }, 500) // 500ms debounce

    return () => clearTimeout(timeoutId)
  }, [searchInput, setSearch])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value)
  }

  return (
    <div>
      <div className="flex gap-8 mt-2 pb-6">
        <Input
          value={searchInput}
          onChange={handleInputChange}
          placeholder={text.searchByUserNameOrWalletAddress}
          size="large"
          className="max-w-[576px]"
          suffix={<SearchOutlined rev={undefined} />}
          allowClear
        />
      </div>
    </div>
  )
}

export default HeaderSearch
