import NftDetail from './NftDetail'
import { ListNfts } from '@/components/collection/detail'
import { useRouter } from 'next/router'
import { useGet } from '@/hooks/useGet'
import { getNftDetail, getCollectionNftsForUser } from '@/services/apiCall'
import Breadcrumbs from '@/components/breadcrumbs/Breadcrumbs'
import { useTranslate } from '@/hooks/useTranslate'
import { useEffect, useMemo } from 'react'

export default function NFTPage() {
  const trans = useTranslate()
  const router = useRouter()
  const { collectionId, nftId } = router.query

  const isReady = useMemo<boolean>(() => {
    return (
      router.isReady &&
      typeof collectionId === 'string' &&
      typeof nftId === 'string' &&
      !!collectionId &&
      !!nftId
    )
  }, [collectionId, nftId])

  // Get specific NFT detail
  const {
    data: nftData,
    refetch: refetchDetail,
    isLoading,
    isError,
  } = useGet({
    queryKey: ['nftDetail', nftId],
    callback: () => getNftDetail(nftId as string),
    enabled: isReady,
  })

  // Get collection NFTs for "You may also like" section
  const { data: collectionNfts, isLoading: collectionLoading } = useGet({
    queryKey: ['nftMetadata', collectionId],
    callback: () => getCollectionNftsForUser({}, collectionId as string),
    enabled: isReady,
  })

  const filteredNfts =
    collectionNfts?.items?.filter((nft: any) => nft._id !== nftId) || []

  useEffect(() => {
    if (isError) {
      router.push('/404')
    }
  }, [isError])

  return (
    <div>
      <Breadcrumbs
        dynamicNames={[
          nftData?.collection?.name || 'Collection',
          nftData?.name || 'NFT',
        ]}
        className="mb-[40px]"
      />
      <NftDetail
        nft={nftData}
        refetchDetail={refetchDetail}
        isLoading={isLoading}
      />

      <div className="mt-20 pt-10">
        <p className="font-montserrat font-semibold text-[18px] leading-[170%] tracking-[0.02em] text-white border-b border-1 border-default pb-3 mb-[30px]">
          {trans.nft_you_may_also_like}
        </p>
        <ListNfts
          collectionId={collectionId as string}
          nfts={filteredNfts}
          loading={collectionLoading}
        />
      </div>
    </div>
  )
}
