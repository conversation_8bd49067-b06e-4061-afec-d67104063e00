import { useMemo } from 'react'
import { getUserBalance } from '@/services/apiCall/users'
import { STORAGEKEY } from '@/services/cookies'
import { useCookies } from 'react-cookie'
import { useQuery } from '@tanstack/react-query'

export const useGetUserBalance = () => {
  const [cookies] = useCookies([STORAGEKEY.ADMIN_ACCESS_TOKEN])
  const isConnected = useMemo<boolean>(() => {
    return !!cookies[STORAGEKEY.USER_ACCESS_TOKEN]
  }, [cookies])

  const {
    data: balance,
    isLoading: loading,
    error,
    refetch: fetchBalance,
  } = useQuery({
    queryKey: ['user-balance'],
    queryFn: () => getUserBalance(),
    enabled: isConnected,
    refetchOnMount: false,
    refetchInterval: false,
    refetchOnWindowFocus: false,
    retry: false,
  })

  return {
    balance,
    loading,
    error,
    refetch: fetchBalance,
  }
}
