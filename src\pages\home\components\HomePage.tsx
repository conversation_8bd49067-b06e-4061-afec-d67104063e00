import React, { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Collection, PaginatedCollection } from '@/interfaces'
import {
  getMarketCollections,
  getListCategories,
  getNews,
} from '@/services/apiCall'
import { HOMEPAGE_CONFIG } from '@/constants/homepage'
import BannerSection from './BannerSection'
import SearchFilterBar from './SearchFilterBar'
import CollectionGrid from './CollectionGrid'
import MarketplaceInsights from './MarketplaceInsights'
import EducationalSection from './EducationalSection'
import { useRouter } from 'next/router'
import { DEFAULT_PAGE_SIZE, ROUTES } from '@/constants'

const HomePage: React.FC = () => {
  const router = useRouter()
  // State for search and filters
  const [searchValue, setSearchValue] = useState('')
  const [categoryValue, setCategoryValue] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [collections, setCollections] = useState<Collection[]>([])

  // State for actual search parameters (used in API calls)
  const [searchParams, setSearchParams] = useState({
    searchWord: '',
    category: '',
  })

  // Fetch categories for filter dropdown
  const { data: categoryData, isLoading: isLoadingCategories } = useQuery({
    queryKey: ['categories'],
    queryFn: getListCategories,
  })

  // Fetch news for marketplace insights
  const { data: newsData, isLoading: isLoadingNews } = useQuery({
    queryKey: ['news'],
    queryFn: () => getNews({ pageSize: DEFAULT_PAGE_SIZE, pageIndex: 1 }),
  })

  // Fetch collections with search and filter
  const { data: collectionData, isLoading: isLoadingCollections } =
    useQuery<PaginatedCollection>({
      queryKey: [
        'market-collections',
        searchParams.searchWord,
        searchParams.category,
        currentPage,
      ],
      queryFn: () =>
        getMarketCollections({
          pageIndex: currentPage,
          pageSize: HOMEPAGE_CONFIG.defaultPageSize,
          searchWord: searchParams.searchWord || undefined,
          category: searchParams.category || undefined,
        }),
    })

  // Update collections when data changes
  useEffect(() => {
    if (collectionData) {
      if (currentPage === 1) {
        setCollections(collectionData.items || [])
      } else {
        setCollections((prev) => [...prev, ...(collectionData.items || [])])
      }
    }
  }, [collectionData, currentPage])

  // Handle search button click
  const handleSearch = () => {
    setSearchParams({
      searchWord: searchValue,
      category: categoryValue,
    })
    setCurrentPage(1)
  }

  // Handle load more
  const handleLoadMore = () => {
    let loadMoreRoute = `${ROUTES.marketCollections}?page=2&limit=${HOMEPAGE_CONFIG.defaultPageSize}`
    if (searchParams.searchWord)
      loadMoreRoute += `&searchWord=${searchParams.searchWord}`
    if (searchParams.category)
      loadMoreRoute += `&category=${searchParams.category}`
    router.push(loadMoreRoute)
  }

  // Check if there are more collections to load
  const hasMore = collectionData
    ? collectionData.pageIndex * collectionData.pageSize <
      collectionData.totalItems
    : false

  return (
    <div className="w-full min-h-screen">
      <BannerSection />
      <div className="w-full grid grid-cols-1 lg:grid-cols-3 gap-12">
        {/* Collection Grid - Takes 2 columns on desktop */}
        <div className="lg:col-span-2">
          <div className="sm:inline-flex justify-start">
            <SearchFilterBar
              searchValue={searchValue}
              categoryValue={categoryValue}
              categories={categoryData?.items || []}
              onSearchChange={setSearchValue}
              onCategoryChange={setCategoryValue}
              onSearch={handleSearch}
              isLoading={isLoadingCategories}
            />
          </div>
          <CollectionGrid
            collections={collections}
            isLoading={isLoadingCollections}
            hasMore={hasMore}
            onLoadMore={handleLoadMore}
            categories={categoryData?.items || []}
          />
        </div>

        {/* Marketplace Insights Sidebar */}
        <div className="lg:col-span-1">
          <MarketplaceInsights
            news={newsData?.items || []}
            isLoading={isLoadingNews}
          />
        </div>
      </div>
      {/* Educational Section */}
      <EducationalSection />
    </div>
  )
}

export default HomePage
