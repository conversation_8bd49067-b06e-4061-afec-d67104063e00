import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  Filler,
  type ChartOptions,
} from 'chart.js'
import { Line } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  Filler
)

// Helpers
const getNiceMax = (max: number) => {
  if (max <= 10) return 10
  const pow = Math.pow(10, Math.floor(Math.log10(max)))
  return Math.ceil(max / pow) * pow
}

const formatK = (v: number, s: number) => {
  if (Math.abs(v) >= 1000) {
    const value = v / 1000
    return Number.isInteger(value) ? `${value}K` : `${value.toFixed(s)}K`
  }
  return v
}

// LineChart using react-chartjs-2, matching old SVG behavior
export const LineChart = ({
  data,
  yMax,
  yFormat,
  unit = '',
}: {
  data: { date: string; value: number }[]
  yMax?: number
  yFormat?: (v: number) => string | number
  unit?: string
}) => {
  if (!data.length) {
    return (
      <div className="h-[320px] bg-getCardBg rounded-[16px] flex justify-center" />
    )
  }

  const maxY = Math.max(...data.map((d) => d.value))
  const yMaxFinal = yMax !== undefined ? yMax : getNiceMax(maxY)
  const step = yMaxFinal / 4
  const yFormatFinal = yFormat || ((v: number) => formatK(v, 2))

  const labels = data.map((d) =>
    /\d{4}-\d{2}-\d{2}/.test(d.date) ? d.date.slice(5, 10) : d.date
  )

  const chartData = {
    labels,
    datasets: [
      {
        label: '',
        data: data.map((d) => d.value),
        borderColor: '#f2c157',
        backgroundColor: 'rgba(245, 184, 59, 0.15)',
        tension: 0.3,
        pointRadius: 0,
        pointHoverRadius: 3,
        borderWidth: 2,
        fill: false,
      },
    ],
  }

  const options: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: {
        callbacks: {
          label: (ctx) => `${yFormatFinal(ctx.raw as number)}${unit}`,
        },
      },
    },
    scales: {
      x: {
        grid: {
          drawTicks: false,
          color: '#1C1C1C',
        },
        ticks: {
          color: '#A1A1A1',
          font: { size: 10, family: 'Inter, Roboto, Arial, sans-serif' },
          maxRotation: 0,
          autoSkip: true,
          autoSkipPadding: 20,
          padding: 20,
        },
        border: { display: false },
      },
      y: {
        min: 0,
        max: yMaxFinal,
        ticks: {
          stepSize: step, // force ~4 ticks evenly spaced
          color: '#A1A1A1',
          font: { size: 10, family: 'Inter, Roboto, Arial, sans-serif' },
          callback: (value) => `${yFormatFinal(value as number)}${unit}`,
          padding: 10,
        },
        grid: {
          drawTicks: false,
          color: 'rgba(255,255,255,0.2)',
        },
        border: { display: true, color: '#1C1C1C', dash: [2, 2] },
      },
    },
  }

  return (
    <div className="w-full h-[320px] bg-getCardBg p-2">
      <Line data={chartData} options={options} />
    </div>
  )
}
