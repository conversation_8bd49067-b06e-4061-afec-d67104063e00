const http = require('http')
const https = require('https')
const { URL } = require('url')

// Test sitemap endpoints
const SITEMAP_ENDPOINTS = [
  '/api/sitemap.xml',
  '/api/sitemap-collections.xml',
  '/api/sitemap-index.xml',
  '/sitemap.xml', // Should redirect to /api/sitemap.xml
]

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url)
    const client = parsedUrl.protocol === 'https:' ? https : http
    
    const req = client.get(url, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          url: url
        })
      })
    })
    
    req.on('error', (err) => {
      reject(err)
    })
    
    req.setTimeout(10000, () => {
      req.destroy()
      reject(new Error(`Request timeout for ${url}`))
    })
  })
}

async function testSitemap() {
  console.log('🧪 Testing Sitemap Endpoints...\n')
  
  for (const endpoint of SITEMAP_ENDPOINTS) {
    const url = `${BASE_URL}${endpoint}`
    
    try {
      console.log(`Testing: ${url}`)
      const response = await makeRequest(url)
      
      // Check status code
      if (response.statusCode === 200) {
        console.log(`✅ Status: ${response.statusCode}`)
      } else if (response.statusCode === 301 || response.statusCode === 302) {
        console.log(`🔄 Redirect: ${response.statusCode} -> ${response.headers.location}`)
      } else {
        console.log(`❌ Status: ${response.statusCode}`)
      }
      
      // Check content type
      const contentType = response.headers['content-type']
      if (contentType && contentType.includes('text/xml')) {
        console.log(`✅ Content-Type: ${contentType}`)
      } else {
        console.log(`⚠️  Content-Type: ${contentType}`)
      }
      
      // Check cache headers
      const cacheControl = response.headers['cache-control']
      if (cacheControl) {
        console.log(`📦 Cache-Control: ${cacheControl}`)
      }
      
      // Validate XML structure
      if (response.data.includes('<?xml') && response.data.includes('<urlset')) {
        const urlCount = (response.data.match(/<url>/g) || []).length
        console.log(`✅ Valid XML with ${urlCount} URLs`)
      } else if (response.data.includes('<?xml') && response.data.includes('<sitemapindex')) {
        const sitemapCount = (response.data.match(/<sitemap>/g) || []).length
        console.log(`✅ Valid Sitemap Index with ${sitemapCount} sitemaps`)
      } else {
        console.log(`❌ Invalid XML structure`)
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
    
    console.log('---')
  }
  
  console.log('\n🎯 Testing Complete!')
  console.log('\n📝 Next Steps:')
  console.log('1. Submit sitemap to Google Search Console')
  console.log('2. Test with Google\'s Rich Results Test')
  console.log('3. Monitor sitemap in search console for errors')
  console.log('4. Set up automated sitemap monitoring')
}

// Run the test
testSitemap().catch(console.error)
