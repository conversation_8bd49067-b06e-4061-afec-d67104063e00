import React from 'react'
import { RightOutlined } from '@ant-design/icons'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { getUserNfts } from '@/services/apiCall/nft-minted'
import { CARD_SIZES } from '@/constants'
import { useTranslate } from '@/hooks'
import Link from 'next/link'
import { MyNftCard } from '@/components/card/MyNftCard'
import { useRouter } from 'next/navigation'
import { setProfileImage } from '@/services/apiCall'
import { message } from 'antd'
import { STORAGEKEY } from '@/services/cookies'
import { useCookies } from 'react-cookie'

interface MyNFTsProps {
  walletAddress: string
  setIsUpdating?: any
  isUpdatingAva?: boolean
}

function MyNFTs({ walletAddress, setIsUpdating }: MyNFTsProps) {
  const defaultPageSize = 4
  const trans = useTranslate()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [cookies] = useCookies([
    STORAGEKEY.USER_ACCESS_TOKEN,
    STORAGEKEY.WALLET_ADDRESS,
  ])

  // Query for user NFTs
  const {
    data: userNfts,
    isLoading: nftsLoading,
    error: nftsError,
  } = useQuery({
    queryKey: ['userNfts', walletAddress],
    queryFn: () =>
      getUserNfts({
        pageIndex: 1,
        pageSize: defaultPageSize,
      }),
    enabled: !!walletAddress,
  })

  const setProfileImageMutation = useMutation({
    mutationFn: ({ nftId }: { nftId: string }) => setProfileImage(nftId),
    onSuccess: () => {
      message.success(trans.profile_image_upload_success)
      // Optionally refetch user data or NFTs to reflect the change
      queryClient.invalidateQueries({ queryKey: ['userNfts'] })
      router.refresh()
    },
    onError: (error) => {
      console.error('Failed to set profile image:', error)
      message.error(trans.profile_image_set_error)
    },
  })

  const handleSetProfileImage = (nftId: string) => {
    if (cookies[STORAGEKEY.USER_ACCESS_TOKEN]) {
      setIsUpdating?.(true)
      setProfileImageMutation.mutate({
        nftId,
      })
    }
  }

  // Check if user has more than 4 NFTs
  const hasMoreNfts = userNfts && userNfts.totalItems > defaultPageSize

  if (nftsLoading) {
    return (
      <div className="mb-12">
        <p className="font-montserrat font-semibold text-lg tracking-[0.02em] leading-[170%] text-white border-b border-default pb-2 mb-8">
          {trans.my_nfts}
        </p>
        <div className="text-white text-center">{trans.loading_nfts}</div>
      </div>
    )
  }

  if (nftsError) {
    return (
      <div className="mb-12">
        <p className="font-montserrat font-semibold text-lg tracking-[0.02em] leading-[170%] text-white border-b border-default pb-2 mb-8">
          {trans.my_nfts}
        </p>
        <div className="text-white text-center">
          {trans.failed_to_load_nfts}
        </div>
      </div>
    )
  }

  return (
    <div className="my-12">
      <p className="font-montserrat font-semibold text-lg tracking-[0.02em] leading-[170%] text-white border-b border-default pb-2 mb-8">
        {trans.my_nfts}
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {userNfts?.items?.slice(0, 4).map((nft, index) => (
          <MyNftCard
            key={nft.tokenId || index}
            name={nft.name}
            imageUrl={nft.canSetAsProfileImage ? nft.image || `/images/nft${index + 1}.jpg` : ''}
            videoUrl={!nft.canSetAsProfileImage ? nft.image : ''}
            size={CARD_SIZES.MD}
            actions={[
              {
                label: trans.view,
                onClick: () => router.push(`/my-profile/nft/${nft.metadataId}`),
              },
              // Conditionally show "Set Profile" action
              ...(nft.canSetAsProfileImage && nft._id
                ? [
                    {
                      label: setProfileImageMutation.isPending
                        ? trans.setting
                        : trans.set_profile,
                      onClick: () => handleSetProfileImage(nft._id || ''),
                      disabled: setProfileImageMutation.isPending,
                    },
                  ]
                : []),
            ]}
          />
        ))}
      </div>
      {hasMoreNfts && (
        <Link href={`/my-nfts`}>
          <button className="text-primary hover:text-primaryHover flex items-center gap-2 font-montserrat font-normal text-sm tracking-[0.02em] leading-[170%] mx-auto">
            <RightOutlined rev="icon" />
            <span>{trans.view_all_my_nfts}</span>
          </button>
        </Link>
      )}
    </div>
  )
}

export default MyNFTs
