export const CONTACT_CONFIG = {
  // Page
  contactTitle: 'contact_title',
  contactSubtitle: 'contact_subtitle',

  // Required Information
  contactRequiredInfoTitle: 'contact_required_info_title',
  fullnameTitle: 'fullname_title',
  companyName1Title: 'company_name_1_title',
  companyName2Title: 'company_name_2_title',
  emailTitle: 'email_title',
  phoneTitle: 'phone_title',
  phoneNote: 'phone_note',
  subjectTitle: 'subject_title',
  inquiryDetailsTitle: 'inquiry_details_title',

  // Validation & Placeholders
  contactFullnameRequired: 'contact_fullname_required',
  contactFullnameMaxLength: 'contact_fullname_max_length',
  contactCompanyNameOptional: 'contact_company_name_optional',
  contactEmailRequired: 'contact_email_required',
  contactEmailInvalid: 'contact_email_invalid',
  contactEmailMaxLength: 'contact_email_max_length',
  contactPhoneRequired: 'contact_phone_required',
  contactPhoneInvalid: 'contact_phone_invalid',
  contactPhoneMaxLength: 'contact_phone_max_length',
  contactSubjectRequired: 'contact_subject_required',
  contactInquiryDetailsRequired: 'contact_inquiry_details_required',
  inquiryDetailsMaxLength: 'contact_inquiry_details_max_length',

  fullnamePlaceholder: 'fullname_placeholder',
  emailPlaceholder: 'email_placeholder',
  phonePlaceholder: 'phone_placeholder',
  subjectPlaceholder: 'subject_placeholder',
  inquiryDetailsPlaceholder: 'inquiry_details_placeholder',
  fullnameCannotBeOnlySpaces: 'fullname_cannot_be_only_spaces',
  companyNameCannotBeOnlySpaces: 'company_name_cannot_be_only_spaces',
  companyNameMaxLength: 'company_name_max_length',
  subjectMaxLength: 'subject_max_length',
  subjectCannotBeOnlySpaces: 'subject_cannot_be_only_spaces',
  inquiryDetailsCannotBeOnlySpaces: 'inquiry_details_cannot_be_only_spaces',

  // Notes & Messages
  note: 'note',
  contactSuccess: 'contact_success',
  contactError: 'contact_error',

  // Actions
  subject: 'Subject',
  submit: 'submit',
  submitting: 'submitting',
} as const
