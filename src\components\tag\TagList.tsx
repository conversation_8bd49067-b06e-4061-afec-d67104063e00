import clsx from 'clsx'
import { HiX } from 'react-icons/hi'

interface TagProps {
  text: string
  remove: (text: string) => void
  disabled?: boolean
  className?: string
}

const TagList = ({ text, remove, disabled, className }: TagProps) => {
  const handleOnRemove = () => {
    remove(text)
  }

  return (
    <span
      className={clsx(
        'gap-1 flex-row inline-flex items-center justify-center border rounded px-1',
        className
      )}
    >
      <span className="max-w-[600px] break-words">{text}</span>
      {!disabled && (
        <button
          type="button"
          onClick={() => handleOnRemove()}
          aria-label={`remove ${text}`}
          className="cursor-pointer "
        >
          <HiX className="hover:text-red-500 hover:opacity-80" />
        </button>
      )}
    </span>
  )
}

export default TagList
