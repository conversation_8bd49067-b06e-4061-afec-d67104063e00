import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { TEducationalItem } from '@/interfaces'

interface KnowledgeCardProps {
  item: TEducationalItem
  className?: string
}

const KnowledgeCard: React.FC<KnowledgeCardProps> = ({
  item,
  className = '',
}) => {
  return (
    <Link href={item.url}>
      <div
        className={`group rounded-lg w-[240px] transition-all duration-300  ${className}`}
      >
        {/* Educational Image */}
        <div className="relative aspect-square w-full overflow-hidden  h-[150px]">
          <Image
            src={item.image}
            alt={item.title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110 rounded-md"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Educational Content */}
        <div className="p-4">
          <h4 className="font-montserrat font-semibold text-[13px] leading-[140%] tracking-[0.02em] text-white group-hover:text-neutral-300 transition-colors max-w-[240px] truncate">
            {item.title}
          </h4>
        </div>
      </div>
    </Link>
  )
}

export default KnowledgeCard
