import { memo, ReactNode, useRef } from 'react'

import { useStore } from '@/store'
import { useClickOutside } from '@/hooks'
import { CloseIcon } from '@/icons'
import { CLOSE_MODAL } from '.'

type ModalProps = {
  width?: string
  height?: string
  backgroundAfter?: `after:bg-${string}`
  className?: string
  children: ReactNode
  onClose?: () => void
  title?: string
  noHeader?: boolean
  isStopClose?: boolean
}

const ModalLayout: React.FC<ModalProps> = ({
  width = '400px',
  height = '452px',
  className,
  children,
  onClose,
  title,
  noHeader = false,
  isStopClose = false,
}) => {
  const modalRef = useRef(null)

  const { setModalType } = useStore()
  useClickOutside(modalRef, () => closeModal())

  const closeModal = () => {
    if (!isStopClose) {
      setModalType(CLOSE_MODAL)
      onClose && onClose()
    }
    return
  }

  window.addEventListener('popstate', () => {
    closeModal()
  })

  return (
    <div className="h-screen w-screen overlay-dark !fixed left-0 top-0 z-[10000000] flex items-center justify-center">
      <div
        ref={modalRef}
        className={`p-8 mx-4 text-tailwindNeutral1 after-full z-0 bg-white h-[${height}] w-[${width}] rounded-2xl ${
          className ?? ''
        }`}
      >
        {!noHeader && (
          <div className="flex justify-between mb-6 relative z-50">
            <h2 className="text-tailwindNeutral1 font-bold text-2xl max-sm:text-xl max-sm:pr-8">
              {title}
            </h2>
            {!isStopClose && (
              <button
                className="translate-x-1 -translate-y-1"
                onClick={() => closeModal()}
              >
                <CloseIcon />
              </button>
            )}
          </div>
        )}
        <div className="relative z-40 max-h-[90vh]">{children}</div>
      </div>
    </div>
  )
}

export default memo(ModalLayout)
