import { STORAGEKEY } from '@/services/cookies'
import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react'
import { useCookies } from 'react-cookie'
import AppSocket from '../../socket'

export interface AppState {
  socket: any
}

export interface AppContextType extends AppState {
  socket: any
}

// Create the context with undefined as initial value
const AppContext = createContext<AppContextType | undefined>(undefined)

// Provider props interface
interface AppProviderProps {
  children: ReactNode
}

// Provider component
export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<any>(null)
  const [cookies] = useCookies([STORAGEKEY.USER_ACCESS_TOKEN])

  useEffect(() => {
    if (!AppSocket) return
    let appSocket: any
    const handleSocket = async () => {
      if (
        !socket ||
        (!socket?.connected && cookies[STORAGEKEY.USER_ACCESS_TOKEN])
      ) {
        appSocket = AppSocket({
          socketToken: cookies[STORAGEKEY.USER_ACCESS_TOKEN],
        })
        // Wait for socket to connect
        appSocket?.on('connect', () => {
          console.log('Socket connected:', appSocket.connected)
          setSocket(appSocket)
        })
        // Handle connection errors
        appSocket?.on('connect_error', (error: any) => {
          console.error('Socket connection error:', error)
        })
      }
    }
    !socket && handleSocket()
  }, [cookies[STORAGEKEY.USER_ACCESS_TOKEN]])

  const value: AppContextType = {
    socket,
  }

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>
}

// Custom hook to use the context
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext)

  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider')
  }

  return context
}
