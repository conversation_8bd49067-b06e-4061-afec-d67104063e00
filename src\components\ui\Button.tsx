import React from 'react'
import { BUTTON_SIZES } from '@/constants'
import dynamic from 'next/dynamic'
const UiSpinner = dynamic(() => import('@/components/ui/Spinner'))

interface IUiButton {
  title: string | React.ReactNode
  className?: string
  handleClick?: () => void
  size?: BUTTON_SIZES
  isGradient?: boolean
  isDisabled?: boolean
  isLoading?: boolean
  isBorder?: boolean
  icon?: React.ReactNode
  isFilled?: boolean
}

const UiButton = React.memo(
  ({
    title,
    className,
    size = BUTTON_SIZES.SM,
    isLoading = false,
    isDisabled = false,
    isGradient = true,
    isFilled = false,
    isBorder = false,
    handleClick,
    icon,
  }: IUiButton) => {
    const buttonHeight =
      size === BUTTON_SIZES.SM ? 'h-[2.25rem]' : 'h-[2.75rem]'
    const widthObject = {
      [BUTTON_SIZES.FIT]: 'fit',
      [BUTTON_SIZES.SM]: 'w-[11.25rem]',
      [BUTTON_SIZES.NORMAL]: 'w-[9.375rem]',
      [BUTTON_SIZES.MD]: 'w-[13.125rem]',
      [BUTTON_SIZES.LG]: 'w-[16.25rem]',
      [BUTTON_SIZES.XL]: 'w-[26rem]',
    }
    const buttonWidth = widthObject[size]
    const filledClass = `bg-primary text-black hover:bg-primaryHover`
    const gradientClass = `bg-gradient-to-l from-[#F2AF36] to-[#FAE475] text-black hover:shadow-[0_0_20px_rgba(242,175,54,0.5)]`
    const noneGradientClass = `bg-transparent text-white`
    const strokeColorFrom = '#000'
    const strokeColorTo = '#000'
    const hoverClass = !isGradient ? 'hover:opacity-80' : ''
    const borderClass = isBorder
      ? 'border !border-[rgba(255, 255, 255, .5)]'
      : ''

    const RenderBaseButton = React.memo(() => {
      return (
        <div
          className={`flex justify-center items-center ${
            isFilled
              ? filledClass
              : isGradient
              ? gradientClass
              : noneGradientClass
          } ${className}
        ${buttonWidth} ${buttonHeight}
        rounded-none shadow-none p-2 box-border font-semibold text-sm cursor-pointer font-montserrat transtion-all duration-300
        ${(isDisabled || isLoading) && '!cursor-not-allowed'}
        ${isDisabled && 'opacity-50'}
        ${!isDisabled && hoverClass}
        ${borderClass}
      `}
          onClick={() => {
            if (!isDisabled && !isLoading) {
              handleClick && handleClick()
            }
          }}
        >
          {icon && (
            <div className="flex items-center justify-center mr-2">{icon}</div>
          )}
          {isLoading ? (
            <UiSpinner
              {...(isGradient ? { strokeColorFrom, strokeColorTo } : {})}
            />
          ) : (
            title
          )}
        </div>
      )
    })
    RenderBaseButton.displayName = 'RenderBaseButton'

    return <RenderBaseButton />
  }
)

UiButton.displayName = 'Button'

export default UiButton
