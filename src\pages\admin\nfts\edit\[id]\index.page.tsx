import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useRouter } from 'next/router'

import FormNft from '../../FormNFT'
import { API_URLS, APPROVAL_STATUS, ROLE } from '@/constants'
import { get } from '@/services/apiCall/baseApi'
import { MESSAGE_FORM } from '@/utils/string'
import { NftDetail } from '@/interfaces'

const MyNFTEdit = () => {
  const { query } = useRouter()
  const [nftData, setNftData] = useState<NftDetail>()
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-extra-semi
    ;(async () => {
      if (query.id) {
        try {
          const data = await get(
            `${API_URLS.adminMetadata}/${query.id}`,
            {},
            ROLE.systemAdmin
          )
          setNftData(data)
        } catch (err) {
          toast.error(MESSAGE_FORM.errorCallServer)
        }
      }
    })()
  }, [query])

  const mode = [APPROVAL_STATUS.waiting, APPROVAL_STATUS.approved].includes(
    nftData?.approvalStatus as APPROVAL_STATUS
  )
    ? 'view'
    : 'update'

  return (
    <div>
      <FormNft data={nftData} mode={mode} />
    </div>
  )
}

export default MyNFTEdit
