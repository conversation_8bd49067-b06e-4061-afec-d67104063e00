import { createConfig, configureChains } from 'wagmi'
import { polygon } from 'wagmi/chains'
import { MetaMaskConnector } from 'wagmi/connectors/metaMask'
import { jsonRpcProvider } from 'wagmi/providers/jsonRpc'

export const polygonMumbai = {
  id: 80002,
  name: '<PERSON><PERSON>',
  network: 'Amoy',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'POL', decimals: 18 },
  rpcUrls: {
    default: {
      http: [`${process.env.NEXT_PUBLIC_POLYGON_RPC}`],
    },
    public: {
      http: [`${process.env.NEXT_PUBLIC_POLYGON_RPC}`],
    },
  },
  blockExplorers: {
    default: {
      name: 'Amoy Explorer',
      url: process.env.NEXT_PUBLIC_POLYGON_BLOCK_EXPLORER_URL,
    },
  },
}

export const configWagmi = {
  chains: {
    development: polygonMumbai,
    staging: polygonMumbai,
    production: polygon,
    default: polygon,
  },
  chainsName: {
    development: 'Polygon Testnet',
    staging: 'Polygon Testnet',
    production: 'Polygon',
    default: 'Polygon',
  },
  blockExplorers: process.env.NEXT_PUBLIC_POLYGON_BLOCK_EXPLORER_URL,
}
export const environmentMode = process.env.NEXT_PUBLIC_NODE_ENV || 'default'

const { chains, publicClient, webSocketPublicClient } = configureChains(
  [configWagmi.chains[environmentMode]],
  [
    jsonRpcProvider({
      rpc: () => {
        return {
          http: `${process.env.NEXT_PUBLIC_POLYGON_RPC}`,
        }
      },
    }),
  ]
)
export const wagmiClient = createConfig({
  autoConnect: true,
  connectors: [
    new MetaMaskConnector({
      chains,
    }),
  ],
  publicClient,
  webSocketPublicClient,
})
