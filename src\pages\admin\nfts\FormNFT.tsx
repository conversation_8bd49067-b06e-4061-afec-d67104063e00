import { useState, useEffect } from 'react'
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  Select,
  InputNumber,
  DatePicker,
  Space,
  Spin,
} from 'antd'
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons'
import InputTags from '@/components/tag/InputTags'
import { approveNft, createNft, uploadNftImage } from '@/services/apiCall/nft'
import { updateNft } from '@/services/apiCall/nft'
import {
  APPROVAL_STATUS,
  BUTTON_SIZES,
  CONTRACT_TYPE,
  MAX_SIZE_FILE_NFT,
} from '@/constants'
import { toast } from 'react-toastify'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import UiButton from '@/components/ui/Button'
import { getListCollections } from '@/services/apiCall/collection'
import * as yup from 'yup'
import { NftDetail } from '@/interfaces'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
import UploadImage, {
  ALLOWED_FILE_TYPE,
} from '@/components/uploadImages/UploadImages'
import router from 'next/router'
import { getPermission } from '@/utils'

const { TextArea } = Input

const text = {
  title: {
    create: 'NFT作成',
    update: 'NFT編集',
    view: 'NFT詳細',
  },
  form: {
    collection: 'コレクション',
    chooseACollection: 'コレクションを選ぶ',
    name: '名前',
    supply: '数量',
    price: '価格',
    startDate: '開始日',
    startTime: '開始時間',
    endDate: '停止日',
    endTime: '停止時間',
    description: '説明',
    enterADescription: '紹介文をご記入ください',
    externalLink: '外部リンク',
    tag: 'タグ',
    traits: '特性',
    addTrait: '特性追加',
    enterTagSeparatedByComma:
      'タグを入力して登録するには「、」を入力してください。',
    traitType: 'タイプ',
    value: '名前',
  },
  upload: {
    dragAndDropMedia: 'メディアのドラッグ&ドロップ',
    browseFiles: 'ファイルを参照',
    maxSize: `最大サイズ: ${MAX_SIZE_FILE_NFT}MB`,
    allowedExtensions: 'JPG, PNG, GIF, SVG, MP4',
  },
  buttons: {
    saveDraft: 'ドラフトを保存',
    create: '作成',
    update: '更新',
    approve: '承認',
    reject: '却下',
  },
  messages: {
    pleaseUploadAnImage: '画像をアップロードしてください',
    uploadImageFailed: '画像のアップロードに失敗しました。',
    uploadImageSuccess: '画像のアップロードに成功しました。',
    fileSizeTooLarge: `ファイルサイズは${MAX_SIZE_FILE_NFT}MB以下にしてください。`,
    getCollectionFailed: 'コレクションの取得に失敗しました',
    draftSaved: 'ドラフトが保存されました。',
    nftCreated: 'NFTが作成されました。',
    nftUpdated: 'NFTを更新しました',
    noNftDataToUpdate: 'NFTデータがありません。',
    failedToUpdateNft: 'NFTの更新に失敗しました。',
    pleaseSelectACollection: 'コレクションを選択してください。',
    pleaseEnterAName: '名前を入力してください。',
    pleaseEnterADescription: '説明を入力してください。',
    tagMaxLength: 'タグは最大255文字までです。',
    invalidUrl: '外部リンクはURLアドレスである必要があります。',
    pleaseEnterSupply: '供給を入力してください。',
    pleaseEnterPrice: '価格を入力してください。',
    pleaseSelectStartDate: '開始日を選択してください。',
    pleaseSelectStartTime: '開始時間を選択してください。',
    pleaseSelectEndDate: '停止日を選択してください。',
    pleaseSelectEndTime: '停止時間を選択してください。',
    valueRequired: '値が必要です。',
    traitTypeRequired: '特性タイプが必要です。',
    traitsMaxLength: '特性は最大10個までです。',
    descriptionMaxLength: '説明は最大255文字までです。',
    nameMaxLength: '名前は最大100文字までです。',
    valueMaxLength: '値は最大50文字までです。',
    traitTypeMaxLength: '特性タイプは最大50文字までです。',
    failedToApprove: 'NFTの承認に失敗しました。',
    approved: '承認済み',
    rejected: '却下済み',
    cannotBeEmptyOrOnlySpaces: '空またはスペースのみにすることはできません',
    priceMustBeAPositiveNumber: '価格は正の数でなければなりません',
    quantityMustBeAPositiveInteger: '数量は正の整数でなければなりません',
    youCanEnterUpTo5Digits: '最大5桁まで入力できます。',
    youCanEnterUpTo4DigitsAfterDecimalPoint:
      '小数点以下4桁までしか入力できません',
  },
  traits: {
    description:
      '特性はアイテムの属性を表すもので、コレクションページではフィルターとして、またアイテムページでは一覧として表示されます。',
  },
}

const FormNFT = ({
  data,
  mode,
}: {
  data?: NftDetail
  mode: 'create' | 'update' | 'view'
}) => {
  const [form] = Form.useForm()
  const [imageUrl, setImageUrl] = useState<string>('')
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [tags, setTags] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [isDraft, setIsDraft] = useState(
    data?.approvalStatus === APPROVAL_STATUS.draft
  )
  const [selectedCollectionStandard, setSelectedCollectionStandard] = useState<
    CONTRACT_TYPE | undefined
  >(data?.collection.standard)
  const { canCreate, canEdit, canApprove } = getPermission({
    approvalStatus: data?.approvalStatus,
    creatorId: data?.createdBy._id || '',
  })

  // Async collection select state
  const [collectionState, setCollectionState] = useState({
    options: [] as { label: string; value: string; standard: CONTRACT_TYPE }[],
    loading: false,
    page: 1,
    total: 0,
    search: '',
    hasMore: true,
  })
  const canCreateForm = mode === 'create' && canCreate
  const canUpdateForm = mode === 'update' && canEdit
  const canEditFields = canCreateForm || canUpdateForm
  const canApproveForm = mode === 'view' && canApprove

  // Fetch collections
  const fetchCollections = async (pageIndex = 1, searchWord = '') => {
    setCollectionState((prev) => ({ ...prev, loading: true }))
    try {
      const params: any = {
        pageIndex,
        pageSize: 10,
        approvalStatus: APPROVAL_STATUS.approved,
      }
      if (searchWord) params.searchWord = searchWord
      const res = await getListCollections(params)
      const newOptions = res.items.map((item: any) => ({
        label: item.name,
        value: item._id,
        standard: item.standard as CONTRACT_TYPE,
      }))
      setCollectionState((prev) => {
        const mergedOptions =
          pageIndex === 1
            ? newOptions
            : [
                ...prev.options,
                ...newOptions.filter(
                  (opt) => !prev.options.some((p) => p.value === opt.value)
                ),
              ]
        return {
          ...prev,
          options: mergedOptions,
          total: res.totalItems,
          page: pageIndex,
          hasMore: mergedOptions.length < res.totalItems,
          loading: false,
        }
      })
    } catch (err) {
      toast.error(text.messages.getCollectionFailed)
      setCollectionState((prev) => ({ ...prev, loading: false }))
    }
  }

  const maxTraits = 10

  // Initial fetch
  useEffect(() => {
    fetchCollections(1, '')
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Prefill form if data is provided (edit mode)
  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data,
        collection: data.collection._id,
        saleRange:
          data.saleStartAt && data.saleEndAt
            ? [dayjs(data.saleStartAt).utc(), dayjs(data.saleEndAt).utc()]
            : undefined,
      })
      setSelectedCollectionStandard(data.collection?.standard)
      setTags(data.tags || [])
      if (data.image) setImageUrl(data.image)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  // Handle search
  const handleCollectionSearch = (value: string) => {
    setCollectionState((prev) => ({ ...prev, search: value }))
    fetchCollections(1, value)
  }

  // Handle scroll to bottom for infinite scroll
  const handleCollectionPopupScroll = (
    e: React.UIEvent<HTMLDivElement, UIEvent>
  ) => {
    const target = e.target as HTMLDivElement
    if (
      target.scrollTop + target.offsetHeight >= target.scrollHeight - 10 &&
      collectionState.hasMore &&
      !collectionState.loading
    ) {
      fetchCollections(collectionState.page + 1, collectionState.search)
    }
  }

  // Media Upload: Max 50MB validation
  // const beforeUpload = (file: File) => {
  //   const isLt50M = file.size / 1024 / 1024 < MAX_SIZE_FILE_NFT
  //   if (!isLt50M) {
  //     toast.error(text.messages.fileSizeTooLarge)
  //   }
  //   return isLt50M ? true : Upload.LIST_IGNORE
  // }

  const onFinishUpdate = async (values: any) => {
    setLoading(true)
    try {
      let imageIdUpdate: string | undefined
      if (imageFile) {
        try {
          imageIdUpdate = await uploadNftImage(imageFile)
        } catch (err: any) {
          toast.error(text.messages.uploadImageFailed)
          setLoading(false)
          return
        }
      } else if (data && !data.image) {
        toast.error(text.messages.pleaseUploadAnImage)
        setLoading(false)
        return
      }
      let saleStartAt = ''
      let saleEndAt = ''
      if (values.saleRange && values.saleRange.length === 2) {
        // Convert to UTC, keeping the same local date/time
        saleStartAt = values.saleRange[0]
          .utc(true)
          .format('YYYY-MM-DDTHH:mm:ss[Z]')
        saleEndAt = values.saleRange[1]
          .utc(true)
          .format('YYYY-MM-DDTHH:mm:ss[Z]')
      }
      const payload = {
        collection: values.collection,
        name: values.name,
        description: values.description,
        tags,
        externalLink: values.externalLink || undefined,
        maximumNumber: values.maximumNumber,
        price: values.price,
        saleStartAt,
        saleEndAt,
        attributes: values.attributes || [],
        imageId: imageIdUpdate,
        isDraft,
      }
      if (data && data._id) {
        await updateNft(data._id, payload)
        toast.success(
          isDraft ? text.messages.draftSaved : text.messages.nftUpdated
        )
        router.push(`/admin/nfts`)
      } else {
        toast.error(text.messages.noNftDataToUpdate)
      }
    } catch (err: any) {
      toast.error(err.message || text.messages.failedToUpdateNft)
      router.push(`/admin/nfts`)
    }
    setLoading(false)
  }

  const onFinishCreate = async (values: any) => {
    setLoading(true)
    try {
      let imageIdCreate = ''
      if (imageFile) {
        try {
          imageIdCreate = await uploadNftImage(imageFile)
        } catch (err: any) {
          toast.error(text.messages.uploadImageFailed)
          setLoading(false)
          return
        }
      } else {
        toast.error(text.messages.pleaseUploadAnImage)
        setLoading(false)
        return
      }
      let saleStartAt = ''
      let saleEndAt = ''
      if (values.saleRange && values.saleRange.length === 2) {
        // Convert to UTC, keeping the same local date/time
        saleStartAt = values.saleRange[0]
          .utc(true)
          .format('YYYY-MM-DDTHH:mm:ss[Z]')
        saleEndAt = values.saleRange[1]
          .utc(true)
          .format('YYYY-MM-DDTHH:mm:ss[Z]')
      }
      const payload = {
        collection: values.collection,
        name: values.name,
        description: values.description,
        tags,
        externalLink: values.externalLink || undefined,
        maximumNumber: values.maximumNumber,
        price: values.price,
        saleStartAt,
        saleEndAt,
        attributes: values.attributes || [],
        imageId: imageIdCreate,
        isDraft,
      }
      await createNft(payload)
      toast.success(
        isDraft ? text.messages.draftSaved : text.messages.nftCreated
      )
      form.resetFields()
      setImageUrl('')
      setImageFile(null)
      setTags([])
      router.push(`/admin/nfts`)
    } catch (err: any) {
      toast.error(err.message || 'Failed to create NFT.')
    }
    setLoading(false)
  }

  const onFinishApprove = async (isApproved: boolean) => {
    setLoading(true)
    try {
      await approveNft(data?._id, isApproved)
      toast.success(
        isApproved ? text.messages.approved : text.messages.rejected
      )
    } catch (err: any) {
      toast.error(err.message || text.messages.failedToApprove)
    }
    setLoading(false)
    router.push(`/admin/nfts`)
  }

  // Handle collection change
  const handleCollectionChange = (value: string) => {
    form.setFieldsValue({ collection: value })
    const collection = collectionState.options.find(
      (opt) => opt.value === value
    )
    setSelectedCollectionStandard(collection?.standard)
    if (collection?.standard !== CONTRACT_TYPE.ERC721) return
    form.setFieldsValue({ maximumNumber: 1 })
  }

  return (
    <Spin spinning={loading} size="large">
      <div className="min-h-screen">
        <div className="max-w-6xl">
          <TextAdminHeader title={text.title[mode]} />
          <Row gutter={32}>
            {/* Left: Image Upload */}
            <Col xs={24} md={12}>
              <div className="border-1 border-white flex flex-col items-center justify-start aspect-square">
                <UploadImage
                  label={text.upload.dragAndDropMedia}
                  accept={[
                    ALLOWED_FILE_TYPE.JPG,
                    ALLOWED_FILE_TYPE.PNG,
                    ALLOWED_FILE_TYPE.GIF,
                    ALLOWED_FILE_TYPE.SVG,
                    ALLOWED_FILE_TYPE.MP4,
                  ]}
                  recommendedSize={`${MAX_SIZE_FILE_NFT}MB`}
                  value={imageFile}
                  uploadVideo={data?.isVideo}
                  imageUrl={imageUrl}
                  onChange={(file) => {
                    setImageFile(file)
                  }}
                  setImageUrl={setImageUrl}
                  maxSize={MAX_SIZE_FILE_NFT}
                  disabled={!canEditFields}
                />
              </div>
            </Col>
            {/* Right: Form Fields */}
            <Col xs={24} md={12}>
              <Form
                form={form}
                layout="vertical"
                onFinish={(values) => {
                  if (canCreateForm) return onFinishCreate(values)
                  if (canUpdateForm) return onFinishUpdate(values)
                }}
                initialValues={{ maximumNumber: 1, price: 1 }}
                requiredMark={false}
              >
                <Form.Item
                  label={
                    <span className="text-white font-semibold">
                      {text.form.collection}{' '}
                      <span className="text-red-500">*</span>
                    </span>
                  }
                  name="collection"
                  rules={[
                    {
                      required: true,
                      message: text.messages.pleaseSelectACollection,
                    },
                  ]}
                >
                  <Select
                    size="large"
                    showSearch
                    filterOption={false}
                    onSearch={handleCollectionSearch}
                    onChange={handleCollectionChange}
                    onPopupScroll={handleCollectionPopupScroll}
                    loading={collectionState.loading}
                    options={[
                      ...(data &&
                      data.collection?.name
                        ?.toLowerCase()
                        .includes(collectionState.search.toLowerCase())
                        ? [
                            {
                              label: data.collection.name,
                              value: data.collection._id,
                            },
                          ]
                        : []),
                      ...collectionState.options.filter(
                        (opt) => opt.value !== data?.collection._id
                      ),
                    ]}
                    placeholder={text.form.chooseACollection}
                    className="w-full"
                    notFoundContent={
                      collectionState.loading ? <Spin size="small" /> : null
                    }
                    disabled={!canEditFields}
                    allowClear
                  />
                </Form.Item>
                <Form.Item
                  label={
                    <span className="text-white font-semibold">
                      {text.form.name} <span className="text-red-500">*</span>
                    </span>
                  }
                  name="name"
                  rules={[
                    { required: true, message: text.messages.pleaseEnterAName },
                    { max: 100, message: text.messages.nameMaxLength },
                    {
                      whitespace: true,
                      message: text.messages.cannotBeEmptyOrOnlySpaces,
                    },
                  ]}
                >
                  <Input
                    placeholder={text.form.name}
                    size="large"
                    maxLength={100}
                    showCount
                    disabled={!canEditFields}
                    allowClear
                  />
                </Form.Item>
                <Form.Item
                  label={
                    <span className="text-white font-semibold">
                      {text.form.description}{' '}
                      <span className="text-red-500">*</span>
                    </span>
                  }
                  name="description"
                  rules={[
                    {
                      required: true,
                      message: text.messages.pleaseEnterADescription,
                    },
                    { max: 255, message: text.messages.descriptionMaxLength },
                    {
                      whitespace: true,
                      message: text.messages.cannotBeEmptyOrOnlySpaces,
                    },
                  ]}
                >
                  <TextArea
                    rows={3}
                    placeholder={text.form.enterADescription}
                    maxLength={255}
                    showCount
                    disabled={!canEditFields}
                    allowClear
                  />
                </Form.Item>
                <Form.Item
                  label={
                    <span className="text-white font-semibold">
                      {text.form.tag}
                    </span>
                  }
                >
                  <InputTags
                    tags={tags}
                    handleChange={(value) => {
                      if (value.some((tag) => tag.length > 255)) {
                        toast.error(text.messages.tagMaxLength)
                        return
                      }
                      setTags(value)
                    }}
                    placeholder={text.form.enterTagSeparatedByComma}
                    disabled={!canEditFields}
                  />
                </Form.Item>
                <Form.Item
                  label={
                    <span className="text-white font-semibold">
                      {text.form.externalLink}
                    </span>
                  }
                  name="externalLink"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value) return Promise.resolve()
                        const schema = yup
                          .string()
                          .trim()
                          .matches(
                            /^https?:\/\/(?:[-\w.])+(:[0-9]+)?(?:\/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?$/
                          )
                        if (!schema.validateSync(value)) {
                          return Promise.reject()
                        }
                        return Promise.resolve()
                      },
                      message: text.messages.invalidUrl,
                    },
                  ]}
                >
                  <Input
                    placeholder={text.form.externalLink}
                    type="url"
                    size="large"
                    maxLength={255}
                    showCount
                    disabled={!canEditFields}
                    allowClear
                  />
                </Form.Item>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <span className="text-white font-semibold">
                          {text.form.supply}{' '}
                          <span className="text-red-500">*</span>
                        </span>
                      }
                      name="maximumNumber"
                      rules={[
                        {
                          required: true,
                          message: text.messages.pleaseEnterSupply,
                        },
                        {
                          validator: (_, value) => {
                            if (value && value.toString().length > 5) {
                              return Promise.reject(
                                text.messages.youCanEnterUpTo5Digits
                              )
                            }
                            return Promise.resolve()
                          },
                        },
                      ]}
                    >
                      <InputNumber
                        className="w-full"
                        size="large"
                        maxLength={5}
                        disabled={
                          !canEditFields ||
                          selectedCollectionStandard === CONTRACT_TYPE.ERC721
                        }
                        onKeyDown={(e) => {
                          if (!/^\d$/.test(e.key) && e.key.length === 1) {
                            e.preventDefault()
                          }
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <span className="text-white font-semibold">
                          {text.form.price}{' '}
                          <span className="text-red-500">*</span>
                        </span>
                      }
                      name="price"
                      rules={[
                        {
                          required: true,
                          message: text.messages.pleaseEnterPrice,
                        },
                        {
                          validator: (_, value) => {
                            const decimalDigits =
                              (value?.toString() || '')?.split('.')[1] || 0
                            if (decimalDigits.length > 4) {
                              return Promise.reject(
                                text.messages
                                  .youCanEnterUpTo4DigitsAfterDecimalPoint
                              )
                            }
                            return Promise.resolve()
                          },
                        },
                      ]}
                    >
                      <InputNumber
                        className="w-full"
                        addonAfter="GET Pay"
                        size="large"
                        onKeyDown={(e) => {
                          const isDecimalPoint =
                            e.key === '.' &&
                            !e.currentTarget.value.includes('.')
                          const isNumberInput = /^\d$/.test(e.key)
                          const isSpecialKey = e.key.length > 1
                          if (
                            !isDecimalPoint &&
                            !isNumberInput &&
                            !isSpecialKey
                          ) {
                            e.preventDefault()
                          }
                        }}
                        disabled={!canEditFields}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item
                  label={
                    <span className="text-white font-semibold">
                      {text.form.startDate} - {text.form.endDate} (UTC)
                      <span className="text-red-500">*</span>
                    </span>
                  }
                  name="saleRange"
                  rules={[
                    {
                      required: true,
                      message: `${text.messages.pleaseSelectStartDate} / ${text.messages.pleaseSelectEndDate}`,
                    },
                  ]}
                >
                  <DatePicker.RangePicker
                    className="w-full"
                    showTime={{ format: 'HH:mm:ss' }}
                    format="YYYY-MM-DD HH:mm:ss"
                    size="large"
                    disabled={!canEditFields}
                    disabledDate={(current) =>
                      current && current < dayjs().startOf('day')
                    }
                    allowClear
                    disabledTime={(date) => {
                      if (!date) return {}
                      const now = dayjs()
                      if (date.isSame(now, 'day')) {
                        const disabledHours: number[] = []
                        for (let i = 0; i < 24; i++) {
                          if (i < now.hour()) disabledHours.push(i)
                        }
                        const disabledMinutes: number[] = []
                        if (date.hour() === now.hour()) {
                          for (let i = 0; i < 60; i++) {
                            if (i <= now.minute()) disabledMinutes.push(i)
                          }
                        }
                        return {
                          disabledHours: () => disabledHours,
                          disabledMinutes: () => disabledMinutes,
                        }
                      }
                      return {}
                    }}
                  />
                </Form.Item>
                <Form.List name="attributes">
                  {(fields, { add, remove }) => (
                    <div>
                      <label className="text-white font-semibold">
                        {text.form.traits}
                      </label>
                      <div className="text-gray-400 text-xs mb-2">
                        {text.traits.description}
                      </div>
                      {fields.map(({ key, name, ...restField }) => (
                        <Space key={key} align="baseline" className="flex mb-2">
                          <Form.Item
                            {...restField}
                            name={[name, 'traitType']}
                            rules={[
                              {
                                required: true,
                                message: text.messages.traitTypeRequired,
                              },
                              {
                                max: 50,
                                message: text.messages.traitTypeMaxLength,
                              },
                              {
                                whitespace: true,
                                message:
                                  text.messages.cannotBeEmptyOrOnlySpaces,
                              },
                            ]}
                          >
                            <Input
                              placeholder={text.form.traitType}
                              size="large"
                              maxLength={50}
                              showCount
                              disabled={!canEditFields}
                              allowClear
                            />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'value']}
                            rules={[
                              {
                                required: true,
                                message: text.messages.valueRequired,
                              },
                              {
                                max: 50,
                                message: text.messages.valueMaxLength,
                              },
                              {
                                whitespace: true,
                                message:
                                  text.messages.cannotBeEmptyOrOnlySpaces,
                              },
                            ]}
                          >
                            <Input
                              placeholder={text.form.value}
                              size="large"
                              maxLength={50}
                              showCount
                              disabled={!canEditFields}
                              allowClear
                            />
                          </Form.Item>
                          {canEditFields && (
                            <MinusCircleOutlined
                              onClick={() => remove(name)}
                              className="text-red-500"
                              rev={undefined}
                            />
                          )}
                        </Space>
                      ))}
                      {canEditFields && (
                        <Button
                          type="dashed"
                          onClick={() => add()}
                          block
                          icon={<PlusOutlined rev={undefined} />}
                          className="mb-4 text-white"
                          size="large"
                          disabled={fields.length >= maxTraits}
                        >
                          {text.form.addTrait}
                        </Button>
                      )}
                      {fields.length >= 10 && (
                        <div className="text-red-500 text-xs mt-1">
                          {text.messages.traitsMaxLength}
                        </div>
                      )}
                    </div>
                  )}
                </Form.List>
                <Row gutter={16} className="mt-8 gap-3">
                  {mode !== 'view' &&
                    (canCreateForm ||
                      data?.approvalStatus === APPROVAL_STATUS.draft) && (
                      <>
                        <UiButton
                          className="w-full"
                          isBorder={true}
                          isGradient={false}
                          size={BUTTON_SIZES.LG}
                          title={text.buttons.saveDraft}
                          handleClick={() => {
                            setIsDraft(true)
                            form.submit()
                          }}
                        />
                        <UiButton
                          className="w-full"
                          title={text.buttons.create}
                          size={BUTTON_SIZES.LG}
                          isDisabled={!form.isFieldsValidating}
                          handleClick={() => {
                            setIsDraft(false)
                            form.submit()
                          }}
                        />
                      </>
                    )}
                  {canUpdateForm &&
                    data?.approvalStatus !== APPROVAL_STATUS.draft && (
                      <>
                        <UiButton
                          className="w-full"
                          isBorder={true}
                          isGradient={false}
                          size={BUTTON_SIZES.LG}
                          title={text.buttons.saveDraft}
                          handleClick={() => {
                            setIsDraft(true)
                            form.submit()
                          }}
                        />
                        <UiButton
                          className="w-full"
                          size={BUTTON_SIZES.LG}
                          title={text.buttons.update}
                          isDisabled={!form.isFieldsValidating}
                          handleClick={() => {
                            setIsDraft(false)
                            form.submit()
                          }}
                        />
                      </>
                    )}
                  {canApproveForm && (
                    <>
                      <UiButton
                        className="w-full"
                        isBorder={true}
                        isGradient={false}
                        size={BUTTON_SIZES.LG}
                        title={text.buttons.reject}
                        handleClick={() => onFinishApprove(false)}
                      />
                      <UiButton
                        className="w-full"
                        size={BUTTON_SIZES.LG}
                        title={text.buttons.approve}
                        handleClick={() => onFinishApprove(true)}
                      />
                    </>
                  )}
                </Row>
              </Form>
            </Col>
          </Row>
        </div>
      </div>
    </Spin>
  )
}

export default FormNFT
