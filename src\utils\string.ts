import {
  ENFT_TYPE,
  ETYPE_TIME_OPTION,
  METADATA_STATUS,
  SORT_TYPE,
  renderMetadataStatus,
  renderSortCondition,
  renderTypeNft,
} from '@/constants'

export const IMAGE = {
  giftBox: require('public/images/giftBox.webp'),
  polygon: require('public/images/polygon.png'),
  bannerLogin: require('public/images/banner_login.png'),
  logoBrand: require('public/logos/logo_brand.png'),
  logoBrandSignin: require('public/logos/logo_brand_signin_new.png'),
  logoX: require('public/logos/logo_x.png'),
  logoFb: require('public/logos/logo_fb.png'),
  logoIg: require('public/logos/logo_ig.png'),
  iconUserDash: require('public/icons/icon_user_dashboard.png'),
  iconNftDash: require('public/icons/icon_nft_dashboard.png'),
  iconPaymentTax: require('public/icons/icon_tax_payment.png'),
  defaultImage: require('public/images/default-image.jpg'),
  default404: require('public/images/default_404.png'),
  aboutImage1: require('public/images/about_image1.jpg'),
  aboutIllustration: require('public/images/about_illustration.jpg'),
  aboutIllustrationMobile: require('public/images/about_illustration_mobile.png'),
  aboutImgLogo: require('public/images/about_img_logo.jpg'),
  aboutPhones: require('public/images/about_phones.png'),
  aboutImgBigCard: require('public/images/about_img_big_card.png'),
  aboutImgSmallCard01: require('public/images/about_img_small_card01.png'),
  aboutImgSmallCard02: require('public/images/about_img_small_card02.png'),
  aboutImgSmallCard03: require('public/images/about_img_small_card03.png'),
  aboutImgSmallCards: require('public/images/aboutImgSmallCards.jpg'),
  faqsWarning: require('public/images/faqs_warning.png'),
  faqsReceiveEmail: require('public/images/receive_email.png'),
  whatIsAGetWallet: require('public/images/what-is-a-get-wallet.png'),
  howToStayProtectedInWeb3: require('public/images/how-to-stay-protected-in-web3.png'),
  whatIsMinting: require('public/images/what-is-minting.png'),
  whatIsAnNft: require('public/images/what-is-an-nft.png'),
}

export const TIME_UTC = 'YYYY-MM-DD hh:mm:ss a'

export const STRING_MODAL = {
  createNFT: 'NFT作成をキャンセルしてもよろしいですか。',
  edit: 'NFT情報の編集をキャンセルしてもよろしいですか。',
  loading: '処理中',
}

export const BUTTON = {
  OK: 'OK',
  create: '作成する',
  back: '戻る',
  save: '保存する',
  delete: '削除する',
  search: '検索',
  createNFTNew: 'Create NFT',
  sale: '販売する',
  cancel: 'キャンセル',
  claimNft: 'コード送信',
}

export const MESSAGE_FORM = {
  image: 'イメージをアップロードしてください。',
  imageDifferentType:
    'JPG、PNG、WEBP、GIFの中、どちらかのフォーマットのファイルをアップロードしてください。',
  imageLogoDifferentType:
    'JPG、PNG、GIF、SVGの中、どちらかのフォーマットのファイルをアップロードしてください。',
  recommendImage: '300x300サイズのファイルはお勧めしております。',
  upload10MB: 'アップロードファイルは10MB以下のものでなければなりません。',
  uploadImageHaveError: '画像のアップロードのエラーが発生しました。',
  uploadType: 'イメージファイルをアップロードしてください。',
  name: 'タイトルを入力してください。',
  maximumNumber: '販売個数を入力してください。',
  tags: 'タグを入力してください。',
  attribute: 'を入力してください。',
  itemType: 'ふるさと納税版NFT',
  introduction: '紹介文を入力してください。',
  limit3000character: '内容を3000文字以内に入力してください。',
  successCreateNFT: 'NFT発行が成功しました。',
  successEditNFT: 'NFT編集が成功しました',
  creatorName: '発行者名を入力してください。',
  greaterZero: '1以上の数字を入力してください。',
  successCreateBanner: 'バナー作成が成功しました。',
  bannerRequire: 'バナーを選択してください。',
  bannerContentRequire: '内容 を入力してください。',
  creditCard: '金額を入力してください。',
  successEditSale: '販売設定が成功しました。',
  priceGreaterZero: '0より大きい金額を入力してください。',
  successCancelSale: '販売停止にしました。',
  errCancelSale: '販売停止できませんでした。',
  cannotCancelNftImport:
    'このNFTは受け取られていないので、販売停止にすることができません。',
  listSaleSetting: '統一のNFTタイプのNFTを選択してください。',
  maxCharacter: '文字以上入力してはいけません',
  tagExist: 'タグが既に存在しています。',
  dataNotAppropriate: 'データは適切ではありません。',
  errorCommon: 'エラーが発生しました。改めてNFTを作成してください。',
  whiteSpace: 'スペースだけ入力してはいけません。',
  typeDataTag: 'データは適切ではありません。',
  productId: '商品IDを入力してください。',
  intermediaryId: '中間業者IDを入力してください。',
  dataEmoji: 'タグのデータは不適切ではありません。',
  noData: 'データが見つかりません。',
  errorCallServer: 'エラーが発生しました。数分後にもう一度お試しください',
  titleIsRequire: 'タイトルが必要です。',
  formatIsIncorrect: 'ファイルの形式は正しくありません。',
  urlNotMatch: 'URLは適切ではありません。',
  urlRequire: 'ふるさとチョイスへのリンクを入力してください。',
  notTheSameStatus:
    '販売されているNFTだけに対して販売停止にすることができます。',
  notFullWidth: '全角文字を入力しないでください。',
  nftSelling: '販売停止にされているNFTだけに対して販売設定することができます。',
  notSaleFusanto: 'ふるさと納税版NFTは一斉に販売設定をしてはいけません。',
  importSuccessCSV: 'CSVファイルのインポートが成功しました。',
  importErrorCSV: 'CSVファイルのインポートが失敗しました。',
  uploadOver10MbCSV: '10MB以下のCSVファイルをアップロードしてください。',
  chooseCSV: 'CSVファイルを選択してください。',
  giftCode: 'NFTを受け取るコードを入力してください。',
  wrongInputCode: '正しいコードを入力してください。',
  giftCodeUsed: 'このコードは既に使われました。又は、正しくではありません。',
  nftClaimed: 'このNFTは既に受け取られました。',
  nftSoldOut: 'このNFTは既に売り切れました。',
  somethingWentWrong:
    '何らかのエラーが発生しました。後ほどもう一度実行してください。',
  productIdExist: '中間業者ID・商品IDは既に存在しています。',
  urlGoogleSheet: 'URLを入力してください。',
  invalidLink: 'URLは正しくありません。',
}

export const PLACE_HOLDER = {
  uploadImage: 'JPG, PNG, WEBP,GIF. Max 10MB',
  searchMyNFT: ' NFTのタイトル、発行者名、タグで検索する。',
  searchGift: 'タイトル、メールアドレス、中間業者ID、商品IDで検索する。',
}

export const NFTのタイプ = [
  {
    label: renderTypeNft[ENFT_TYPE.all],
    value: ENFT_TYPE.all,
  },
  {
    label: renderTypeNft[ENFT_TYPE.ふるさと納税版],
    value: ENFT_TYPE.ふるさと納税版,
  },
]

export const ステータス = [
  {
    label: renderMetadataStatus[METADATA_STATUS.all],
    value: METADATA_STATUS.all,
  },
  {
    label: renderMetadataStatus[METADATA_STATUS.notSell],
    value: METADATA_STATUS.notSell,
  },
  {
    label: renderMetadataStatus[METADATA_STATUS.selling],
    value: METADATA_STATUS.selling,
  },
  {
    label: renderMetadataStatus[METADATA_STATUS.soldOut],
    value: METADATA_STATUS.soldOut,
  },
]

export const sortPrice = [
  {
    label: renderSortCondition[SORT_TYPE.lastCreated],
    value: SORT_TYPE.lastCreated,
  },
  {
    label: renderSortCondition[SORT_TYPE.oldCreated],
    value: SORT_TYPE.oldCreated,
  },
]

export const TEXT_STATUS_UPLOAD_FILE = {
  400: MESSAGE_FORM.formatIsIncorrect,
  413: MESSAGE_FORM.upload10MB,
}

export const TYPES_VIDEO_ALLOWED = [
  'video/mp4',
  'video/mov',
  'video/wmv',
  'video/avi',
  'video/x-ms-wmv',
  'video/quicktime',
]

export const TYPES_FILE_ALLOWED = [
  'text/csv',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/pdf',
  'text/plain',
]

export const TYPES_IMAGE_ALLOWED = [
  'image/png',
  'image/gif',
  'image/jpeg',
  'image/jpg',
  'image/webp',
  'image/svg+xml',
]

export const optionDay = [
  {
    label: '一日前',
    value: ETYPE_TIME_OPTION.yesterday,
  },
  {
    label: '過去7日間',
    value: ETYPE_TIME_OPTION.lastWeek,
  },
  {
    label: '過去3ヶ月',
    value: ETYPE_TIME_OPTION.threeMonths,
  },
  {
    label: '過去6ヶ月',
    value: ETYPE_TIME_OPTION.sixMonths,
  },
]

export const dummyDataSold = [
  {
    title: 'JAN',
    amount: 90,
  },
  {
    title: 'FEB',
    amount: 100,
  },
  {
    title: 'MAR',
    amount: 250,
  },
  {
    title: 'APR',
    amount: 70,
  },
  {
    title: 'MAY',
    amount: 220,
  },
  {
    title: 'JUN',
    amount: 102,
  },
]
