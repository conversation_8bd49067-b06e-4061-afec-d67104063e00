import { ROUTES } from '@/constants'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { useMemo } from 'react'

interface NextHeadProps {
  title?: string
  description?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  canonicalUrl?: string
}

const NextHead = ({
  title,
  description,
  ogTitle,
  ogDescription,
  ogImage,
  canonicalUrl,
}: NextHeadProps) => {
  const { pathname } = useRouter()

  // Map router → title
  const defaultTitle = useMemo(() => {
    const mapping: Record<string, string> = {
      [ROUTES.adminDashboard]: 'ダッシュボード',
      [ROUTES.adminCollections]: 'コレクション管理',
      [ROUTES.adminNfts]: 'NFT管理',
      [ROUTES.adminBanners]: 'バナー管理',
      [ROUTES.adminUsers]: 'ユーザー管理',
      [ROUTES.adminTransactions]: '取引管理',
      [ROUTES.adminNews]: 'ニュース管理',
      [ROUTES.adminAdminAccounts]: 'アカウント管理',
      [ROUTES.privacyPolicy]: 'Glitters Privacy Policy',
      [ROUTES.termsOfUse]: 'Glitters Terms of Service',
      [ROUTES.collections]: 'Collections',
      [ROUTES.knowledge]: 'Knowledge',
      [ROUTES.news]: 'News',
      [ROUTES.contact]: 'Contact',
    }

    // Find key that pathname starts with
    const matched = Object.keys(mapping).find((key) => pathname.startsWith(key))

    return matched ? mapping[matched] : 'GLITTERS'
  }, [pathname])

  return (
    <Head>
      <title>{title || defaultTitle}</title>
      {description && <meta name="description" content={description} />}
      {ogTitle && <meta property="og:title" content={ogTitle} />}
      {ogDescription && <meta property="og:description" content={ogDescription} />}
      {ogImage && <meta property="og:image" content={ogImage} />}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      <link rel="icon" href="/favicon.png" />
      <meta
        name="description"
        content="Create campaigns with blockchain technology"
      />
      <meta name="robots" content="index, follow" />
      <meta
        name="googlebot"
        content="max-video-preview:-1, max-image-preview:large, max-snippet:-1"
      />
    </Head>
  )
}

export default NextHead
