@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap');

@import './variables.scss';
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

* {
  font-family: 'Montserrat', sans-serif;
}

@layer base {
  h1 {
    @apply text-3xl;
  }

  h2 {
    @apply text-2xl;
  }

  h3 {
    @apply text-xl;
  }

  h4 {
    @apply text-base;
  }

  h5 {
    @apply text-sm;
  }

  h6 {
    @apply text-xs;
  }
}

//-------- Scroll bar ------------
html *::-webkit-scrollbar {
  border-radius: 0;
  width: 8px;
  height: 8px;
}

html *::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #f2b516;

  &:hover {
    @apply cursor-pointer;
  }
}

html *::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: transparent;
}

// ------------- Typography ------------
@for $i from 1 through 11 {
  .line-clamp-#{$i} {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $i;
  }
}

@media only screen and (max-width: 639px) {
  @for $i from 1 through 11 {
    .mobile-line-clamp-#{$i} {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: $i;
    }
  }
}

// ------------- Layout ---------------
%flexCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}

.wrapper {
  max-width: var(--max-with-wrapper);
  @apply mx-auto;
}

.main {
  &-layout {
    @apply bg-tailwindBgMain;
  }

  &-content {
    @apply w-4/6 mx-auto max-[1580px]:w-5/6 max-2xl:w-5/6 max-xl:w-full max-xl:px-16 max-lg:w-full max-lg:px-7 max-md:px-5 max-[360px]:px-3;
  }

  &-content-home {
    @apply px-8 max-xl:px-0;
  }

  &-content-admin {
    @apply w-[70%] mx-auto max-[1580px]:w-5/6 max-2xl:w-11/12 max-xl:w-full max-xl:px-4;
  }
}

.full-container {
  height: calc(100vh - var(--height-header));
}

.min-h-container {
  min-height: calc(100vh - var(--height-header));
}

.sub-menu {
  @apply max-h-[500px] overflow-y-auto absolute bg-white shadow-md py-3 gap-4 z-10 overflow-hidden w-full top-[calc(100%_+_12px)] left-0 rounded-lg border transition-all px-2;

  &__item {
    @apply flex bg-transparent p-2 hover:bg-tailwindBrand1 text-sm rounded-sm w-full text-left text-tailwindNeutral1 cursor-pointer;
  }
}

// ------------- Buttons --------------
%smallButton {
  @apply h-8 w-40 text-sm;
}

%bgActive {
  @apply bg-gradient-to-t from-[#098798] to-[#4cf0ff];
}

%hoverBgActive {
  @apply hover:bg-gradient-to-t from-[#098798] to-[#4cf0ff];
}

%btnDisabled {
  @apply cursor-not-allowed;
}

.bg-active {
  @extend %bgActive;
}

.hover-bg-active:hover {
  @extend %hoverBgActive;
}

.btn {
  &-primary {
    @apply flex justify-center items-center capitalize rounded-3xl border-2 border-tailwindBrand1 h-12 w-72 font-semibold;
    @extend %hoverBgActive;

    &.--small {
      @extend %smallButton;
    }

    &.--disabled {
      @extend %btnDisabled;
    }
  }

  &-linear {
    @apply flex items-center justify-center overflow-hidden rounded-lg w-80 h-20 text-2xl text-white font-semibold shadow;

    button {
      @extend %hoverBgActive;
      @apply capitalize w-full h-full border-solid shadow border-[6px] hover:border-0;
      border-image-slice: 1;
    }

    &.--jade button {
      border-image-source: linear-gradient(to bottom, #2ce5c1, #86daff);
    }

    &.--purple button {
      border-image-source: linear-gradient(to bottom, #b39aff, #a4caff);
    }
  }
}

// ------------- Pseudo ---------------
%afterFull {
  @apply relative after:absolute after:top-0 after:left-0 after:w-full after:h-full;
}

.after-full {
  @extend %afterFull;
}

%overlay {
  @extend %afterFull;
  @apply after:z-0 after:opacity-50;

  * {
    z-index: 10;
  }
}

.overlay {
  &-dark {
    @extend %overlay;
    @apply after:bg-black;
  }

  &-gradient {
    @apply after:content-[''] after:block after:absolute after:w-full after:h-full after:top-0 after:left-0 after:bg-gradient-to-b from-[#ffffff00] via-[#fcfcfc1f] to-[#0000009c];
  }
}

.wrapper-content {
  &::-webkit-scrollbar-thumb {
    background-color: var(--active-color);
  }

  &::-webkit-scrollbar {
    width: 4px;
  }

  > div {
    display: none !important;

    &:first-child {
      display: flex;
      align-items: center;
      justify-content: center;
      display: block !important;
      width: 100% !important;
      margin: 0 !important;

      iframe {
        width: 100% !important;

        .css-1dbjc4n.r-1ets6dv.r-1q9bdsx.r-rs99b7.r-1loqt21.r-vakc41.r-y54riw.r-1udh08x {
          width: 100% !important;
        }
      }
    }
  }
}

// ------------ Carousel --------------
.carousel-custom {
  .control-dots {
    z-index: 20 !important;
    margin-bottom: 0 !important;
    @extend %flexCenter;

    .dot {
      width: 12px !important;
      height: 12px !important;

      &.selected {
        width: 16px !important;
        height: 16px !important;
        background: var(--active-color);
      }
    }
  }

  .carousel-slider {
    @apply pb-6 max-sm:pb-8;
  }
}

:where(.css-dev-only-do-not-override-amq5gd).ant-carousel
  .slick-dots
  li.slick-active
  button {
  background: #f2c157;
  opacity: 1;
}

:where(.css-dev-only-do-not-override-amq5gd).ant-carousel
  .slick-dots
  li
  button {
  background: white;
  opacity: 1;
}

:where(.css-dev-only-do-not-override-amq5gd).ant-carousel .slick-dots-bottom {
  bottom: 25px;
  padding: 0 15px;
}

// ------------ pagination --------------
.ant-pagination {
  .ant-pagination-item,
  .ant-pagination-prev,
  .ant-pagination-next {
    border: 1px solid #9e9e9e;
    min-width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    background-color: none;
  }

  .ant-pagination-item-active {
    &:hover {
      border-color: #fff;
    }

    border-color: #f2b516;
    background-color: transparent;

    a {
      color: #f2b516;

      &:hover {
        color: #f2b516;
      }
    }
  }
}

@media screen and (max-width: 639px) {
  .ant-pagination {
    li {
      height: 32px !important;
      min-width: 32px !important;

      > button > span {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      > a {
        font-size: 12px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// --------------- Input --------------
.ant-input {
  // border-color: #9e9e9e;
  font-size: 16px;

  &:hover {
    border-color: #f2b516;
  }

  &:focus {
    border-color: #f2b516;
  }
}

.ant-checkbox-checked .ant-checkbox-inner::after {
  border-color: black !important;
}

.ant-checkbox-checked .ant-checkbox-inner::after {
  border-color: black !important;
}

.ant-form-item .ant-form-item-label > label {
  font-weight: bold;
}

// --------------- Table --------------
.ant-table-wrapper .ant-table-thead > tr > th {
  color: #9e9e9e;
  font-size: 14px;
}

.ant-table-tbody > tr:hover {
  cursor: pointer;
}

.reset-checkbox {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
}

.custom-select {
  table {
    thead > tr {
      th:first-child {
        min-width: 80px !important;
      }
    }

    tbody {
      tr {
        td:first-child {
          min-width: 80px !important;
        }
      }
    }
  }
}

.custom-table-header {
  table {
    thead > tr {
      td,
      th {
        background-color: white !important;
      }

      td::before {
        visibility: hidden;
      }

      th:nth-child(2) {
        color: black;
        font-weight: 700;
        font-size: 1.875rem;
        line-height: 2.25rem;
      }
    }
  }

  tbody {
    tr {
      td {
        border: 1px solid #f0f0f0;
      }
    }
  }
}

// --------------- Checkbox --------------
.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
  .ant-checkbox-inner {
  border-color: #f2c157;
}

.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
  .ant-checkbox-checked:not(.ant-checkbox-disabled)
  .ant-checkbox-inner {
  background-color: #f2c157;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #f2c157;
  border-color: #f2c157;
}

.ant-checkbox-indeterminate .ant-checkbox-inner:after {
  background-color: #f2c157;
}

.custom-checkbox {
  display: flex;
  align-items: center;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  line-height: 1.8;
  letter-spacing: 0.02em;
  color: #f2c157;

  .ant-checkbox-inner {
    width: 24px;
    height: 24px;
    background-color: white;
    border: 1.5px solid #0a0a0a;
    border-radius: 0;
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #f2c157;
    border-color: #0a0a0a;
  }

  .ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: #0a0a0a;
    content: url('data:image/svg+xml; utf8, <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 6L5 10L14 1" stroke="#0A0A0A" strokeWidth="1.5" strokeLinecap="round"/></svg>');
  }

  .ant-checkbox + span {
    margin-left: 4px;
  }
}

// ------------ pagination --------------
.ant-tabs-tab {
  .ant-tabs-tab-btn {
    color: #9e9e9e;
  }
}

.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab {
  border-radius: 6px 6px 0px 0px;
}

.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #000;
}

.ant-tabs .ant-tabs-ink-bar {
  background: #f2b516;
}

// ------------ modal --------------
.ant-modal .ant-modal-content {
  border-radius: 6px;
}

// ------------ dragger upload --------------
.ant-upload-wrapper .ant-upload-drag {
  border: none;
  background-color: transparent;
}

// ------------ radio --------------
.ant-radio-wrapper:hover .ant-radio-inner {
  border-color: #f2b516;
}

.ant-radio-wrapper {
  .ant-radio-checked {
    &::after {
      border-color: #f2b516;
    }

    .ant-radio-inner {
      border-color: #f2b516;
      background-color: #000;
      height: 22px;
      width: 22px;
      &::after {
        background-color: #f2b516;
        zoom: 2;
      }
    }
  }
}
.ant-radio {
  .ant-radio-inner {
    height: 22px;
    width: 22px;
  }
}
.ant-radio-wrapper-disabled {
  color: #9e9e9e;
}

// ------------ switch --------------
.ant-switch {
  background: #e3e3e3;
}

.ant-switch.ant-switch-checked {
  background: #52de20;
}

.input-radio {
  @apply bg-transparent reset-checkbox relative rounded-full w-4 h-4 border border-white block checked:after:bg-tailwindBrand1 after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:w-3 after:h-3 after:rounded-full;
}

// ------------ select --------------
.ant-select-selection-item {
  @media (min-width: 1366px) and (max-width: 1500px) {
    font-size: 12px;
  }
}

// ------------ notification --------------
.ant-notification
  .ant-notification-notice-wrapper
  .ant-notification-notice-with-icon
  .ant-notification-notice-message {
  margin-bottom: 8px;
  margin-inline-start: 30px;
  margin-inline-end: 25px;
  font-size: 16px;
}

.ant-notification-notice-icon {
  height: 60%;
}

// ------------ spin antd --------------
.ant-spin {
  .ant-spin-dot-item {
    background-color: #f2b516;
  }

  .ant-spin-text {
    color: #f2b516;
  }
}

input[type='checkbox']:checked {
  &::after {
    left: 2px;
    top: 50%;
    transform: translateY(-50%);
    content: url('data:image/svg+xml; utf8, <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="aqua" viewBox="0 0 24 24"><path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z"/></svg>');
  }
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

// ------------ layout antd --------------
.ant-layout .ant-layout-sider {
  @apply shadow-lg min-w-[230px] #{!important};
}

// mdEditor
.notShowOptions {
  > div {
    &:first-child {
      display: none;
    }
  }
}

.mdEditor {
  max-height: 80vh;
  font-size: 16px;

  // .button.button-type-header,
  .button.button-type-table,
  .button.button-type-quote,
  .button.button-type-wrap,
  .button.button-type-code-inline,
  .button.button-type-code-block,
  .button.button-type-underline,
  .button-type-image {
    display: none !important;
  }

  textarea {
    &::placeholder {
      color: var(--placeholder-color);
    }

    &:last-child {
      border-right: none !important;
    }

    background: transparent !important;
    border-right: 1px solid var(--active-color) !important;
    color: white !important;
    font-size: 16px !important;
  }

  section {
    border-right: none !important;

    &:last-child {
      display: none;
    }

    div,
    p {
      color: white !important;
      font-size: 16px;
    }
  }

  .rc-md-navigation {
    background: transparent !important;
    border-bottom: 1px solid var(--color-border-input) !important;
  }

  th {
    color: black;
  }

  code {
    color: black;
  }
}

.rc-md-editor.full.mdEditor {
  width: 98% !important;
  height: 98vh !important;
  max-height: 98vh !important;
  left: 1% !important;
  top: 1vh !important;
}

.placeholder-color {
  &::placeholder {
    color: var(--placeholder-color);
    opacity: 1;
  }

  &:-ms-input-placeholder {
    color: var(--placeholder-color);
  }

  &::-ms-input-placeholder {
    color: var(--placeholder-color);
  }
}

.add-style-default {
  ul,
  ol {
    padding-left: 40px !important;
  }

  ul {
    list-style: disc;
  }

  ol {
    list-style: auto;
  }

  table {
    @apply table-auto;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply text-sm my-0;
  }

  br {
    content: '';
    display: block;
  }

  &-top {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      @apply text-lg my-0;
    }
  }
}

.content-des-campaign {
  a {
    text-decoration: underline;

    &:hover {
      color: var(--active-color);
    }
  }
}

.word-break {
  word-break: break-word;
}

.title-banner {
  span {
    @apply text-[58px] font-medium;
  }
}

@media screen and (max-width: 639px) {
  .hidden-scrollbar {
    scrollbar-width: none;
  }

  .hidden-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

.border-table-mobile {
  @apply relative after:content-[''] after:block after:absolute after:w-[1px] after:h-[calc(100%-18px)] after:bg-tailwindBrand1 after:top-0 after:left-[0px] before:content-[''] before:block before:absolute before:w-[1px] before:h-[calc(100%-18px)] before:bg-tailwindBrand1 before:top-0 before:right-[0px];
}

.prevent-select {
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

//loading.........
$color_1: #f2b516;
$font-family_1: 'Noto Sans JP', sans-serif;
$border-top-color_1: #f2b516;

@keyframes pulse {
  0% {
    transform: scale(0.6);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0;
  }

  100% {
    transform: scale(0.6);
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loader-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loader {
  width: 70px;
  height: 70px;
  position: relative;

  &:before {
    content: '';
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 6px solid #ffb805;
    position: absolute;
    top: 0;
    left: 0;
    animation: pulse 1s ease-in-out infinite;
  }

  &:after {
    content: '';
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 6px solid transparent;
    border-top-color: $border-top-color_1;
    position: absolute;
    top: 0;
    left: 0;
    animation: spin 2s linear infinite;
  }
}

.loader.miniSize {
  width: 50px;
  height: 50px;

  &:before {
    width: 50px;
    height: 50px;
  }

  &:after {
    width: 50px;
    height: 50px;
  }
}

.loader-text {
  font-size: 24px;
  margin-top: 20px;
  color: $color_1;
  font-family: $font-family_1;
  text-align: center;
  text-transform: uppercase;
}

.content {
  display: none;
}

.loaded {
  .loader-container {
    display: none;
  }

  .content {
    display: block;
  }
}

.avatar-upload {
  .ant-upload-list-item-container {
    display: none;
  }
}

.hidden-side-menu-faqs {
  @media screen and (max-width: 1200px) {
    display: none;
  }
}

.hidden-side-select {
  @media screen and (min-width: 1200px) {
    display: none;
  }
}

/* carousel custom dots styles */
.custom-carousel-dots {
  bottom: -15px !important;
}

.custom-carousel-dots button {
  background: white !important;
}

.custom-carousel-dots .slick-active button {
  background: #f2c157 !important;
}

.truncate-select {
  .ant-select-selection-item {
    width: 300px;
  }
}

/* css for static page starts */
.static-page {
  font-style: normal;
  letter-spacing: 0.02em;
  margin: auto;
}

.static-page h1,
.static-page h2,
.static-page h3,
.static-page h4 {
  margin-top: 20px;
  color: white;
}

.static-page p,
.static-page li {
  color: #e5e5e5;
}

.static-page ul > li {
  list-style-type: disc;
}

.static-page ol > li {
  list-style-type: decimal;
}

.static-page ol,
.static-page ul {
  margin-left: 20px;
}

.static-page a {
  color: #f2c157;
}

.static-page a:hover {
  color: rgba(242, 193, 87, 0.75);
}

.static-page table {
  margin-top: 20px;
}

.static-page th,
.static-page td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.static-page p,
.static-page span,
.static-page ul,
.static-page li,
.static-page td {
  font-weight: 275;
}

/* css for static page ends */

.tinymce-content * {
  all: revert;
}

.ant-table-cell {
  cursor: default;
  text-wrap: nowrap;
}

.new-content {
  img {
    max-width: 100%;
  }

  button:has(img) {
    border: none !important;
  }
}

.privacy-policy {
  * {
    max-width: 100%;
  }

  .tb-access {
    overflow-x: auto;
  }
}

.ant-breadcrumb-link a {
  color: white !important;
  font-family: 'Montserrat';
  font-weight: 300;
  font-size: 14px;
  line-height: 170%;
  letter-spacing: 0.02em;
}

.ant-breadcrumb > ol > li:last-child .ant-breadcrumb-link a {
  font-weight: 400; /* only last breadcrumb link bold */
}

:where(.css-dev-only-do-not-override-1o9j7fa).ant-breadcrumb a:hover {
  color: #fff;
  background: none !important;
}

.custom-table {
  .ant-table-thead > tr > th {
    border-right: none !important;
    background: transparent !important;
  }

  .ant-pagination {
    .ant-pagination-item {
      border: none !important;
      border-radius: 8px;
      width: 40px;
      height: 40px;
      overflow: hidden;

      a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 14px;
        color: #f2c157 !important;
        font-weight: 400 !important;
      }

      /* hover item thường */
      &:hover a {
        color: #fff !important;
        background: none !important;
      }
    }

    /* khi active */
    .ant-pagination-item-active {
      background: #1c1c1c !important;

      a {
        color: #fff !important;
        font-weight: 400 !important;
        background: transparent !important;
      }

      /* hover active giữ nguyên */
      &:hover a {
        color: #fff !important;
        background: transparent !important;
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      border: none !important;

      button {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        color: #f2c157 !important;
        font-weight: 400 !important;
      }

      &:hover button {
        background: none !important;
        color: #fff !important;
      }
    }
  }
}

.custom-pagination {
  .ant-pagination-item {
    border: none !important;
    border-radius: 8px;
    width: 40px;
    height: 40px;

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #f2c157 !important;
      font-size: 14px;
    }

    &:hover a {
      color: #fff !important;
    }
  }

  .ant-pagination-item-active {
    background: #1c1c1c !important;

    a {
      color: #fff !important;
      font-weight: 400 !important;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border: none !important;

    .ant-pagination-item-link {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #f2c157 !important;
    }
    .ant-pagination-item-link:hover {
      background: transparent !important;
    }
  }
}

.ant-badge-status-dot {
  width: 12px !important;
  height: 12px !important;
}
.ant-badge-status-default {
  background-color: transparent !important;
  border: 1px solid #858585 !important;
}
