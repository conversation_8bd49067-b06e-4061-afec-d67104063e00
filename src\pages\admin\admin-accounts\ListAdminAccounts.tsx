import { PlusOutlined } from '@ant-design/icons'
import { Input, Select } from 'antd'
import {
  ADMIN_ROLE,
  ACCOUNT_STATUS,
  ACCOUNT_STATUS_MAP,
  ADMIN_ROLE_MAP,
  DEFAULT_PAGE_SIZE,
} from '../../../constants'
import {
  IconCheckCircle,
  IconPen,
  IconRemoveCircle,
  IconSearch,
} from '../../../icons'
import moment from 'moment'
import { useState, useEffect } from 'react'
import { Admin, AdminSearch } from '../../../interfaces'
import { useStore } from '../../../store'
import { useMutation } from 'wagmi'
import {
  enableAdmin,
  disableAdmin,
  getListAdmins,
} from '../../../services/apiCall/admin'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import CreateAdminAccountModal from './CreateAdminAccountModal'
import UiButton from '@/components/ui/Button'
import { TextButton } from '@/components/buttons'
import AdminTable from '@/components/table/AdminTable'
export default function ListAdminAccount() {
  const queryClient = useQueryClient()
  const [email, setEmail] = useState('')
  const [role, setRole] = useState<string>('')
  const [status, setStatus] = useState<string>('')
  const { setLoading } = useStore()
  const [adminQueryParams, setAdminQueryParams] = useState<AdminSearch>({
    pageSize: DEFAULT_PAGE_SIZE,
    pageIndex: 1,
  })
  const { data: listAdmins, refetch } = useQuery({
    queryKey: ['list-admins', adminQueryParams],
    queryFn: () => getListAdmins(adminQueryParams),
  })
  const { isPending: isEnableAdminPending, mutate: mutateEnableAdmin } =
    useMutation({
      mutationFn: (adminId: string) => {
        setLoading(true)
        return enableAdmin(adminId)
      },
      onSuccess: () => {
        setLoading(false)
        // Refetch the list after enabling
        refetch()
      },
      onError: () => {
        setLoading(false)
      },
    })
  const { isPending: isDisableAdminPending, mutate: mutateDisableAdmin } =
    useMutation({
      mutationFn: (adminId: string) => {
        setLoading(true)
        return disableAdmin(adminId)
      },
      onSuccess: () => {
        setLoading(false)
        // Refetch the list after disabling
        refetch()
      },
    })

  // Update query params when filters change
  useEffect(() => {
    setAdminQueryParams((prev) => {
      return {
        ...prev,
        email: email || undefined,
        role: (role as ADMIN_ROLE) || undefined,
        status: (status as ACCOUNT_STATUS) || undefined,
        pageIndex: 1,
      }
    })
  }, [email, role, status])

  const text = {
    email: 'メールアドレス',
    role: '役割',
    status: '状態',
    createdAt: '作成日',
    action: 'アクション',
    edit: '編集',
    enable: '有効化',
    disable: '無効化',
    searchByEmailAddress: 'メールアドレスで検索',
    allRoles: 'すべての役割',
    allStatuses: 'すべてのステータス',
    createAdmin: '管理者アカウント作成',
  }

  const columns = [
    {
      title: text.email,
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: text.role,
      dataIndex: 'role',
      key: 'role',
      render: (role: ADMIN_ROLE) => ADMIN_ROLE_MAP.get(role) || role,
    },
    {
      title: text.status,
      dataIndex: 'status',
      key: 'status',
      render: (status: ACCOUNT_STATUS) =>
        ACCOUNT_STATUS_MAP.get(status) || status,
    },
    {
      title: text.createdAt,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: Date) => moment(createdAt).format('YYYY-MM-DD'),
    },
    {
      title: text.action,
      key: 'status',
      render: (_status: ACCOUNT_STATUS, record: Admin) => {
        return (
          <div className="flex space-x-6">
            <TextButton
              onClick={() => setEditAdmin(record)}
              title={''}
              startIcon={<IconPen />}
            />
            {record.status === ACCOUNT_STATUS.blocked ? (
              <TextButton
                disabled={isEnableAdminPending}
                onClick={() => mutateEnableAdmin(record._id)}
                title={text.enable}
                startIcon={<IconCheckCircle />}
              />
            ) : (
              <TextButton
                disabled={isDisableAdminPending}
                onClick={() => mutateDisableAdmin(record._id)}
                title={text.disable}
                startIcon={<IconRemoveCircle />}
              />
            )}
          </div>
        )
      },
    },
  ]

  // Handle pagination
  const handleTableChange = (pagination: any) => {
    setAdminQueryParams((prev) => ({
      ...prev,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize,
    }))
  }

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editAdmin, setEditAdmin] = useState<Admin | null>(null)

  return (
    <>
      <div>
        <div className="[@media(min-width:1150px)]:flex mb-8 justify-between space-x-8 ">
          <div className="flex-wrap [@media(min-width:975px)]:flex [@media(min-width:975px)]:flex-nowrap space-x-0 h-full">
            <div className="flex mr-4 items-center w-full h-full">
              <Input
                size="small"
                placeholder={text.searchByEmailAddress}
                suffix={<IconSearch height={20} width={20} />}
                className="h-[44px]"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                allowClear
              />
            </div>

            <div className="flex md:flex-1 space-x-4 [@media(max-width:975px)]:mt-3">
              <Select
                value={role}
                className="h-[44px] w-[150px]"
                size="large"
                onChange={(value) => setRole(value)}
                options={[
                  { value: '', label: text.allRoles }, // all roles
                  ...Object.values(ADMIN_ROLE).map((item) => ({
                    value: item,
                    label: ADMIN_ROLE_MAP.get(item),
                  })),
                ]}
              />
              <Select
                value={status}
                size="large"
                className="h-[44px]"
                onChange={(value) => setStatus(value)}
                options={[
                  { value: '', label: text.allStatuses }, // all status
                  ...Object.values(ACCOUNT_STATUS).map((item) => ({
                    value: item,
                    label: ACCOUNT_STATUS_MAP.get(item),
                  })),
                ]}
              />
            </div>
          </div>

          <div className="h-[40px] my-3 [@media(min-width:1150px)]:my-0 float-right">
            <UiButton
              className="h-full"
              title={
                <p className="space-x-2">
                  <PlusOutlined rev={undefined} />
                  <span>{text.createAdmin}</span>
                </p>
              }
              handleClick={() => setIsCreateModalOpen(true)}
            />
          </div>
        </div>
        <AdminTable
          className="custom-table"
          columns={columns}
          dataSource={listAdmins?.items || []}
          rowKey="_id"
          pagination={{
            pageSize: adminQueryParams.pageSize,
            current: adminQueryParams.pageIndex,
            total: listAdmins?.totalItems || 0,
            position: ['bottomCenter'],
          }}
          onChange={handleTableChange}
          scroll={{ x: 'max' }}
        />
      </div>
      <CreateAdminAccountModal
        open={isCreateModalOpen || !!editAdmin}
        onCancel={() => {
          setIsCreateModalOpen(false)
          setEditAdmin(null)
        }}
        onSuccess={() => {
          setIsCreateModalOpen(false)
          setEditAdmin(null)
          refetch()
          queryClient.invalidateQueries({ queryKey: ['getAdminAccountStats'] })
        }}
        admin={editAdmin} // pass admin prop for edit
        isEdit={!!editAdmin}
      />
    </>
  )
}
