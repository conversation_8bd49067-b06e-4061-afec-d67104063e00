//eslint-disable-next-line
export const specialCharacter = /^[A-Za-zＡ-ｚ0-9０-９一-龯ぁ-んァ-ン 　]+$/gm

//eslint-disable-next-line
export const schemaEmail = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g

export const schemaLink =
  //eslint-disable-next-line
  /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/

export const schemaDatePicker = /^\d{2}\/\d{2}\/\d{4}$/g

export const schemaNumber = /^[1-9１-９][0-9０-９]*$/

export const schemaNumberFloat = /^-?\d+\.?\d*$/

export const schemaLinkCampaiOff =
  /^((ftp|http|https):\/\/)?(www.)?(?!.*(ftp|http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+((\/)[\w#+]+)*(\/\w+\?[a-zA-Z0-9+_]+=\w+(&[a-zA-Z0-9+_]+=\w+)*)?$/gm

export const schemaDiscordInviteLink =
  //eslint-disable-next-line
  /(https:\/\/)?(www\.)?(((discord(app)?)?\.com\/invite)|((discord(app)?)?\.gg))\/(?<invite>.+)/gm

export const schemaTelegramInviteLink =
  //eslint-disable-next-line
  /(https?:\/\/)?(www[.])?(telegram|t)\.me\/([a-zA-Z0-9_+-]*)\/?$/gm

//eslint-disable-next-line
export const schemaContractAddress = /^0x[a-fA-F0-9]{40}$/g

export const regexEmoji =
  /[\u{1f300}-\u{1f5ff}\u{1f900}-\u{1f9ff}\u{1f600}-\u{1f64f}\u{1f680}-\u{1f6ff}\u{2600}-\u{26ff}\u{2700}-\u{27bf}\u{1f1e6}-\u{1f1ff}\u{1f191}-\u{1f251}\u{1f004}\u{1f0cf}\u{1f170}-\u{1f171}\u{1f17e}-\u{1f17f}\u{1f18e}\u{3030}\u{2b50}\u{2b55}\u{2934}-\u{2935}\u{2b05}-\u{2b07}\u{2b1b}-\u{2b1c}\u{3297}\u{3299}\u{303d}\u{00a9}\u{00ae}\u{2122}\u{23f3}\u{24c2}\u{23e9}-\u{23ef}\u{25b6}\u{23f8}-\u{23fa}]/gu

export const regexAlphabet = /^[a-zA-Z]+$/

export const regexAlphabetNumber =
  // eslint-disable-next-line no-useless-escape
  /^[a-zA-Z0-9!@#\$%^&*()_+{}\[\]:;<>,.?~\/\\=|\-]+$/

export const regexFullWidth = /[Ａ-Ｚａ-ｚ０-９]/g

export const regexBanJapanese =
  // eslint-disable-next-line no-useless-escape
  /^[a-zA-Z0-9０-９!@#$%^&*()_+{}\[\]:;<>,.?/~\-\\]+$/u

export const regexEmailFullWidth =
  /^[^\uff10-\uff19\uff21-\uff3a\uff41-\uff5a\uff01-\uff0f\uff1a-\uff20\uff3b-\uff40\uff5b-\uff5f\uffe5\uff3d\uff04\uff05\uff07\uff0a\uff0b\uff0c\uff0e\uff0f\uff1a\uff1b\uff1c\uff1d\uff1e\uff1f\uff20\uff3b\uff3c\uff3d\uff3e\uff3f\uff5b-\uff60\uff61-\uff64\uffe0\uffe1\uffe2\uffe3\uffe4\uffe5\uff0c\uff0e\uff1a\uff1b\uff1c\uff1d\uff1e\uff1f\uff01\uff02\uff03\uff04\uff05\uff07\uff0a\uff0b\uff0c\uff0e\uff0f]+$/
