const LANGUAGE_STORAGE_KEY = 'preferred-language'

export const getCurrentLanguage = (): 'en' | 'ja' => {
  if (typeof window === 'undefined') {
    return 'en' // Default for SSR
  }

  const storedLang = localStorage.getItem(LANGUAGE_STORAGE_KEY)
  return (storedLang || 'en') as 'en' | 'ja'
}

export const setLanguage = (lang: string): void => {
  if (typeof window === 'undefined') {
    return
  }

  localStorage.setItem(LANGUAGE_STORAGE_KEY, lang)
}

export const initializeLanguage = (): string => {
  if (typeof window === 'undefined') {
    return 'en'
  }

  const storedLang = localStorage.getItem(LANGUAGE_STORAGE_KEY)
  if (!storedLang) {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, 'en')
    return 'en'
  }
  return storedLang
}
