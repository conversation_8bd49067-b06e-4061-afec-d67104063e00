import { Form, FormItemProps, Row, Tooltip } from 'antd'
import React from 'react'
import { InfoCircleOutlined } from '@ant-design/icons'

interface IFormItem extends FormItemProps {
  textToolTip?: string
  iconToolTip?: JSX.Element
  childrenForm?: JSX.Element
  children?: JSX.Element
  tooltip?: boolean
}
export const FormItem = (props: IFormItem) => {
  const { tooltip = true } = props
  return (
    <div id={props.id}>
      <Row className="mb-2 gap-2">
        <span className="text-tailwindNeutral1 text-base font-sans font-bold">
          {props.label}
        </span>
        {tooltip && (
          <Tooltip title={props.textToolTip || ''}>
            {props.iconToolTip || (
              <InfoCircleOutlined rev={'icon'} style={{ fontSize: 16 }} />
            )}
          </Tooltip>
        )}
      </Row>
      {!props.children && (
        <Form.Item name={props.name} rules={props.rules}>
          {props.childrenForm}
        </Form.Item>
      )}
      {props.children}
    </div>
  )
}
