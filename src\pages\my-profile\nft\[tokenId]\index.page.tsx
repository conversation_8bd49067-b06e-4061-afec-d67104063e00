import React, { useEffect } from 'react'
import NftDetail from '@/components/nftComponents/NftDetail'
import { useRouter } from 'next/router'
import { getNftMetadata } from '@/services/apiCall/nft-minted'
import { useGet } from '@/hooks/useGet'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'

const UserNftDetail = () => {
  const { query, replace } = useRouter()
  const [cookies] = useCookies([STORAGEKEY.USER_ACCESS_TOKEN])
  const token = cookies[STORAGEKEY.USER_ACCESS_TOKEN]
  const tokenId = query.tokenId as string
  const { data, isLoading, isError } = useGet({
    queryKey: ['nft-metadata', tokenId],
    callback: () => getNftMetadata(tokenId),
    enabled: !!tokenId && !!token,
  })

  useEffect(() => {
    if (isError) {
      replace('/404')
    }
  }, [isError, replace])

  if (isLoading) return <div>Loading...</div>
  if (!data) return null

  return <NftDetail {...data} />
}

export default UserNftDetail
