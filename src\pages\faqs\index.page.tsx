import TextTitle from '@/components/texts/TextTitle'
import React from 'react'
import { FloatButton } from 'antd'
import Head from 'next/head'

import { pageHeadConfigDefault } from '@/constants'

function Faqs() {
  const pageHeadConfigAbout = {
    title: 'title',
    description: 'description',
  }

  return (
    <div className="xl:mx-[15vw] p-4 pb-12 xl:px-12 flex flex-col bg-white">
      <Head>
        <title>{pageHeadConfigAbout.title}</title>
        <meta name="description" content={pageHeadConfigAbout.description} />
        <meta property="og:title" content={pageHeadConfigAbout.title} />
        <meta
          property="og:description"
          content={pageHeadConfigAbout.description}
        />
        <meta name="keywords" content={pageHeadConfigDefault.keywords} />
      </Head>
      <div className="flex justify-center items-center h-[160px] bg-tailwindBrand1">
        <TextTitle className="max-sm:text-2xl" title="FAQs" />
      </div>
      Faqs
      <FloatButton.BackTop />
    </div>
  )
}

export default Faqs
