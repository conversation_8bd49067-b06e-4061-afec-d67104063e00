import PolygonIcon from 'public/logos/polygon.png'
import { configWagmi, environmentMode } from '@/helpers/wagmiClient'

export const useNetworkData = () => {
  const chainsData = [
    {
      id: configWagmi.chains[environmentMode].id,
      name: configWagmi.chainsName[environmentMode],
      icon: PolygonIcon,
      symbol: 'POLYGON',
      blockExplorers: configWagmi.blockExplorers,
    },
  ]

  return chainsData
}
