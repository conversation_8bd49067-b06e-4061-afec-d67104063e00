import { useEffect, useState } from 'react'
import { useTimeSelectBar } from '@/components/time-select-bar'
import { StatisticsCard } from '@/components/card/StatisticsCard'

import { get } from '@/services/apiCall/baseApi'
import { ROLE } from '@/constants/auth'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import { getCookie } from '@/utils/handleCookie'
import UiButton from '@/components/ui/Button'
import Spinner from '@/components/ui/Spinner'
import { formatPrice } from '@/utils'
import { BUTTON_SIZES } from '@/constants'
import { IconDownload } from '@/components/icons'
import { LineChart } from './LineChart'

// Types for dashboard API response
type StatisticsItem = {
  title: string
  amount: number
  currency?: boolean
}

type NewlyRegisteredUsersStatistics = StatisticsItem[]
type TotalNftSoldStatistics = StatisticsItem[]
type TotalRevenueStatistics = StatisticsItem[]

type DashboardStats = {
  totalNftsSold: number
  totalRevenue: number
  avgSalePrice: number
  totalRegisteredUsers: number
  newlyRegisteredUsersStatistics: NewlyRegisteredUsersStatistics
  totalNftSoldStatistics: TotalNftSoldStatistics
  totalRevenueStatistics: TotalRevenueStatistics
  buyerRateStatistics: StatisticsItem[]
}

export const TEXT_DASHBOARD = {
  CSV_EXPORT: 'CSV出力',
  DAY_1: '1日',
  DAYS_7: '7日',
  MONTH_1: '1ヶ月',
  MONTHS_3: '3ヶ月',
  MONTHS_6: '6ヶ月',
  TOTAL_NFTS_SOLD: '総販売NFT数',
  TOTAL_REVENUE: '総売上',
  AVG_SALE_PRICE: '平均販売価格',
  ACTIVE_USERS: 'アクティブユーザー数',
  ITEMS_SOLD: '販売されたアイテム数',
  NUMBER_OF_NFTS_SOLD_OVER_TIME: 'NFT販売数の推移',
  TOTAL_REVENUE_ALT: '総収益',
  REVENUE_FROM_NFT_SALES: 'NFT販売による収益',
  CONVERSION_RATE_CVR: 'コンバージョン率（CVR）',
  USER_CONVERSION_RATE_TO_BUYERS: 'ユーザーの購入者への転換率',
  NUMBER_OF_USERS: 'ユーザー数',
  TOTAL_USERS_ON_MARKETPLACE:
    'マーケットプレイスにウォレットを接続したユーザー',
  NFT_MARKET_OVERVIEW: 'NFTマーケットプレイスオーバービュー',
  DASHBOARD_DESCRIPTION:
    '重要なパフォーマンス指標を確認するためのダッシュボードです。',
} as const

const fetchDashboardStats = async (
  timeOption: string
): Promise<DashboardStats> => {
  return await get('v1/admin/dashboard', { timeOption }, ROLE.systemAdmin)
}

const exportCsv = async (timeOption: string) => {
  const token = getCookie('admin_access_token') || ''
  const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/v1/admin/dashboard/export-csv?timeOption=${timeOption}`

  const res = await fetch(url, {
    headers: {
      Authorization: token,
      'x-signature': process.env.NEXT_PUBLIC_GLOBAL_API_SIGNATURE || '',
    },
  })

  const csvText = await res.text()

  const bom = '\uFEFF'
  const blob = new Blob([bom + csvText], { type: 'text/csv;charset=utf-8;' })

  const blobUrl = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = blobUrl
  link.setAttribute('download', 'dashboard-statistics.csv')
  document.body.appendChild(link)
  link.click()
  link.remove()
}

export default function AdminDashboardPage() {
  const { timeOption, TimeSelectBar } = useTimeSelectBar()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    setLoading(true)
    fetchDashboardStats(timeOption)
      .then(setStats)
      .finally(() => setLoading(false))
  }, [timeOption])

  return (
    <div className="min-h-screen">
      <TextAdminHeader title={TEXT_DASHBOARD.NFT_MARKET_OVERVIEW} />

      <div className="mb-7 flex justify-between items-center">
        <span className="text-sm font-montserrat text-white">
          {TEXT_DASHBOARD.DASHBOARD_DESCRIPTION}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 [@media(min-width:1000px)]:grid-cols-2 [@media(min-width:1175px)]:grid-cols-4 gap-4 mb-12">
        <StatisticsCard
          className="bg-getCardBg"
          title={TEXT_DASHBOARD.TOTAL_NFTS_SOLD}
          stat={stats?.totalNftsSold ?? '--'}
        />
        <StatisticsCard
          className="bg-getCardBg"
          title={TEXT_DASHBOARD.TOTAL_REVENUE}
          stat={
            stats ? (
              <>
                {formatPrice(stats?.totalRevenue, 4)}{' '}
                <span className="text-xl ml-2">GET PAY</span>
              </>
            ) : (
              '--'
            )
          }
        />
        <StatisticsCard
          className="bg-getCardBg"
          title={TEXT_DASHBOARD.AVG_SALE_PRICE}
          stat={
            stats ? (
              <>
                {formatPrice(stats.avgSalePrice, 4)}{' '}
                <span className="text-xl ml-2">GET PAY</span>
              </>
            ) : (
              '--'
            )
          }
        />
        <StatisticsCard
          className="bg-getCardBg"
          title={TEXT_DASHBOARD.NUMBER_OF_USERS}
          stat={stats?.totalRegisteredUsers ?? '--'}
        />
      </div>

      <div className="mb-6 flex">
        <TimeSelectBar className="max-w-md mr-auto" />
        <UiButton
          title={<span>{TEXT_DASHBOARD.CSV_EXPORT}</span>}
          isGradient={false}
          size={BUTTON_SIZES.FIT}
          handleClick={() => exportCsv(timeOption)}
          className="!text-primary !text-[14px] !font-normal"
          icon={<IconDownload />}
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-screen">
          <Spinner size="h-12 w-12" />
        </div>
      ) : (
        <div
          className="dashboard grid grid-cols-1 [@media(min-width:1136px)]:grid-cols-2 gap-4 mb-5"
          style={{ background: 'none', padding: 0 }}
        >
          <div className="card bg-getCardBg rounded-[8px] p-4 text-white">
            <div className="mb-2">
              <div className="font-semibold text-sm">
                {TEXT_DASHBOARD.ITEMS_SOLD}
              </div>
              <div className="text-xs">
                {TEXT_DASHBOARD.NUMBER_OF_NFTS_SOLD_OVER_TIME}
              </div>
            </div>
            <LineChart
              data={
                stats?.totalNftSoldStatistics?.map((d) => ({
                  date: d.title,
                  value: d.amount,
                })) ?? []
              }
            />
          </div>

          <div className="card bg-getCardBg rounded-[8px] p-4 text-white">
            <div className="mb-2">
              <div className="font-semibold text-sm">
                {TEXT_DASHBOARD.TOTAL_REVENUE}
              </div>
              <div className="text-xs">
                {TEXT_DASHBOARD.REVENUE_FROM_NFT_SALES}
              </div>
            </div>
            <LineChart
              data={
                stats?.totalRevenueStatistics?.map((d) => ({
                  date: d.title,
                  value: d.amount,
                })) ?? []
              }
              unit={'GP'}
            />
          </div>

          <div className="card bg-getCardBg rounded-[8px] p-4 text-white">
            <div className="mb-2">
              <div className="font-semibold text-sm">
                {TEXT_DASHBOARD.CONVERSION_RATE_CVR}
              </div>
              <div className="text-xs">
                {TEXT_DASHBOARD.USER_CONVERSION_RATE_TO_BUYERS}
              </div>
            </div>
            <LineChart
              data={
                stats?.buyerRateStatistics?.map((d) => ({
                  date: d.title,
                  value: d.amount,
                })) ?? []
              }
              yFormat={(v) => `${v}`}
              unit="%"
            />
          </div>

          <div className="card bg-getCardBg rounded-[8px] p-4 text-white">
            <div className="mb-2">
              <div className="font-semibold text-sm">
                {TEXT_DASHBOARD.NUMBER_OF_USERS}
              </div>
              <div className="text-xs">
                {TEXT_DASHBOARD.TOTAL_USERS_ON_MARKETPLACE}
              </div>
            </div>
            <LineChart
              data={
                stats?.newlyRegisteredUsersStatistics?.map((d) => ({
                  date: d.title,
                  value: d.amount,
                })) ?? []
              }
            />
          </div>
        </div>
      )}
    </div>
  )
}
