import { Modal } from 'antd'
import ButtonComponent from '../buttons/Button'
import clsx from 'clsx'
import { IconWarning } from '@/icons'

interface ModalWarningProps {
  isModalOpen: boolean
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  content: string | JSX.Element
  okText?: string
  cancelText?: string
  icon?: JSX.Element
  containerClass?: string
  contentClass?: string
  buttonContainerClass?: string
  funcOk?: () => void | false
  onCancel?: () => void
}

function ModalConfirm(props: ModalWarningProps) {
  const {
    isModalOpen,
    setModalOpen,
    content,
    okText = 'はい',
    cancelText = 'いいえ',
    icon = <IconWarning />,
    containerClass,
    contentClass,
    buttonContainerClass,
    funcOk = false,
    ...rest
  } = props
  const handleCancel = () => {
    setModalOpen(false)
  }
  const handleOk = () => {
    if (funcOk) {
      funcOk()
    } else {
      setModalOpen(!isModalOpen)
    }
  }

  return (
    <div>
      <Modal
        closeIcon={null}
        open={isModalOpen}
        onCancel={props.onCancel || handleCancel}
        okText={okText}
        cancelText={cancelText}
        centered
        footer={null}
        {...rest}
      >
        <div
          className={clsx(
            'flex items-center justify-center flex-col gap-12 p-8',
            containerClass
          )}
        >
          <div className="">{icon}</div>
          <div
            className={clsx(
              'font-bold text-2xl px-6 text-center',
              contentClass
            )}
          >
            {content}
          </div>
          <div className={clsx('flex gap-4', buttonContainerClass)}>
            <ButtonComponent
              type="primary"
              title={okText}
              className="!text-black !h-12  w-[172px] !font-bold"
              onClick={handleOk}
            />
            <ButtonComponent
              type="default"
              title={cancelText}
              className="text-black !h-12 border-tailwindNeutral3 w-[172px] !font-bold"
              onClick={handleCancel}
            />
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default ModalConfirm
