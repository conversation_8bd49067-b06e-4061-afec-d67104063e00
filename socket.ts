import { io, ManagerOptions, SocketOptions } from 'socket.io-client'
import { getC<PERSON><PERSON>, STORAGEKEY } from '@/services/cookies'

interface ISocket {
  socketToken?: string
}
const socketUser = (props?: ISocket) => {
  const token = props?.socketToken || getCookie(STORAGEKEY.USER_ACCESS_TOKEN)
  if (token) {
    const options: Partial<ManagerOptions & SocketOptions> = {
      withCredentials: true,
      auth: {
        token,
      },
      reconnection: false,
      transports: ['websocket'],
    }
    const socket = io(process.env.NEXT_PUBLIC_API_BASE_URL as string, options)
    return socket
  }
}

export default socketUser
