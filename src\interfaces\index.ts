export * from './common'
export * from './user'
export * from './auth'
export * from './nftComponents'
export * from './connectItems'
export * from './banner'
export * from './news'
export * from './socket'
export * from './activity'
export * from './admin'
export * from './transaction'
export * from './collection'
export * from './categories'
export * from './nft'
export * from './nft-minted'
export * from './collection'
export * from './educational'

export type Paginated<T> = {
  totalItems: number
  pageIndex: number
  pageSize: number
  items: T[]
}

export type PaginationOption = {
  pageIndex: number
  pageSize: number
}
