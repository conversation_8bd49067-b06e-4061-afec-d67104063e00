import React, { useC<PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { useRouter } from 'next/router'
import { HiOutlineUser } from 'react-icons/hi'
import { useClickOutside, useTranslate } from '@/hooks'
import { getCurrentLanguage, shortenString } from '@/utils'
import { IConnectItem } from '@/interfaces'
import ButtonComponent from '@/components/buttons/Button'
import { IconAccount, LogoutIcon } from '@/icons'
import {
  ROUTES,
  SUB_MENU_USER,
  EXTENSION_INSTALL_URL,
  ERROR_WALLET,
  CHROME_ERROR,
} from '@/constants'
import UiButton from '@/components/ui/Button'
import { useGetWallet } from '@/hooks/useGetWallet'
import { DownOutlined, LogoutOutlined } from '@ant-design/icons'
import { Avatar, Checkbox } from 'antd'
import UiModal from '@/components/ui/Modal'
import Link from 'next/link'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'
import useMobileConnect from '@/hooks/useMobileConnect'
import { getUserDetail } from '@/services/apiCall'
import { useQuery } from '@tanstack/react-query'

const ConnectItemOrigin: React.FC<IConnectItem> = ({
  isMobile,
  setShowMobileMenu,
}) => {
  const { connectWallet, disconnectWallet, extensionConnectionError } =
    useGetWallet()

  const [cookies] = useCookies([
    STORAGEKEY.WALLET_ADDRESS,
    STORAGEKEY.USER_ACCESS_TOKEN,
    STORAGEKEY.USER,
  ])
  const { push } = useRouter()
  const trans = useTranslate()
  const accountRef = useRef(null)
  const [isShowMenu, setShowMenu] = useState(false)
  const [isAgree, setIsAgree] = useState(true)
  const [showExtensionErrorModal, setShowExtensionErrorModal] = useState(false)
  const [showModalConnectWallet, setShowModalConnectWallet] = useState(false)
  const isConnected = useMemo<boolean>(() => {
    return !!cookies[STORAGEKEY.USER_ACCESS_TOKEN]
  }, [cookies])

  const walletAddress = cookies[STORAGEKEY.WALLET_ADDRESS]
  const { otherWallet: evmWalletAddress } = cookies[STORAGEKEY.USER] || {}

  const [, setAvatar] = useState(
    () => localStorage.getItem('user_avatar') || ''
  )

  useEffect(() => {
    const onStorageChange = (e: StorageEvent) => {
      if (e.key === 'avatar') {
        setAvatar(e.newValue || '')
      }
    }

    window.addEventListener('storage', onStorageChange)

    return () => {
      window.removeEventListener('storage', onStorageChange)
    }
  }, [])

  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', walletAddress],
    queryFn: async () => {
      const result = await getUserDetail()
      localStorage.setItem('avatar', JSON.stringify(result?.avatar))
      return result
    },
    enabled: !!walletAddress,
  })
  // Hooks
  const handleClickOutside = useCallback(() => setShowMenu(false), [])
  useClickOutside(accountRef, handleClickOutside)

  // Handle extension connection error

  // Close extension error modal
  const closeExtensionErrorModal = () => {
    setShowExtensionErrorModal(false)
  }

  // Handle install extension link click
  const handleInstallExtension = () => {
    window.open(EXTENSION_INSTALL_URL, '_blank')
  }

  const { connectWalletMobile, handleLogout } = useMobileConnect({
    isMobile: !!isMobile,
  })
  const handleClickSubMenu = (type: string) => {
    setShowMenu(false)
    setShowMobileMenu && setShowMobileMenu(false)
    switch (type) {
      case SUB_MENU_USER.myProfile:
        push(ROUTES.myProfile)
        break
      case SUB_MENU_USER.disconnect:
        isMobile ? handleLogout(false) : disconnectWallet()
        if (window.location.pathname.includes('my-profile')) {
          push('/')
        }
        break
      default:
        push(ROUTES.home)
    }
  }

  const handleConnectWallet = () => {
    if (isMobile) {
      connectWalletMobile()
      return
    }
    setIsAgree(true)
    setShowModalConnectWallet(true)
  }

  // Handle connect wallet modal actions
  const handleCloseConnectWalletModal = () => {
    setShowModalConnectWallet(false)
  }
  const handleConfirmConnectWallet = () => {
    if (!isAgree) return
    connectWallet()
    setShowModalConnectWallet(false)
  }

  useEffect(() => {
    if (
      extensionConnectionError &&
      (extensionConnectionError.message.includes(ERROR_WALLET) ||
        extensionConnectionError.message.includes(CHROME_ERROR))
    ) {
      setShowExtensionErrorModal(true)
    }
  }, [extensionConnectionError])

  const isSigningMessage = false

  const RenderDropDown = React.memo(() => (
    <div
      className={`sub-menu bg-white !w-[200px] ${
        isShowMenu ? 'block' : 'hidden'
      }`}
    >
      <ButtonComponent
        title={trans.profile}
        type="default"
        beforeIcon={<HiOutlineUser className="h-5 w-5" />}
        className="sub-menu__item border-none flex gap-3 items-center"
        onClick={() => handleClickSubMenu(SUB_MENU_USER.myProfile)}
      />
      <ButtonComponent
        type="default"
        onClick={() => handleClickSubMenu(SUB_MENU_USER.disconnect)}
        className="sub-menu__item border-none flex gap-3 items-center"
        beforeIcon={<LogoutIcon />}
        title={trans.wallet_disconnect}
      />
    </div>
  ))
  const RenderLabel = React.memo(() => {
    return (
      <>
        {isSigningMessage
          ? trans.loading
          : shortenString(evmWalletAddress || '')}
      </>
    )
  })
  const RenderConnected = React.memo(() => (
    <>
      {isMobile ? (
        <div className="flex flex-col items-center gap-6">
          <p className="text-white cursor-pointer">
            <RenderLabel />
          </p>
          {!isSigningMessage && (
            <>
              <UiButton
                title={
                  <div className="flex items-center gap-2">
                    {userProfile?.avatar ? (
                      <Avatar size={32} src={userProfile.avatar} />
                    ) : (
                      <IconAccount width={32} height={32} />
                    )}
                    <p>{trans.profile}</p>
                  </div>
                }
                isGradient={false}
                handleClick={() => handleClickSubMenu(SUB_MENU_USER.myProfile)}
              />
              <UiButton
                title={
                  <div className="flex items-center gap-2">
                    <LogoutOutlined
                      rev={undefined}
                      style={{ fontSize: '24px' }}
                    />
                    <p>{trans.wallet_disconnect}</p>
                  </div>
                }
                isGradient={false}
                handleClick={() => handleClickSubMenu(SUB_MENU_USER.disconnect)}
              />
            </>
          )}
        </div>
      ) : (
        <div className="flex items-center gap-5">
          {!isSigningMessage ? (
            <UiButton
              title={
                <div className="flex items-center gap-2">
                  {userProfile?.avatar ? (
                    <Avatar size={32} src={userProfile.avatar} />
                  ) : (
                    <IconAccount width={32} height={32} />
                  )}
                  <p>
                    <RenderLabel />
                  </p>
                  <DownOutlined rev={undefined} />
                </div>
              }
              isGradient={false}
              handleClick={() => setShowMenu(!isShowMenu)}
            />
          ) : (
            <ButtonComponent title={trans.loading} />
          )}
          <RenderDropDown />
        </div>
      )}
    </>
  ))

  return (
    <div className="flex items-center flex-col xl:flex-row">
      {/* -------- Connect wallet -------- */}
      <div className={`relative ml-0 xl:ml-4`} ref={accountRef}>
        {/* connect button */}
        {isConnected ? (
          <RenderConnected />
        ) : (
          <UiButton
            className="w-[180px] font-semibold text-sm leading-[140%] text-center tracking-[0.02em]"
            title={trans.connect_wallet}
            handleClick={handleConnectWallet}
          />
        )}
      </div>

      {/* Connect Wallet Modal */}
      <UiModal
        isOpen={showModalConnectWallet}
        onClose={handleCloseConnectWalletModal}
        header={trans.connect_wallet}
        cancelButton={{
          isShow: true,
          title: trans.close,
          action: handleCloseConnectWalletModal,
        }}
        confirmButton={{
          isShow: true,
          title: trans.connect_wallet,
          action: handleConfirmConnectWallet,
        }}
      >
        <div className="modal rounded-lg p-0 text-white w-full max-w-full font-montserrat">
          <p
            className={`text-center text-md mt-2 mb-4 font-montserrat ${
              getCurrentLanguage() === 'en'
                ? 'text-wrap'
                : 'text-wrap md:text-nowrap'
            }`}
          >
            {trans.connect_wallet_note}
          </p>
          <div className="checkbox-container flex items-center justify-center gap-2 mb-4 font-montserrat">
            <Checkbox
              id="agree-terms"
              checked={isAgree}
              onChange={(e) => setIsAgree(e.target.checked)}
              className="text-sm text-white select-none font-montserrat"
            >
              <span className="font-montserrat">
                {getCurrentLanguage() === 'en' ? (
                  <>
                    I agree to the{' '}
                    <Link
                      href="/terms-of-use"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline font-montserrat"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Terms of Use
                    </Link>{' '}
                    and{' '}
                    <Link
                      href="/privacy-policy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline font-montserrat"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Privacy Policy
                    </Link>
                    .
                  </>
                ) : (
                  <>
                    <Link
                      href="/terms-of-use"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline font-montserrat"
                      onClick={(e) => e.stopPropagation()}
                    >
                      利用規約
                    </Link>
                    と
                    <Link
                      href="/privacy-policy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline font-montserrat"
                      onClick={(e) => e.stopPropagation()}
                    >
                      プライバシーポリシー
                    </Link>
                    に同意します
                  </>
                )}
              </span>
            </Checkbox>
          </div>
          {/* Overlay a disabled style on the Connect button if not agreed */}
          {!isAgree && (
            <div className="absolute left-0 top-0 w-full h-full bg-black bg-opacity-30 flex items-end justify-end pointer-events-none">
              {/* visually disables modal, but buttons are still present for accessibility */}
            </div>
          )}
        </div>
      </UiModal>

      {/* Extension Error Modal */}
      <UiModal
        isOpen={showExtensionErrorModal}
        onClose={closeExtensionErrorModal}
        header={trans.extension_required}
        cancelButton={{
          isShow: true,
          title: trans.close,
          action: closeExtensionErrorModal,
        }}
        confirmButton={{
          isShow: true,
          title: trans.install_extension,
          action: handleInstallExtension,
        }}
      >
        <div className="text-center space-y-4">
          <div className="mb-4">
            <p className="text-base sm:text-lg leading-relaxed">
              {trans.extension_install_message}
            </p>
          </div>
          <div>
            <p className="text-xs sm:text-sm text-gray-300 leading-relaxed">
              {trans.extension_install_submessage}
            </p>
          </div>
        </div>
      </UiModal>
    </div>
  )
}

export const ConnectItem = React.memo(ConnectItemOrigin)
