import React from 'react'
import { Tabs, TabsProps, Row, Col, Empty, Tooltip, ColProps } from 'antd'
import {
  HiOutlineAdjustments,
  HiOutlineInformationCircle,
} from 'react-icons/hi'

import Text from '../texts/Text'
import TagComponent from '../tag'

import { MESSAGE_FORM } from '@/utils/string'
import { TYPE_NFT } from '@/constants/type'

interface NftInformationProps {
  urlGoogleSheet?: string
  isOwnUser?: boolean
  nameNft: string
  typeNft: TYPE_NFT
  owner?: string
  token?: number | string
  productId?: string
  mintedAt?: string
  soldAt?: string
  description?: string
  amountRelease?: number
  mintedNumber: number
  dataAttribute?: {
    traitType: string
    value: string
  }[]
}

interface INftItemProps extends ColProps {
  title?: string
  value?: string | number
  classNameValue?: string
  toolTip?: boolean
}

function LabelComponent({ icon, text }: { icon: JSX.Element; text: string }) {
  return (
    <div className="flex flex-row items-center gap-2 justify-center text-base 2xl:w-[150px] w-[120px] h-[35px]">
      {icon}
      {text}
    </div>
  )
}

const NftItem = (props: INftItemProps) => {
  const { title, value, classNameValue, toolTip = true, ...rest } = props
  return (
    <Col xs={24} xxl={12} xl={12} lg={24} {...rest}>
      <div className="text-tailwindNeutral3 block 2xl:text-sm text-xs ">
        {title}
      </div>
      {toolTip ? (
        <Tooltip title={value} color={'#f2b516'}>
          <div
            className={`font-bold 2xl:text-base text-sm whitespace-pre-line  ${classNameValue}`}
          >
            {value}
          </div>
        </Tooltip>
      ) : (
        <div
          className={`font-bold 2xl:text-base text-sm whitespace-pre-line  ${classNameValue}`}
        >
          {value}
        </div>
      )}
    </Col>
  )
}
function NftInformation(props: NftInformationProps) {
  return (
    <div className="bg-white 2xl:h-[300px] xl:h-[300px] p-5 rounded-md">
      <Row align={'middle'} className="mb-5">
        <Tooltip title={props.nameNft} color="#f2b516">
          <h2 className="text-xl text-tailwindNeutral1 whitespace-nowrap font-medium 2xl:max-w-[300px] xl:max-w-[200px] text-ellipsis overflow-hidden">
            {props.nameNft}
          </h2>
        </Tooltip>
        {!props.isOwnUser && (
          <span className="text-xl text-tailwindNeutral1 font-medium">
            {`#${props.mintedNumber + 1}`}
          </span>
        )}
        <TagComponent title={'title'} className="ml-7" />
      </Row>
      <NftItem
        className="mb-3"
        classNameValue="2xl:max-h-[70px] lg:max-h-[60px] overflow-auto"
        xxl={24}
        xl={24}
        title="デスクリプション"
        value={props.description || '---'}
        toolTip={false}
      />
      <Row gutter={[16, 16]} className="mb-3 ">
        <NftItem
          title="発行者名"
          value={props.owner || ''}
          classNameValue="text-ellipsis overflow-hidden line-clamp-1"
        />
        <NftItem title="商品ID" value={`${props.productId}`} />
      </Row>
      <Row gutter={[16, 16]} className="mb-3">
        {props.amountRelease && (
          <NftItem title="発行可能個数" value={props.amountRelease || 0} />
        )}
        <NftItem
          xxl={12}
          title={props.isOwnUser ? '受取日時 (UTC)' : 'リリース日 (UTC)'}
          value={
            props.isOwnUser ? props.mintedAt || '---' : props.soldAt || '---'
          }
          classNameValue="text-ellipsis overflow-hidden line-clamp-1"
        />
      </Row>
    </div>
  )
}

const NftAttribute = ({
  data,
}: {
  data: {
    traitType: string
    value: string
  }[]
}) => {
  return (
    <div className="bg-white h-[300px]  px-4 py-5 rounded-r-md rounded-b-md overflow-x-hidden">
      {data?.length > 0 ? (
        <Row gutter={[16, 16]}>
          {data?.map((item, index) => (
            <Col xs={12} sm={12} xl={8} xxl={8} key={index}>
              <div className="border shadow-md flex flex-col w-full h-[120px] rounded-md cursor-pointer text-center px-4 py-4 overflow-y-auto">
                <Text className="text-tailwindNeutral3">{item.traitType}</Text>
                <Text className="font-bold text-[16px] block">-</Text>
                <Text className="font-bold text-[16px]">{item.value}</Text>
              </div>
            </Col>
          ))}
        </Row>
      ) : (
        <Row justify={'center'}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={MESSAGE_FORM.noData}
          />
        </Row>
      )}
    </div>
  )
}

function NftInformationDetail(props: NftInformationProps) {
  let items: TabsProps['items'] = []

  items = [
    {
      key: '1',
      label: (
        <LabelComponent
          icon={<HiOutlineInformationCircle className="h-6 w-6" />}
          text="情報"
        />
      ),
      children: <NftInformation {...props} />,
    },
    {
      key: '2',
      label: (
        <LabelComponent
          icon={<HiOutlineAdjustments className="h-6 w-6 rotate-90" />}
          text="属性"
        />
      ),

      children: <NftAttribute data={props.dataAttribute || []} />,
    },
  ]

  return (
    <Tabs
      className=""
      type="card"
      defaultActiveKey="1"
      items={items}
      tabBarGutter={7}
      tabBarStyle={{ margin: 0 }}
      moreIcon={null}
    />
  )
}

export default NftInformationDetail
