# Layout Page Implementation Plan Template

## 1. Overview

Briefly describe the purpose and scope of the layout page to be implemented. State which part of the application it will affect and its intended user experience.

---

## 2. Example

Provide a code snippet or UI wireframe/mockup that illustrates the intended layout. This can be a Figma link, image, or a JSX/TSX code example.

```tsx
// Example layout usage
import AdminLayout from '@/components/layouts/adminLayout'

export default function AdminDashboardPage() {
  return <AdminLayout>{/* Page content goes here */}</AdminLayout>
}
```

---

## 3. Flow

Describe the user and data flow for the layout page. Include:

- How the layout is composed (which components are used)
- Navigation and routing
- State management (Zustand slices, hooks)
- Data fetching (React Query, API calls)
- Any conditional rendering or dynamic imports

---

## 4. Version Note

Document the version of Next.js, TypeScript, and any major dependencies relevant to this implementation. In this section, also clearly state what is being done in this plan (e.g., creating a new layout, refactoring an existing one, updating dependencies, etc.). Note any breaking changes or migration considerations.

- **Plan Action:** <Describe the main action or change being implemented in this plan>

---

## 5. API Spec

List the API endpoints required for this layout page, including:

- Endpoint URL
- HTTP method
- Request/response schema (TypeScript interfaces)
- Example payloads

```ts
// Example
GET /api/user/info
Response: {
  id: string;
  name: string;
  ...
}
```

---

## 6. Summary List of Files

### Files to Create

- [ ] `src/components/layouts/<NewLayout>.tsx` – New layout component
- [ ] `src/pages/<route>/index.page.tsx` – New page using the layout
- [ ] ...

### Files to Modify

- [ ] `src/constants/routes.ts` – Add new route
- [ ] `src/store/slices/<slice>.ts` – Update state if needed
- [ ] ...

---

## Implementation Steps

Provide a step-by-step checklist for implementing this plan. Each step should be actionable and clear for developers to follow.

1. Create the new layout component in `src/components/[page]/`.
2. Create or update the page in `src/pages/<route>/` to use the new layout.
3. Add or update route definitions in `src/constants/routes.ts`.
4. Update or create any necessary Zustand state slices in `src/store/slices/` (if needed)
5. Implement or update API calls in `src/services/apiCall/` if needed.
6. Add or update translations in `public/locales/ja/` and `public/locales/en/`.
7. Ensure accessibility (ARIA, keyboard navigation, alt text, etc.).

---

## 7. Other Notes

- Internationalization: Add translations to `public/locales/ja/` if needed
- Styling: Use Tailwind CSS and/or SCSS as per project convention
- Accessibility: Ensure keyboard navigation and ARIA attributes
- Testing: Add/modify tests if applicable
- Documentation: Update `architecture.md` or relevant docs

---

## 8. Checklist

- [ ] Plan reviewed and approved
- [ ] All new/modified files listed
- [ ] API contracts confirmed
- [ ] UI/UX reviewed
- [ ] Code follows project conventions

---

## Example Flow Generate New Page (Step by Step)

This section provides a concrete example of how to use this plan to generate a new page with a custom layout.

1. **Plan**: Fill out this template for your new layout/page feature and get approval.
2. **Component**: Create `src/components/[page]/ExampleLayout.tsx` with your layout logic.
3. **Page**: Create `src/pages/example/index.page.tsx` and content with `ExampleLayout`.
4. **Route**: Add `/example` to `src/constants/routes.ts` if not present.
5. **State**: If needed, add/update Zustand slice in `src/store/slices/`.
6. **API**: If needed, add API call in `src/services/apiCall/` and update API spec in the plan.
7. **i18n**: Add any new text to `public/locales/ja/common.json` and `public/locales/en/common.json`.
8. **Styling**: Use Tailwind CSS/SCSS for layout and visuals.
9. **Accessibility**: Ensure ARIA, alt text, and keyboard navigation.
10. **Testing**: Add/modify tests for the new layout/page.
11. **Docs**: Update `architecture.md` or other docs as needed.
12. **Review**: Check all items in the checklist and get final approval.
