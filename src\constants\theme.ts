import { ThemeConfig, theme } from 'antd'

export const customTheme: ThemeConfig = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: '#f2c157',
    fontFamily: 'Montserrat',
    colorText: '#fff',
    colorBgContainer: '#0A0A0A', // default background for inputs
    colorBorder: '#444444',
    borderRadius: 0,
  },
  components: {
    Input: {
      colorBgContainer: 'transparent',
      colorText: '#fff',
      colorTextPlaceholder: '#888',
      colorBorder: '#444444',
      controlHeightLG: 44,
    },
    Select: {
      colorIcon: '#fff', // arrow color
      colorIconHover: '#f2c157', // arrow hover color (optional)
      colorText: '#fff',
      colorBgContainer: '#0A0A0A',
      optionActiveBg: '#1a1a1a',
      optionSelectedBg: '#2a2a2a',
      controlHeightLG: 44,
    },
    InputNumber: {
      controlHeightLG: 44,
    },
    DatePicker: {
      controlHeightLG: 44,
    },
    Modal: {
      colorBgElevated: '#121212',
      colorText: '#fff',
    },
    Button: {
      colorPrimaryText: '#0A0A0A',
    },
  },
}
