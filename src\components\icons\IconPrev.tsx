import * as React from 'react'

type Props = {
  size?: number | string
  stroke?: number
  color?: string
  className?: string
}

function IconPrevBase({
  size = 16,
  stroke = 1.5,
  color = '#F2C157',
  className = '',
}: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 8 16"
      width={size}
      height={size}
      fill="none"
      className={className}
    >
      <path
        d="M6.999999 1L1.66938 7.21905C1.28419 7.66844 1.2842 8.33156 1.66938 8.78095L7 15"
        stroke={color}
        strokeWidth={stroke}
        strokeLinecap="round"
      />
    </svg>
  )
}

const IconPrev = React.memo(IconPrevBase)

export default IconPrev
