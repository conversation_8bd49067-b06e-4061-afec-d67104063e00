import { Upload, <PERSON><PERSON>, <PERSON> } from 'antd'
import React, { useState, useEffect } from 'react'
import { getBase64 } from '@/components/inputs/InputUploadImage'
import { isCheckCorrectType } from '@/utils/uploadImages'
import { MAX_SIZE_FILE } from '@/constants/common'
import { DeleteOutlined } from '@ant-design/icons'
import { isCheckSizeImage } from '@/utils/uploadImages'
import { PreviewNft } from '../nfts'

export enum ALLOWED_FILE_TYPE {
  JPG = 'JPG',
  PNG = 'PNG',
  SVG = 'SVG',
  GIF = 'GIF',
  MP4 = 'MP4',
}

interface UploadImageProps {
  width?: string
  height?: string
  label: string
  accept?: ALLOWED_FILE_TYPE[]
  recommendedSize?: string
  value: File | null
  imageUrl?: string
  onChange: (file: File | null) => void
  required?: boolean
  maxSize?: number
  disabled?: boolean
  setImageUrl?: (url: string) => void
  uploadVideo?: boolean
}

const TEXT_LABELS = {
  DRAG_AND_DROP: 'ドラッグアンドドロップ',
  CLICK_TO_UPLOAD: 'またはクリックしてアップロード',
  RECOMMEND_SIZE: '推奨サイズ',
  FIlE_TYPES: 'ファイルタイプ:JPG、PNG、SVG、GIF',
  FILE_TYPE: 'ファイルタイプ',
}

export const UploadImage: React.FC<UploadImageProps> = ({
  label,
  accept = [
    ALLOWED_FILE_TYPE.JPG,
    ALLOWED_FILE_TYPE.PNG,
    ALLOWED_FILE_TYPE.GIF,
    ALLOWED_FILE_TYPE.SVG,
  ],
  recommendedSize,
  value,
  onChange,
  required = false,
  width = 'w-full',
  height = 'h-full',
  imageUrl,
  maxSize = MAX_SIZE_FILE,
  disabled = false,
  setImageUrl,
  uploadVideo = false,
}) => {
  const [preview, setPreview] = useState<string>('')
  const [loading] = useState(false)
  const acceptString = accept.map((fileType) => `.${fileType}`).join(',')
  const isVideo = (uploadVideo && preview !== '') || /^data:video/.test(preview)

  useEffect(() => {
    if (value) {
      getBase64(value as any).then((url) => setPreview(url as string))
    } else if (imageUrl) {
      setPreview(imageUrl)
    } else {
      setPreview('')
      setImageUrl?.('')
    }
  }, [value, imageUrl, setImageUrl])

  const beforeUpload = (file: File) => {
    if (!isCheckCorrectType(accept, file.type)) return Upload.LIST_IGNORE
    if (!isCheckSizeImage(file.size, maxSize)) return Upload.LIST_IGNORE
    getBase64(file as any).then((url) => setPreview(url as string))
    onChange(file)
    return false // prevent auto upload
  }

  const handleRemove = () => {
    setPreview('')
    setImageUrl?.('')
    onChange(null)
  }

  const PreviewFile = () => {
    return (
      <PreviewNft
        src={preview}
        alt="preview"
        isVideo={isVideo}
        className="max-w-full max-h-[200px] object-contain rounded-md mx-auto"
        controls={isVideo}
      />
    )
  }

  return (
    <div className={`${width} ${height}`}>
      <div className="mb-2">
        <span className="font-semibold text-white text-xs">
          {label} {required && <span className="text-red-500">*</span>}
        </span>
      </div>
      {disabled && preview ? (
        <div className="bg-transparent border rounded-md min-h-[220px] flex items-center justify-center">
          <PreviewFile />
        </div>
      ) : (
        <Spin spinning={loading}>
          <Upload.Dragger
            name={label.toLowerCase().replace(/\s/g, '')}
            accept={acceptString}
            beforeUpload={beforeUpload}
            maxCount={1}
            showUploadList={false}
            onRemove={handleRemove}
            className={`bg-transparent border border-[#535353] rounded-md min-h-[220px] flex items-center justify-center 
          ${
            disabled ? 'opacity-50 pointer-events-none cursor-not-allowed' : ''
          }`}
          >
            {preview ? (
              <div
                className="flex items-center justify-center"
                onClick={(e) => {
                  e.stopPropagation()
                  e.preventDefault()
                }}
              >
                <div className="relative group">
                  <PreviewFile />
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<DeleteOutlined rev={undefined} />}
                    onClick={(e) => {
                      if (disabled) return
                      e.stopPropagation()
                      e.preventDefault()
                      handleRemove()
                    }}
                    className={`
                  absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity
                ${disabled ? 'pointer-events-none opacity-30' : ''}`}
                    style={{ zIndex: 10 }}
                  />
                </div>
              </div>
            ) : (
              <div>
                <p className="text-center text-white-400">
                  {TEXT_LABELS.DRAG_AND_DROP} <br />
                  <span className="text-yellow-500 cursor-pointer">
                    {TEXT_LABELS.CLICK_TO_UPLOAD}
                  </span>
                </p>
                {recommendedSize && (
                  <p className="mt-2 text-xs text-gray-500">{`${
                    TEXT_LABELS.RECOMMEND_SIZE
                  } : 
                ${recommendedSize}。 ${TEXT_LABELS.FILE_TYPE}: ${accept.join(
                    '、'
                  )}`}</p>
                )}
              </div>
            )}
          </Upload.Dragger>
        </Spin>
      )}
    </div>
  )
}

export default UploadImage
