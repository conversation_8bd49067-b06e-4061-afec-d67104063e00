import { Modal, Upload, UploadProps } from 'antd'
import { RcFile, UploadFile } from 'antd/es/upload'
import clsx from 'clsx'
import { useState } from 'react'
import { HiXCircle } from 'react-icons/hi'

import ButtonComponent from '../buttons/Button'
import Text from '../texts/Text'
import { IconUpload } from '@/icons'
import { post } from '@/services/apiCall/baseApi'
import { API_URLS, LENGTH_BIT_IMAGE, MAX_SIZE_FILE, ROLE } from '@/constants'
import { toast } from 'react-toastify'
import { MESSAGE_FORM } from '@/utils/string'

interface ModalWarningProps {
  isModalOpen: boolean
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  content: string | JSX.Element
  okText?: string
  cancelText?: string
  icon?: JSX.Element
  containerClass?: string
  contentClass?: string
  buttonContainerClass?: string
  funcOk?: () => void | false
  onSuccess: () => void
}

function ModalImportCsv(props: ModalWarningProps) {
  const {
    isModalOpen,
    setModalOpen,
    okText = 'インポート',
    cancelText = 'キャンセル',
    containerClass,
    contentClass,
    buttonContainerClass,
    funcOk = false,
    onSuccess,
    ...rest
  } = props
  const handleCancel = () => {
    setModalOpen(false)
    setCsvFile(null)
    setCsvFileError('')
  }
  const handleOk = () => {
    if (funcOk) {
      funcOk()
    } else {
      setModalOpen(!isModalOpen)
    }
  }

  const [csvFile, setCsvFile] = useState<UploadFile<unknown> | null>(null)
  const [csvFileError, setCsvFileError] = useState<string | null>(null)

  const handleRequest = (info) => {
    const { type } = info.file
    if (type !== 'text/csv') {
      return setCsvFileError('CSVファイルをアップロードしてください。')
    }
    setCsvFile(info?.file)
    setCsvFileError('')
  }

  const handleRemoveCsv = () => {
    setCsvFile(null)
    setCsvFileError('')
  }
  const config: UploadProps = {
    showUploadList: false,
    name: 'file',
    multiple: false,
    customRequest: handleRequest,
    beforeUpload(file) {
      if (file.size / LENGTH_BIT_IMAGE > MAX_SIZE_FILE) {
        setCsvFileError(MESSAGE_FORM.uploadOver10MbCSV)
        return false
      }
      return true
    },
  }
  const submit = async () => {
    if (!csvFile) {
      setCsvFileError(MESSAGE_FORM.chooseCSV)
      return
    }
    const form = new FormData()
    form.append('file', csvFile as RcFile)
    try {
      await post(
        API_URLS.adminImportCsv,
        form,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
        ROLE.systemAdmin
      )
      handleCancel()
      toast.success(MESSAGE_FORM.importSuccessCSV)
      onSuccess()
    } catch (err) {
      setCsvFileError(
        Array.isArray(err?.response?.data?.message)
          ? err?.response?.data?.message?.join('\n')
          : err?.response?.data?.message
      )
    }
  }
  return (
    <div>
      <Modal
        closeIcon={null}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={okText}
        cancelText={cancelText}
        centered
        footer={null}
        {...rest}
      >
        <div
          className={clsx(
            'flex items-center justify-center flex-col gap-8 p-2 py-4',
            containerClass
          )}
        >
          <div
            className={clsx(
              'font-bold text-2xl px-6 text-center flex flex-col w-full gap-8  border-b pb-8',
              contentClass
            )}
          >
            <h2>CSVインポート</h2>

            <div className="flex justify-between items-center   ">
              {csvFile ? (
                <div className="flex gap-2  items-center">
                  <Text className="text-tailwindBlue max-w-[150px] truncate ">
                    {csvFile.name}
                  </Text>
                  <HiXCircle
                    onClick={handleRemoveCsv}
                    className="w-5 h-5 text-tailwindNeutral3 cursor-pointer hover:opacity-80 hover:text-tailwindBrand1"
                  />
                </div>
              ) : (
                <div />
              )}
              <Upload {...config}>
                <ButtonComponent
                  className="w-40 h-11 text-tailwindNeutral1 border-tailwindNeutral3 ml-2"
                  title="ファイル選択"
                  afterIcon={<IconUpload color="black" />}
                />
              </Upload>
            </div>
            {csvFileError && (
              <Text className="text-tailwindFailed text-left font-normal text-sm whitespace-pre-line max-h-[60px] overflow-auto">
                {csvFileError}
              </Text>
            )}
          </div>
          <div className={clsx('flex gap-8 px-4 w-full', buttonContainerClass)}>
            <ButtonComponent
              type="primary"
              title={okText}
              onClick={submit}
              className="w-full"
            />
            <ButtonComponent
              type="default"
              title={cancelText}
              onClick={handleCancel}
              className="w-full"
            />
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default ModalImportCsv
