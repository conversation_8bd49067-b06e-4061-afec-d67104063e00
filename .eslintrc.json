{"extends": ["next", "next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "globals": {"React": "readonly"}, "rules": {"no-undef": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "warn", "@next/next/no-img-element": "off", "react-hooks/exhaustive-deps": "off", "react/display-name": "off", "@typescript-eslint/no-extra-semi": "off"}}