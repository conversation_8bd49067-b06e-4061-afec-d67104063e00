import { IIcon } from '@/interfaces'
import Icon from '@ant-design/icons'
import clsx from 'clsx'
import { ReactNode } from 'react'

export const CloseIcon = ({
  width = '25px',
  height = '25px',
  color,
  className = '',
  onClick,
}: IIcon) => (
  <svg
    onClick={onClick}
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color || '#F2B516'}
    strokeWidth="5"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={clsx('ai ai-Cross', className)}
  >
    <path d="M20 20L4 4m16 0L4 20" />
  </svg>
)

export const LoadingIcon = ({
  className = '',
  width = '48px',
  height = '48px',
}: IIcon) => (
  <svg
    width={width}
    height={height}
    className={className}
    viewBox="0 0 47 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M47 24C46.94 20.865 46.259 17.742 45.005 14.886C43.754 12.027 41.936 9.435 39.707 7.296C37.478 5.157 34.835 3.462 31.973 2.343C29.114 1.221 26.039 0.686998 23 0.749998C19.961 0.809998 16.937 1.47 14.171 2.688C11.402 3.903 8.89699 5.664 6.82399 7.824C4.75099 9.984 3.11299 12.546 2.03299 15.315C0.949995 18.084 0.433995 21.057 0.496995 24C0.556995 26.943 1.19899 29.865 2.37799 32.541C3.55399 35.217 5.26099 37.641 7.35199 39.645C9.44299 41.649 11.921 43.23 14.6 44.274C17.276 45.318 20.153 45.816 22.997 45.753C25.844 45.693 28.667 45.072 31.25 43.929C33.836 42.792 36.176 41.142 38.111 39.117C40.046 37.092 41.573 34.701 42.575 32.112C43.187 30.543 43.601 28.905 43.82 27.246C43.877 27.249 43.937 27.252 43.997 27.252C45.653 27.252 46.997 25.908 46.997 24.252C46.997 24.168 46.994 24.084 46.985 24.003H46.997L47 24ZM42.233 31.965C41.132 34.458 39.539 36.717 37.583 38.583C35.627 40.449 33.317 41.919 30.821 42.885C28.325 43.854 25.652 44.313 22.997 44.247C20.345 44.187 17.72 43.602 15.317 42.537C12.914 41.475 10.739 39.936 8.94199 38.052C7.14499 36.168 5.72899 33.939 4.80199 31.533C3.87199 29.13 3.43099 26.553 3.49699 23.997C3.56299 21.441 4.12099 18.915 5.14999 16.605C6.17599 14.295 7.65799 12.201 9.47299 10.473C11.288 8.745 13.436 7.386 15.749 6.495C18.062 5.601 20.537 5.181 22.997 5.247C25.457 5.313 27.884 5.853 30.101 6.843C32.321 7.83 34.331 9.258 35.99 11.004C37.649 12.75 38.954 14.814 39.806 17.037C40.661 19.257 41.06 21.633 40.997 23.997H41.009C41.003 24.078 40.997 24.162 40.997 24.246C40.997 25.794 42.167 27.066 43.673 27.228C43.382 28.86 42.899 30.453 42.23 31.962L42.233 31.965Z"
      fill="currentColor"
    />
  </svg>
)

export const LogoutIcon = () => {
  return (
    <svg
      width={17}
      height={16}
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.6667 1.33325L14 1.33325C14.9205 1.33325 15.6667 2.07944 15.6667 2.99992V12.9999C15.6667 13.9204 14.9205 14.6666 14 14.6666H10.6667M1.5 7.99992L11.5 7.99992M1.5 7.99992L4.83333 4.66659M1.5 7.99992L4.83333 11.3333"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const EditIcon = () => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 12L7.46967 11.4697C7.32902 11.6103 7.25 11.8011 7.25 12H8ZM17 3L17.5303 2.46967C17.2374 2.17678 16.7626 2.17678 16.4697 2.46967L17 3ZM21 7L21.5303 7.53033C21.8232 7.23744 21.8232 6.76256 21.5303 6.46967L21 7ZM12 16V16.75C12.1989 16.75 12.3897 16.671 12.5303 16.5303L12 16ZM8 16H7.25C7.25 16.4142 7.58579 16.75 8 16.75V16ZM20 20V20.75C20.1989 20.75 20.3897 20.671 20.5303 20.5303C20.671 20.3897 20.75 20.1989 20.75 20H20ZM4 20H3.25C3.25 20.4142 3.58579 20.75 4 20.75L4 20ZM4 4L4 3.25C3.58579 3.25 3.25 3.58579 3.25 4L4 4ZM12 4.75C12.4142 4.75 12.75 4.41421 12.75 4C12.75 3.58579 12.4142 3.25 12 3.25V4.75ZM20.75 12C20.75 11.5858 20.4142 11.25 20 11.25C19.5858 11.25 19.25 11.5858 19.25 12H20.75ZM8.53033 12.5303L17.5303 3.53033L16.4697 2.46967L7.46967 11.4697L8.53033 12.5303ZM16.4697 3.53033L20.4697 7.53033L21.5303 6.46967L17.5303 2.46967L16.4697 3.53033ZM20.4697 6.46967L11.4697 15.4697L12.5303 16.5303L21.5303 7.53033L20.4697 6.46967ZM12 15.25H8V16.75H12V15.25ZM8.75 16V12H7.25V16H8.75ZM13.4697 6.53033L17.4697 10.5303L18.5303 9.46967L14.5303 5.46967L13.4697 6.53033ZM20 19.25L4 19.25L4 20.75L20 20.75V19.25ZM4.75 20L4.75 4L3.25 4L3.25 20H4.75ZM4 4.75L12 4.75V3.25L4 3.25L4 4.75ZM19.25 12V20H20.75V12H19.25Z"
        fill="#9E9E9E"
      />
    </svg>
  )
}

export const EditIconTable = () => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 13L0.46967 12.4697C0.329018 12.6103 0.25 12.8011 0.25 13H1ZM10 4L10.5303 3.46967C10.2374 3.17678 9.76256 3.17678 9.46967 3.46967L10 4ZM14 8L14.5303 8.53033C14.8232 8.23744 14.8232 7.76256 14.5303 7.46967L14 8ZM5 17V17.75C5.19891 17.75 5.38968 17.671 5.53033 17.5303L5 17ZM1 17H0.25C0.25 17.4142 0.585786 17.75 1 17.75L1 17ZM1 1V0.25C0.585786 0.25 0.25 0.585786 0.25 1L1 1ZM17 1H17.75C17.75 0.585786 17.4142 0.25 17 0.25V1ZM17 17V17.75C17.4142 17.75 17.75 17.4142 17.75 17H17ZM9 16.25C8.58579 16.25 8.25 16.5858 8.25 17C8.25 17.4142 8.58579 17.75 9 17.75V16.25ZM0.25 9C0.25 9.41421 0.585786 9.75 1 9.75C1.41421 9.75 1.75 9.41421 1.75 9H0.25ZM1.53033 13.5303L10.5303 4.53033L9.46967 3.46967L0.46967 12.4697L1.53033 13.5303ZM9.46967 4.53033L13.4697 8.53033L14.5303 7.46967L10.5303 3.46967L9.46967 4.53033ZM13.4697 7.46967L4.46967 16.4697L5.53033 17.5303L14.5303 8.53033L13.4697 7.46967ZM5 16.25H1V17.75H5V16.25ZM1.75 17V13H0.25V17H1.75ZM6.46967 7.53033L10.4697 11.5303L11.5303 10.4697L7.53033 6.46967L6.46967 7.53033ZM1 1.75H17V0.25H1V1.75ZM16.25 1V17H17.75V1H16.25ZM17 16.25H9V17.75H17V16.25ZM1.75 9V1H0.25V9H1.75Z"
        fill="#696969"
      />
    </svg>
  )
}

export const CopyIcon = () => {
  return (
    <svg
      className="cursor-pointer"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.5 12H17C18.1046 12 19 11.1046 19 10V3C19 1.89543 18.1046 1 17 1H10C8.89543 1 8 1.89543 8 3V4.5M3 19H10C11.1046 19 12 18.1046 12 17V10C12 8.89543 11.1046 8 10 8H3C1.89543 8 1 8.89543 1 10V17C1 18.1046 1.89543 19 3 19Z"
        stroke="#9E9E9E"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const MyNftIcon = ({ color = '#F2B516' }: { color?: string }) => {
  return (
    <svg
      width={18}
      height={19}
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 5H17M1 5V16C1 17.1046 1.89543 18 3 18H15C16.1046 18 17 17.1046 17 16V5M1 5L3 1H15L17 5M12 9C12 10.6569 10.6569 12 9 12C7.34315 12 6 10.6569 6 9"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const TransactionHistoryIcon = ({
  color = '#F2B516',
}: {
  color?: string
}) => {
  return (
    <svg
      width={18}
      height={20}
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 1V5M6 1V5M1 9H17M17 9V17C17 18.1046 16.1046 19 15 19H3C1.89543 19 1 18.1046 1 17V5C1 3.89543 1.89543 3 3 3H15C16.1046 3 17 3.89543 17 5V9Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

const createIcon = (path: ReactNode) =>
  function CommonIcon(props?: IIcon) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={props?.width || 24}
        height={props?.height || 24}
        viewBox={`0 0 ${props?.width || 24} ${props?.height || 24}`}
        fill="none"
      >
        {path}
      </svg>
    )
  }

export const IconReceive = (props: IIcon) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M14 4L18 4C19.1046 4 20 4.89543 20 6V18C20 19.1046 19.1046 20 18 20H14M15 12L3 12M15 12L11 16M15 12L11 8"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconDashboard = createIcon(
  <path
    d="M19.0167 7.14185C19.6261 7.50157 20 8.15653 20 8.86418V18.0001C20 19.1046 19.1046 20.0001 18 20.0001H16C14.8954 20.0001 14 19.1046 14 18.0001V14C14 12.8954 13.1046 12 12 12V12C10.8954 12 10 12.8954 10 14V18.0001C10 19.1046 9.10457 20.0001 8 20.0001H6C4.89543 20.0001 4 19.1046 4 18.0001V8.86418C4 8.15653 4.37395 7.50156 4.98335 7.14185L10.9833 3.60018C11.6106 3.22996 12.3894 3.22996 13.0167 3.60018L19.0167 7.14185Z"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconCreateCollection = createIcon(
  <path
    d="M21 12L12 16L3.00001 12M21 16L12 20L3 16M21 8L12 12L3.00001 8L12 4L21 8Z"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconDiamond = createIcon(
  <path
    d="M21 10H3M10.5 5L9 10L12 20L15 10L13.5 5M3.53192 10.591L11.2567 19.1741C11.6539 19.6155 12.3461 19.6155 12.7433 19.1741L20.4681 10.591C20.7795 10.245 20.811 9.72994 20.544 9.3486L17.7986 5.42654C17.6114 5.15921 17.3057 5 16.9793 5H7.02066C6.69434 5 6.38855 5.15921 6.20142 5.42654L3.45598 9.3486C3.18905 9.72994 3.22053 10.245 3.53192 10.591Z"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinejoin="bevel"
  />
)

export const IconListCollection = createIcon(
  <>
    <path
      d="M40.596 17V7.2H41.632V16.104H47.12V17H40.596ZM48.9101 17V9.65H49.9041V17H48.9101ZM49.4141 8.026C49.2088 8.026 49.0361 7.956 48.8961 7.816C48.7561 7.676 48.6861 7.508 48.6861 7.312C48.6861 7.116 48.7561 6.95267 48.8961 6.822C49.0361 6.682 49.2088 6.612 49.4141 6.612C49.6195 6.612 49.7921 6.67733 49.9321 6.808C50.0721 6.93867 50.1421 7.102 50.1421 7.298C50.1421 7.50333 50.0721 7.676 49.9321 7.816C49.8015 7.956 49.6288 8.026 49.4141 8.026ZM54.9239 17.07C54.3172 17.07 53.7432 16.986 53.2019 16.818C52.6606 16.6407 52.2359 16.4213 51.9279 16.16L52.3759 15.376C52.6746 15.6 53.0572 15.796 53.5239 15.964C53.9906 16.1227 54.4806 16.202 54.9939 16.202C55.6939 16.202 56.1979 16.0947 56.5059 15.88C56.8139 15.656 56.9679 15.362 56.9679 14.998C56.9679 14.7273 56.8792 14.5173 56.7019 14.368C56.5339 14.2093 56.3099 14.0927 56.0299 14.018C55.7499 13.934 55.4372 13.864 55.0919 13.808C54.7466 13.752 54.4012 13.6867 54.0559 13.612C53.7199 13.5373 53.4119 13.43 53.1319 13.29C52.8519 13.1407 52.6232 12.94 52.4459 12.688C52.2779 12.436 52.1939 12.1 52.1939 11.68C52.1939 11.2787 52.3059 10.9193 52.5299 10.602C52.7539 10.2847 53.0806 10.0373 53.5099 9.86C53.9486 9.67333 54.4806 9.58 55.1059 9.58C55.5819 9.58 56.0579 9.64533 56.5339 9.776C57.0099 9.89733 57.4019 10.0607 57.7099 10.266L57.2759 11.064C56.9492 10.84 56.5992 10.6813 56.2259 10.588C55.8526 10.4853 55.4792 10.434 55.1059 10.434C54.4432 10.434 53.9532 10.5507 53.6359 10.784C53.3279 11.008 53.1739 11.2973 53.1739 11.652C53.1739 11.932 53.2579 12.1513 53.4259 12.31C53.6032 12.4687 53.8319 12.5947 54.1119 12.688C54.4012 12.772 54.7139 12.842 55.0499 12.898C55.3952 12.954 55.7359 13.024 56.0719 13.108C56.4172 13.1827 56.7299 13.29 57.0099 13.43C57.2992 13.5607 57.5279 13.752 57.6959 14.004C57.8732 14.2467 57.9619 14.5687 57.9619 14.97C57.9619 15.3993 57.8406 15.7727 57.5979 16.09C57.3646 16.398 57.0192 16.6407 56.5619 16.818C56.1139 16.986 55.5679 17.07 54.9239 17.07ZM62.3898 17.07C61.6992 17.07 61.1672 16.8833 60.7938 16.51C60.4205 16.1367 60.2338 15.6093 60.2338 14.928V8.026H61.2278V14.872C61.2278 15.3013 61.3352 15.6327 61.5498 15.866C61.7738 16.0993 62.0912 16.216 62.5018 16.216C62.9405 16.216 63.3045 16.09 63.5938 15.838L63.9438 16.552C63.7478 16.7293 63.5098 16.86 63.2298 16.944C62.9592 17.028 62.6792 17.07 62.3898 17.07ZM58.9178 10.476V9.65H63.4678V10.476H58.9178ZM74.4434 17.084C73.7061 17.084 73.0247 16.9627 72.3994 16.72C71.7741 16.468 71.2327 16.118 70.7754 15.67C70.3181 15.222 69.9587 14.6947 69.6974 14.088C69.4454 13.4813 69.3194 12.8187 69.3194 12.1C69.3194 11.3813 69.4454 10.7187 69.6974 10.112C69.9587 9.50533 70.3181 8.978 70.7754 8.53C71.2421 8.082 71.7881 7.73667 72.4134 7.494C73.0387 7.242 73.7201 7.116 74.4574 7.116C75.1667 7.116 75.8341 7.23733 76.4594 7.48C77.0847 7.71333 77.6121 8.068 78.0414 8.544L77.3834 9.202C76.9821 8.79133 76.5387 8.49733 76.0534 8.32C75.5681 8.13333 75.0454 8.04 74.4854 8.04C73.8974 8.04 73.3514 8.14267 72.8474 8.348C72.3434 8.544 71.9047 8.82867 71.5314 9.202C71.1581 9.566 70.8641 9.99533 70.6494 10.49C70.4441 10.9753 70.3414 11.512 70.3414 12.1C70.3414 12.688 70.4441 13.2293 70.6494 13.724C70.8641 14.2093 71.1581 14.6387 71.5314 15.012C71.9047 15.376 72.3434 15.6607 72.8474 15.866C73.3514 16.062 73.8974 16.16 74.4854 16.16C75.0454 16.16 75.5681 16.0667 76.0534 15.88C76.5387 15.6933 76.9821 15.3947 77.3834 14.984L78.0414 15.642C77.6121 16.118 77.0847 16.4773 76.4594 16.72C75.8341 16.9627 75.1621 17.084 74.4434 17.084ZM82.9878 17.07C82.2784 17.07 81.6391 16.9113 81.0698 16.594C80.5098 16.2673 80.0664 15.824 79.7398 15.264C79.4131 14.6947 79.2498 14.046 79.2498 13.318C79.2498 12.5807 79.4131 11.932 79.7398 11.372C80.0664 10.812 80.5098 10.3733 81.0698 10.056C81.6298 9.73867 82.2691 9.58 82.9878 9.58C83.7158 9.58 84.3598 9.73867 84.9198 10.056C85.4891 10.3733 85.9324 10.812 86.2498 11.372C86.5764 11.932 86.7398 12.5807 86.7398 13.318C86.7398 14.046 86.5764 14.6947 86.2498 15.264C85.9324 15.824 85.4891 16.2673 84.9198 16.594C84.3504 16.9113 83.7064 17.07 82.9878 17.07ZM82.9878 16.188C83.5198 16.188 83.9911 16.0713 84.4018 15.838C84.8124 15.5953 85.1344 15.2593 85.3678 14.83C85.6104 14.3913 85.7318 13.8873 85.7318 13.318C85.7318 12.7393 85.6104 12.2353 85.3678 11.806C85.1344 11.3767 84.8124 11.0453 84.4018 10.812C83.9911 10.5693 83.5244 10.448 83.0018 10.448C82.4791 10.448 82.0124 10.5693 81.6018 10.812C81.1911 11.0453 80.8644 11.3767 80.6218 11.806C80.3791 12.2353 80.2578 12.7393 80.2578 13.318C80.2578 13.8873 80.3791 14.3913 80.6218 14.83C80.8644 15.2593 81.1911 15.5953 81.6018 15.838C82.0124 16.0713 82.4744 16.188 82.9878 16.188ZM89.0491 17V6.612H90.0431V17H89.0491ZM93.0889 17V6.612H94.0829V17H93.0889ZM100.251 17.07C99.4853 17.07 98.8133 16.9113 98.2347 16.594C97.656 16.2673 97.2033 15.824 96.8767 15.264C96.55 14.6947 96.3867 14.046 96.3867 13.318C96.3867 12.59 96.5407 11.946 96.8487 11.386C97.166 10.826 97.5953 10.3873 98.1367 10.07C98.6873 9.74333 99.3033 9.58 99.9847 9.58C100.675 9.58 101.287 9.73867 101.819 10.056C102.36 10.364 102.785 10.8027 103.093 11.372C103.401 11.932 103.555 12.5807 103.555 13.318C103.555 13.3647 103.55 13.416 103.541 13.472C103.541 13.5187 103.541 13.57 103.541 13.626H97.1427V12.884H103.009L102.617 13.178C102.617 12.646 102.5 12.1747 102.267 11.764C102.043 11.344 101.735 11.0173 101.343 10.784C100.951 10.5507 100.498 10.434 99.9847 10.434C99.4807 10.434 99.028 10.5507 98.6267 10.784C98.2253 11.0173 97.9127 11.344 97.6887 11.764C97.4647 12.184 97.3527 12.6647 97.3527 13.206V13.36C97.3527 13.92 97.474 14.4147 97.7167 14.844C97.9687 15.264 98.314 15.5953 98.7527 15.838C99.2007 16.0713 99.7093 16.188 100.279 16.188C100.727 16.188 101.142 16.1087 101.525 15.95C101.917 15.7913 102.253 15.5487 102.533 15.222L103.093 15.866C102.766 16.258 102.355 16.5567 101.861 16.762C101.375 16.9673 100.839 17.07 100.251 17.07ZM108.91 17.07C108.182 17.07 107.529 16.9113 106.95 16.594C106.381 16.2673 105.933 15.824 105.606 15.264C105.279 14.6947 105.116 14.046 105.116 13.318C105.116 12.5807 105.279 11.932 105.606 11.372C105.933 10.812 106.381 10.3733 106.95 10.056C107.529 9.73867 108.182 9.58 108.91 9.58C109.535 9.58 110.1 9.70133 110.604 9.944C111.108 10.1867 111.505 10.5507 111.794 11.036L111.052 11.54C110.8 11.1667 110.487 10.8913 110.114 10.714C109.741 10.5367 109.335 10.448 108.896 10.448C108.373 10.448 107.902 10.5693 107.482 10.812C107.062 11.0453 106.731 11.3767 106.488 11.806C106.245 12.2353 106.124 12.7393 106.124 13.318C106.124 13.8967 106.245 14.4007 106.488 14.83C106.731 15.2593 107.062 15.5953 107.482 15.838C107.902 16.0713 108.373 16.188 108.896 16.188C109.335 16.188 109.741 16.0993 110.114 15.922C110.487 15.7447 110.8 15.474 111.052 15.11L111.794 15.614C111.505 16.09 111.108 16.454 110.604 16.706C110.1 16.9487 109.535 17.07 108.91 17.07ZM116.433 17.07C115.742 17.07 115.21 16.8833 114.837 16.51C114.463 16.1367 114.277 15.6093 114.277 14.928V8.026H115.271V14.872C115.271 15.3013 115.378 15.6327 115.593 15.866C115.817 16.0993 116.134 16.216 116.545 16.216C116.983 16.216 117.347 16.09 117.637 15.838L117.987 16.552C117.791 16.7293 117.553 16.86 117.273 16.944C117.002 17.028 116.722 17.07 116.433 17.07ZM112.961 10.476V9.65H117.511V10.476H112.961ZM120.076 17V9.65H121.07V17H120.076ZM120.58 8.026C120.375 8.026 120.202 7.956 120.062 7.816C119.922 7.676 119.852 7.508 119.852 7.312C119.852 7.116 119.922 6.95267 120.062 6.822C120.202 6.682 120.375 6.612 120.58 6.612C120.785 6.612 120.958 6.67733 121.098 6.808C121.238 6.93867 121.308 7.102 121.308 7.298C121.308 7.50333 121.238 7.676 121.098 7.816C120.967 7.956 120.795 8.026 120.58 8.026ZM127.112 17.07C126.403 17.07 125.763 16.9113 125.194 16.594C124.634 16.2673 124.191 15.824 123.864 15.264C123.537 14.6947 123.374 14.046 123.374 13.318C123.374 12.5807 123.537 11.932 123.864 11.372C124.191 10.812 124.634 10.3733 125.194 10.056C125.754 9.73867 126.393 9.58 127.112 9.58C127.84 9.58 128.484 9.73867 129.044 10.056C129.613 10.3733 130.057 10.812 130.374 11.372C130.701 11.932 130.864 12.5807 130.864 13.318C130.864 14.046 130.701 14.6947 130.374 15.264C130.057 15.824 129.613 16.2673 129.044 16.594C128.475 16.9113 127.831 17.07 127.112 17.07ZM127.112 16.188C127.644 16.188 128.115 16.0713 128.526 15.838C128.937 15.5953 129.259 15.2593 129.492 14.83C129.735 14.3913 129.856 13.8873 129.856 13.318C129.856 12.7393 129.735 12.2353 129.492 11.806C129.259 11.3767 128.937 11.0453 128.526 10.812C128.115 10.5693 127.649 10.448 127.126 10.448C126.603 10.448 126.137 10.5693 125.726 10.812C125.315 11.0453 124.989 11.3767 124.746 11.806C124.503 12.2353 124.382 12.7393 124.382 13.318C124.382 13.8873 124.503 14.3913 124.746 14.83C124.989 15.2593 125.315 15.5953 125.726 15.838C126.137 16.0713 126.599 16.188 127.112 16.188ZM136.925 9.58C137.523 9.58 138.045 9.69667 138.493 9.93C138.951 10.154 139.305 10.4993 139.557 10.966C139.819 11.4327 139.949 12.0207 139.949 12.73V17H138.955V12.828C138.955 12.0533 138.759 11.47 138.367 11.078C137.985 10.6767 137.443 10.476 136.743 10.476C136.221 10.476 135.763 10.5833 135.371 10.798C134.989 11.0033 134.69 11.3067 134.475 11.708C134.27 12.1 134.167 12.576 134.167 13.136V17H133.173V9.65H134.125V11.666L133.971 11.288C134.205 10.756 134.578 10.3407 135.091 10.042C135.605 9.734 136.216 9.58 136.925 9.58Z"
      fill="currentColor"
    />
    <path
      d="M3 18H21M12 6V4M4 15V14C4 9.58172 7.58172 6 12 6V6C16.4183 6 20 9.58172 20 14V15H4Z"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </>
)

export const IconListNft = IconListCollection

export const IconApproval = createIcon(
  <path
    d="M12 16H12.01M12 12H12.01M12 8H12.01M21 14V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17V14C4.10457 14 5 13.1046 5 12C5 10.8954 4.10457 10 3 10V7C3 5.89543 3.89543 5 5 5H19C20.1046 5 21 5.89543 21 7V10C19.8954 10 19 10.8954 19 12C19 13.1046 19.8954 14 21 14Z"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconUserMangement = createIcon(
  <path
    d="M18.5051 19H20C21.1046 19 22.0669 18.076 21.716 17.0286C21.1812 15.4325 19.8656 14.4672 17.5527 14.1329M14.5001 10.8645C14.7911 10.9565 15.1244 11 15.5 11C17.1667 11 18 10.1429 18 8C18 5.85714 17.1667 5 15.5 5C15.1244 5 14.7911 5.04354 14.5001 5.13552M9.5 14C13.1135 14 15.0395 15.0095 15.716 17.0286C16.0669 18.076 15.1046 19 14 19H5C3.89543 19 2.93311 18.076 3.28401 17.0286C3.96047 15.0095 5.88655 14 9.5 14ZM9.5 11C11.1667 11 12 10.1429 12 8C12 5.85714 11.1667 5 9.5 5C7.83333 5 7 5.85714 7 8C7 10.1429 7.83333 11 9.5 11Z"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconBannerManagement = createIcon(
  <path
    d="M4 17L7.58959 13.7694C8.38025 13.0578 9.58958 13.0896 10.3417 13.8417L11.5 15L15.0858 11.4142C15.8668 10.6332 17.1332 10.6332 17.9142 11.4142L20 13.5M11 9C11 9.55228 10.5523 10 10 10C9.44772 10 9 9.55228 9 9C9 8.44772 9.44772 8 10 8C10.5523 8 11 8.44772 11 9ZM6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20Z"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconCreditCard = createIcon(
  <path
    d="M21 10V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17V10M21 10V8C21 6.89543 20.1046 6 19 6H5C3.89543 6 3 6.89543 3 8V10M21 10H12H3"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconSpeaker = createIcon(
  <path
    d="M6 18V14M6 14H8L13 17V7L8 10H5C3.89543 10 3 10.8954 3 12V12C3 13.1046 3.89543 14 5 14H6ZM17 7L19 5M17 17L19 19M19 12H21"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconAccount = createIcon(
  <path
    d="M24 24.9444C23.311 21.8333 20.7142 20 16.0002 20C11.2861 20 8.68901 21.8333 8 24.9444M16 28C22.6274 28 28 22.6274 28 16C28 9.37258 22.6274 4 16 4C9.37258 4 4 9.37258 4 16C4 22.6274 9.37258 28 16 28ZM16 16C17.7778 16 18.6667 15.0476 18.6667 12.6667C18.6667 10.2857 17.7778 9.33333 16 9.33333C14.2222 9.33333 13.3333 10.2857 13.3333 12.6667C13.3333 15.0476 14.2222 16 16 16Z"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconGear = createIcon(
  <>
    <path
      d="M6.65202 2.56614C6.85537 1.65106 7.667 1 8.6044 1H9.39571C10.3331 1 11.1447 1.65106 11.3481 2.56614L11.551 3.47935C12.2121 3.73819 12.8243 4.09467 13.3697 4.53105L14.2639 4.24961C15.1581 3.96818 16.1277 4.34554 16.5964 5.15735L16.9921 5.84264C17.4608 6.65445 17.3028 7.68287 16.612 8.31652L15.9218 8.94961C15.9733 9.29223 16.0001 9.643 16.0001 10C16.0001 10.357 15.9733 10.7078 15.9218 11.0504L16.612 11.6835C17.3028 12.3171 17.4608 13.3455 16.9921 14.1574L16.5965 14.8426C16.1278 15.6545 15.1581 16.0318 14.2639 15.7504L13.3698 15.4689C12.8243 15.9053 12.2121 16.2618 11.551 16.5206L11.3481 17.4339C11.1447 18.3489 10.3331 19 9.39571 19H8.6044C7.667 19 6.85537 18.3489 6.65202 17.4339L6.44909 16.5206C5.78796 16.2618 5.17579 15.9053 4.63034 15.4689L3.73614 15.7504C2.84199 16.0318 1.87234 15.6545 1.40364 14.8426L1.00798 14.1573C0.539284 13.3455 0.697308 12.3171 1.38811 11.6835L2.07833 11.0504C2.02678 10.7077 2.00005 10.357 2.00005 10C2.00005 9.643 2.02678 9.29225 2.07833 8.94962L1.38813 8.31652C0.697322 7.68288 0.539297 6.65446 1.008 5.84265L1.40365 5.15735C1.87235 4.34554 2.842 3.96818 3.73616 4.24962L4.63035 4.53106C5.1758 4.09467 5.78796 3.73819 6.44909 3.47935L6.65202 2.56614Z"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 10C10 10.5523 9.55232 11 9.00003 11C8.44775 11 8.00003 10.5523 8.00003 10C8.00003 9.44772 8.44775 9 9.00003 9C9.55232 9 10 9.44772 10 10Z"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </>
)

export const IconChevronDown = createIcon(
  <path
    d="M4.5 6.75L8.46967 10.7197C8.76256 11.0126 9.23744 11.0126 9.53033 10.7197L13.5 6.75"
    stroke="currentColor"
    strokeWidth="1.2"
    strokeLinecap="round"
    strokeLinejoin="round"
  />
)

export const IconSearch = (props: IIcon) => (
  <svg
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 15 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.15749 9.15755L13.8333 13.8333M5.91659 10.5C8.44789 10.5 10.4999 8.44795 10.4999 5.91665C10.4999 3.38534 8.44789 1.33331 5.91659 1.33331C3.38528 1.33331 1.33325 3.38534 1.33325 5.91665C1.33325 8.44795 3.38528 10.5 5.91659 10.5Z"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconPen = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.25 4.875L13.125 6.75M3 15V13.125L12.5625 3.5625C13.0803 3.04473 13.9197 3.04473 14.4375 3.5625V3.5625C14.9553 4.08027 14.9553 4.91973 14.4375 5.4375L4.875 15H3Z"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconRemoveCircle = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 9H6M9 15.75C12.7279 15.75 15.75 12.7279 15.75 9C15.75 5.27208 12.7279 2.25 9 2.25C5.27208 2.25 2.25 5.27208 2.25 9C2.25 12.7279 5.27208 15.75 9 15.75Z"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconCheckCircle = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 6.75L7.5 11.625L5.625 9.75M9 15.75C12.7279 15.75 15.75 12.7279 15.75 9C15.75 5.27208 12.7279 2.25 9 2.25C5.27208 2.25 2.25 5.27208 2.25 9C2.25 12.7279 5.27208 15.75 9 15.75Z"
      stroke="currentColor"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconRightArrow = (props: IIcon) => (
  <svg
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 17L15 12L10 7"
      stroke={props.color || '#696969'}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconAdd = (props: IIcon) => (
  <svg
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 5V13M5 9L13 9M5 17H13C15.2091 17 17 15.2091 17 13V5C17 2.79086 15.2091 1 13 1H5C2.79086 1 1 2.79086 1 5V13C1 15.2091 2.79086 17 5 17Z"
      stroke={props.color || 'white'}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconUpload = (props: IIcon) => (
  <svg
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7 9V8C7 5.23858 9.23858 3 12 3C14.7614 3 17 5.23858 17 8V9C19.2091 9 21 10.7909 21 13C21 14.4806 20.1956 15.7733 19 16.4649M7 9C4.79086 9 3 10.7909 3 13C3 14.4806 3.8044 15.7733 5 16.4649M7 9C7.43285 9 7.84965 9.06875 8.24006 9.19594M12 11V21M12 11L16 15M12 11L8 15"
      stroke={props.color || '#9e9e9e'}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const IconRightButton = (props: IIcon) => (
  <svg
    width={props.width || 24}
    height={props.height || 24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 12L7 12M17 12L13 16M17 12L13 8"
      stroke={props.color || 'white'}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export const OutlineArrowForward = (props: IIcon) => (
  <svg
    width={props.width || 26}
    height={props.height || 24}
    viewBox="0 0 26 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2 10.5C1.17157 10.5 0.5 11.1716 0.5 12C0.5 12.8284 1.17157 13.5 2 13.5L2 10.5ZM25.0607 13.0607C25.6464 12.4749 25.6464 11.5251 25.0607 10.9393L15.5147 1.3934C14.9289 0.80761 13.9792 0.80761 13.3934 1.3934C12.8076 1.97918 12.8076 2.92893 13.3934 3.51472L21.8787 12L13.3934 20.4853C12.8076 21.0711 12.8076 22.0208 13.3934 22.6066C13.9792 23.1924 14.9289 23.1924 15.5147 22.6066L25.0607 13.0607ZM2 13.5L24 13.5L24 10.5L2 10.5L2 13.5Z"
      fill="url(#paint0_linear_69_785)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_69_785"
        x1={2}
        y1="12.5"
        x2={24}
        y2="12.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FF7E5F" />
        <stop offset={1} stopColor="#FFB88C" />
      </linearGradient>
    </defs>
  </svg>
)

export const AboutXIcon = (props: IIcon) => (
  <svg
    width={props.width || 99}
    height={props.height || 93}
    viewBox="0 0 99 93"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <line
      x1="7.28628"
      y1="1.58579"
      x2="97.2863"
      y2="91.5858"
      stroke="url(#paint0_linear_304_19)"
      strokeWidth={4}
    />
    <line
      y1={-2}
      x2="127.279"
      y2={-2}
      transform="matrix(-0.707107 0.707107 0.707107 0.707107 93.1282 3)"
      stroke="url(#paint1_linear_304_19)"
      strokeWidth={4}
    />
    <defs>
      <linearGradient
        id="paint0_linear_304_19"
        x1="5.51852"
        y1="3.35355"
        x2="95.5185"
        y2="93.3536"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FBED96" />
        <stop offset={1} stopColor="#ABECD6" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_304_19"
        x1={0}
        y1="0.5"
        x2="127.279"
        y2="0.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FBED96" />
        <stop offset={1} stopColor="#ABECD6" />
      </linearGradient>
    </defs>
  </svg>
)

export const IconUserCircle = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18 18.7083C17.4832 16.375 15.5357 15 12.0001 15C8.46459 15 6.51676 16.375 6 18.7083M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM12 12C13.3333 12 14 11.2857 14 9.5C14 7.71429 13.3333 7 12 7C10.6667 7 10 7.71429 10 9.5C10 11.2857 10.6667 12 12 12Z"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconCopy = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.25 12V3.5C2.25 2.39543 3.14543 1.5 4.25 1.5H11.25M7.25 16.5H13C14.1046 16.5 15 15.6046 15 14.5V6.5C15 5.39543 14.1046 4.5 13 4.5H7.25C6.14543 4.5 5.25 5.39543 5.25 6.5V14.5C5.25 15.6046 6.14543 16.5 7.25 16.5Z"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconOutlinedUser = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.25 15C13.3546 15 14.3258 14.0723 13.957 13.0311C13.3593 11.3437 11.8124 10.5 9 10.5C6.18759 10.5 4.64072 11.3437 4.04302 13.0311C3.67422 14.0723 4.64543 15 5.75 15H12.25Z"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M9 8.25C10.5 8.25 11.25 7.5 11.25 5.625C11.25 3.75 10.5 3 9 3C7.5 3 6.75 3.75 6.75 5.625C6.75 7.5 7.5 8.25 9 8.25Z"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconLineChart = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.25 12.375L6.75 7.5L9.75 12L15.75 4.875"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconWallet = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.25 4.5V12C2.25 13.6569 3.59315 15 5.25 15H14.75C15.3023 15 15.75 14.5523 15.75 14V12M14.25 6H3.75C2.92157 6 2.25 5.32843 2.25 4.5V4.5C2.25 3.67157 2.92157 3 3.75 3H13.25C13.8023 3 14.25 3.44772 14.25 4V6ZM14.25 6H14.75C15.3023 6 15.75 6.44772 15.75 7V9M15.75 9H13.5C12.6716 9 12 9.67157 12 10.5V10.5C12 11.3284 12.6716 12 13.5 12H15.75M15.75 9V12"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconDollar = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.99998 2.25V15.75M11.7592 4.97579C10.5473 3.20182 5.95906 3.01647 5.95906 6.12559C5.95906 9.2347 12.5643 7.74747 12.1829 11.4089C11.8649 14.4618 6.89474 14.5038 5.4082 12.0668"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconExternalLink = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="18"
        height="20"
        viewBox="0 0 18 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.5 3.17944H4.5C3.67157 3.17944 3 3.85102 3 4.67944V14.3972C3 15.2256 3.67157 15.8972 4.5 15.8972H13.5C14.3284 15.8972 15 15.2256 15 14.3972V11.128M8.25 10.3332L15 3.17944M15 3.17944V7.15374M15 3.17944H11.25"
          stroke="currentColor"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconExport = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="54"
        height="54"
        viewBox="0 0 54 54"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="download-2">
          <path
            id="Icon"
            d="M36 24.75L27 33.75M27 33.75L18 24.75M27 33.75V6.75M47.25 33.75V36.75C47.25 40.0637 44.5637 42.75 41.25 42.75H12.75C9.43629 42.75 6.75 40.0637 6.75 36.75V33.75"
            stroke="#F2C157"
            strokeWidth="1.2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
      </svg>
    )}
    {...props}
    rev
  />
)

export const IconSortDropDrag = (props: IIcon) => (
  <Icon
    component={() => (
      <svg
        width="12"
        height="6"
        viewBox="0 0 12 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.75 4.5H11.25M0.75 1.5H11.25"
          stroke="#F2C157"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    )}
    {...props}
    rev
  />
)
