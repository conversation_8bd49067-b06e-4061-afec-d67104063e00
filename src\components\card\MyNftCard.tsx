import React from 'react'
import { CARD_SIZES } from '@/constants'
import UiCard from '@/components/ui/Card'
import UiButton from '../ui/Button'
import { getCurrentLanguage } from '@/utils'

interface ActionButton {
  label: string
  onClick: () => void
  disabled?: boolean
}

interface MyNftCardProps {
  name: string
  imageUrl: string
  actions?: ActionButton[]
  size?: CARD_SIZES
  videoUrl?: string
}

export function MyNftCard({
  name,
  imageUrl,
  actions = [],
  size = CARD_SIZES.MD,
  videoUrl
}: MyNftCardProps) {
  const storedLang = getCurrentLanguage()
  const viewClass = storedLang === 'ja' ? 'w-[38%]' : ''
  // Create action buttons JSX
  const actionButtons =
    actions.length > 0 ? (
      <div className="flex gap-2 flex-row">
        {actions.map((action, index) =>
          index === 0 ? (
            <UiButton
              key={`${index}-${action.label}`}
              title={action.label}
              handleClick={action.onClick}
              isBorder={true}
              isGradient={false}
              className={`w-full ${viewClass}`}
              isDisabled={action.disabled}
            />
          ) : (
            <UiButton
              key={`${index}-${action.label}`}
              title={action.label}
              handleClick={action.onClick}
              className="w-full"
              isDisabled={action.disabled}
            />
          )
        )}
      </div>
    ) : null

  return (
    <UiCard
      title={name}
      image={imageUrl}
      size={size}
      variant="default"
      video={videoUrl}
    >
      {actionButtons}
    </UiCard>
  )
}
