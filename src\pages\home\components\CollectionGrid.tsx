import React from 'react'
import { Category, Collection } from '@/interfaces'
import UiCard from '@/components/ui/Card'
import { useTranslate } from '@/hooks'
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES, CARD_SIZES } from '@/constants'
import { ReloadOutlined } from '@ant-design/icons'
import { FALLBACK_IMAGE } from '@/constants'

interface CollectionGridProps {
  collections: Collection[]
  isLoading?: boolean
  hasMore?: boolean
  onLoadMore?: () => void
  className?: string
  categories: Category[]
  noResultsText?: string
  noResultsSubtext?: string
  loadMoreText?: string
}

const CollectionGrid: React.FC<CollectionGridProps> = ({
  collections,
  isLoading = false,
  hasMore = false,
  onLoadMore,
  className = '',
  noResultsText,
  noResultsSubtext,
  loadMoreText,
}) => {
  const t = useTranslate()

  if (isLoading && !collections.length) {
    return (
      <div
        className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
      >
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="bg-gray-800 rounded-lg overflow-hidden animate-pulse"
          >
            <div className="aspect-square bg-gray-700" />
            <div className="p-4 space-y-3">
              <div className="h-4 bg-gray-700 rounded" />
              <div className="h-3 bg-gray-700 rounded w-3/4" />
              <div className="h-3 bg-gray-700 rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Collection Grid */}
      <div
        className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
      >
        {collections.map((collection) => (
          <UiCard
            key={collection._id}
            title={collection.name}
            image={collection.logoImage || FALLBACK_IMAGE}
            price={`${collection?.floorPrice || 0}`}
            priceLabel={t.floor_price}
            href={`/collections/${collection._id}`}
            variant="default"
            size={CARD_SIZES.MD}
            className="group"
            isSelected={false}
            imageClassName="object-cover transition-transform duration-300 group-hover:scale-110 rounded-md"
            titleToolTip={true}
          />
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="flex justify-center w-full">
          <UiButton
            title={
              <span>
                <ReloadOutlined rev={undefined} />{' '}
                {loadMoreText || t.nftGrid_loadMore}
              </span>
            }
            size={BUTTON_SIZES.XL}
            isLoading={isLoading}
            handleClick={onLoadMore}
            isGradient={false}
            className="flex items-center gap-2 !text-primary"
          />
        </div>
      )}

      {/* Empty State */}
      {!isLoading && collections.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">
            {noResultsText || t.nftGrid_noResults}
          </div>
          <p className="text-gray-500">
            {noResultsSubtext || t.nftGrid_noResultsSubtext}
          </p>
        </div>
      )}
    </div>
  )
}

export default CollectionGrid
