import { TYPE_NFT } from '@/constants/type'
import { Dispatch, SetStateAction } from 'react'

export type NftHeaderPropsType = {
  total?: number
  search: NFTSearchType
  setSearch: Dispatch<SetStateAction<NFTSearchType>>
  className?: string
}

export type NFTSearchType = {
  type?: string
  sort?: string
  sortCondition?: string
  search?: string
  searchWord?: string
  pageSize?: number
  page?: number
  itemType?: number | null
}

export type TDefaultNftSearch = {
  pageSize?: number
  pageIndex?: number
  sortCondition?: string
  searchWord?: string
  itemType?: number | null
  status?: string
}

export interface INFTComponentProps {
  _id: string
  tokenId: string
  name: string
  itemType: TYPE_NFT
  saleInformation: {
    fiatCurrency?: string
    priceByFiat?: number | string
    intermediaryId: string
    link: string
    productId: string
  }
  creatorName: string
  image: string
}

export type TNftsData = {
  items: INFTComponentProps[]
  pageIndex?: number
  pageSize?: number
  totalItems?: number
}

export interface TransactionInfo {
  boughtAt: number
  buyerAddress: string
  createdAt: string
  fiatCurrency: string
  priceByFiat: number
  sellerAddress: string
  tokenId: string
  transactionHash: string
  updatedAt: string
  _id: string
}

export interface UserTransactions {
  items: TransactionInfo[]
  pageIndex: number
  pageSize: number
  totalItems: number
}
