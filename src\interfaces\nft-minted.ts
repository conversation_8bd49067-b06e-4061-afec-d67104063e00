import { Paginated } from '.'

// NFT Minted Item interface
export type NftMintedItem = {
  tokenId: string
  metadataId: string
  name: string
  description: string
  image: string
  attributes?: Array<{
    trait_type: string
    value: string
  }>
  collection?: string
  category?: string
  createdAt: string
  updatedAt: string
  _id?: string
  price: number
  canSetAsProfileImage?: boolean
  isVideo?: boolean
}

// Paginated NFT Minted Response interface
export type PaginatedNftMintedResponse = Paginated<NftMintedItem>

// NFT Metadata interface for external endpoint
export interface NftMetadata {
  name: string
  description: string
  image: string
  attributes?: Array<{
    trait_type: string
    value: string
  }>
  external_url?: string
  animation_url?: string
  collection?: any
  mintedCount?: number
  canSetAsProfileImage?: boolean
}
