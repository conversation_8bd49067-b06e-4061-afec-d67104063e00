import React, { useEffect, useMemo, useState } from 'react'
import { Empty } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import Link from 'next/link'
import { pageConfig, ROUTES } from '@/constants'
import { ColumnUsersTableType, UserSearchInput, Users } from '@/interfaces'
import AdminTable from '@/components/table/AdminTable'
function UserTableList({
  dataUsers,
  valueSearch,
  setSearch,
}: {
  dataUsers: Users | undefined
  valueSearch: UserSearchInput
  setSearch: (data: UserSearchInput) => void
}) {
  const [isLoading, setLoading] = useState(false)
  // const totalHeightMinus = 390
  // const { tableHeight } = useTableHeight(totalHeightMinus)
  const users: ColumnUsersTableType[] | undefined = useMemo(
    () =>
      dataUsers?.items &&
      dataUsers.items.map((data) => {
        return {
          key: data._id,
          name: data.name || '',
          walletAddress: data.walletAddress,
          otherWallet: data.otherWallet,
        }
      }),
    [dataUsers]
  )

  const text = {
    userName: 'ユーザー名',
    walletAddress: 'ウォレットアドレス',
    moreDetail: '詳細',
  }

  const columns: ColumnsType<ColumnUsersTableType> = [
    {
      title: text.userName,
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (name: string) => (
        <span className="font-montserrat font-semibold text-sm tracking-[0.02em] leading-[170%] text-white">
          {name || '-'}
        </span>
      ),
    },
    {
      title: text.walletAddress,
      dataIndex: 'otherWallet',
      key: 'otherWallet',
      render: (otherWallet) => (
        <span className="font-montserrat font-normal text-sm tracking-[0.02em] leading-[170%] text-white">
          {otherWallet}
        </span>
      ),
    },
    {
      title: ' ',
      dataIndex: 'action',
      align: 'center',
      width: '15%',
      key: 'action',
      render: (_, record) => (
        <Link
          className="flex items-center justify-center text-primary space-x-2"
          href={`${ROUTES.adminUsers}/${record?.walletAddress}`}
        >
          {/* <IconExternalLink /> */}
          <span className="mr-1">{text.moreDetail}</span>
        </Link>
      ),
    },
  ]

  useEffect(() => {
    setLoading(true)
    users && setLoading(false)
  }, [users])

  return (
    <div className="mt-6">
      <AdminTable
        className="mx-2 custom-table"
        pagination={{
          position: ['bottomCenter'],
          current: valueSearch.page,
          pageSize: dataUsers?.pageSize,
          total: dataUsers?.totalItems,
          onChange: (page) => setSearch({ ...valueSearch, page }),
        }}
        loading={isLoading}
        columns={columns}
        dataSource={users}
        // scroll={{ y: tableHeight }}
        scroll={{ x: 'auto' }}
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={pageConfig.noData}
            />
          ),
        }}
      />
    </div>
  )
}

export default UserTableList
