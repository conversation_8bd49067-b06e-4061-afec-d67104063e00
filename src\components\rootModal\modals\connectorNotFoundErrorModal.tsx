import React from 'react'

import ModalLayout from '../modalLayout'
import { useStore } from '@/store'
import { IconWarning } from '@/icons'
import ButtonComponent from '@/components/buttons/Button'
import clsx from 'clsx'

const ConnectorNotFoundErrorModal: React.FC = () => {
  const { setModalType } = useStore()

  return (
    <ModalLayout
      className="h-auto !w-[472px] flex items-center"
      backgroundAfter="after:bg-gray-800"
      noHeader
    >
      <div className="flex flex-col items-center justify-center">
        <IconWarning className="-mt-16" />
        <div
          className={clsx(
            'mt-12 text-2xl text-black text-center font-bold break-words line-clamp-4 px-5 mb-4',
            'max-sm:text-xl max-sm:p-0 max-sm:mb-3 max-sm:mt-6'
          )}
        >
          metamaskをインストールするには、このリンクをクリックしてください。
        </div>
        <a
          className="text-[#00A3FF]"
          href={'https://metamask.io/'}
          target="_blank"
          rel="noreferrer"
        >
          https://metamask.io/
        </a>
      </div>
      <ButtonComponent
        className="m-auto mt-12 max-sm:mt-6"
        type="primary"
        title="OK"
        onClick={() => setModalType({ type: null })}
      />
    </ModalLayout>
  )
}

export default ConnectorNotFoundErrorModal
