import { Modal, Form, Input, Select } from 'antd'
import {
  ADMIN_ERR_MESSAGE,
  ADMIN_ROLE,
  ADMIN_ROLE_MAP,
  BUTTON_SIZES,
} from '../../../constants'
import { createAdmin as createAdminApi } from '../../../services/apiCall/admin'
import { CreateAdminBody, Admin } from '../../../interfaces/admin'
import { useMutation } from 'wagmi'
import { useEffect } from 'react'
import { editAdmin as editAdminApi } from '../../../services/apiCall/admin'
import { toast } from 'react-toastify'
import UiButton from '@/components/ui/Button'

interface CreateAdminAccountModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  admin?: Admin | null
  isEdit?: boolean
}

export default function CreateAdminAccountModal({
  open,
  onCancel,
  onSuccess,
  admin,
  isEdit,
}: CreateAdminAccountModalProps) {
  const [form] = Form.useForm()

  const text = {
    createAdminFailed: '管理者の作成に失敗しました',
    updateAdminFailed: '管理者の更新に失敗しました',
    update: '更新',
    create: '作成',
    editAdmin: '管理者アカウント編集',
    createAdmin: '管理者アカウント作成',
    pleaseInputEmail: 'メールアドレスを入力してください。',
    invalidEmail: '無効なメールアドレス',
    pleaseInputPassword: 'パスワードを入力してください',
    pleaseSelectRole: '役割を選択してください',
    passwordMustBeNoLessThan8: 'パスワードは 8 文字以上である必要があります。',
    email: 'メールアドレス',
    password: 'パスワード',
    role: '役割',
    contactEmailMaxLength: 'メールアドレスは最大255文字まで入力できます',
    passwordMaxLength: 'パスワードは最大100文字まで入力できます',
  }

  useEffect(() => {
    if (admin) {
      form.setFieldsValue({
        email: admin.email,
        password: '', // leave blank for security
        role: admin.role,
        status: admin.status,
      })
    } else {
      form.resetFields()
    }
  }, [admin, form, open])

  const { mutate: saveAdmin, isPending } = useMutation({
    mutationFn: (data: CreateAdminBody) => {
      if (isEdit && admin) {
        // Exclude password if not entered
        const { password, ...rest } = data
        const body = password ? data : rest
        return editAdminApi(admin._id, body as Partial<CreateAdminBody>)
      }
      return createAdminApi(data)
    },
    onSuccess: () => {
      form.resetFields()
      onSuccess()
      onCancel()
    },
    onError: (error: any) => {
      let errMessage = isEdit ? text.updateAdminFailed : text.createAdminFailed
      errMessage =
        ADMIN_ERR_MESSAGE[error?.response?.data?.message] || errMessage
      toast.error(errMessage)
    },
  })

  function handleFinish(values: any) {
    if (isEdit && !values.password) {
      // Remove password if empty
      saveAdmin({
        email: values.email,
        role: values.role,
      } as CreateAdminBody)
    } else {
      saveAdmin(values)
    }
  }

  return (
    <Modal
      title={
        <p className="text-center mb-12 mt-8 text-xl font-semibold">
          {isEdit ? text.editAdmin : text.createAdmin}
        </p>
      }
      open={open}
      onCancel={() => {
        onCancel()
      }}
      footer={null}
      centered
      destroyOnClose
      styles={{
        body: {
          paddingLeft: '2rem',
          paddingRight: '2rem',
        },
      }}
      className="rounded-[8px] border-[#525252] border-[1px]"
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        initialValues={{
          role: ADMIN_ROLE.approver,
          // status: ACCOUNT_STATUS.active, // remove status
        }}
        requiredMark={false}
      >
        <Form.Item
          label={
            <span className="font-montserrat font-semibold">
              {text.email}
              <span style={{ color: 'red', marginLeft: 4 }}>*</span>
            </span>
          }
          name="email"
          rules={[
            { required: true, message: text.pleaseInputEmail },
            {
              max: 255,
              message: text.contactEmailMaxLength,
            },
            { type: 'email', message: text.invalidEmail },
          ]}
        >
          <Input
            maxLength={255}
            placeholder="<EMAIL>"
            disabled={isEdit} // disable edit email
            className="h-[44px]"
            showCount
          />
        </Form.Item>
        <Form.Item
          label={
            <span className="font-montserrat font-semibold">
              {text.password}
              {!isEdit && (
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
              )}
            </span>
          }
          name="password"
          rules={
            isEdit
              ? []
              : [
                  { required: true, message: text.pleaseInputPassword },
                  { min: 8, message: text.passwordMustBeNoLessThan8 },
                  { max: 100, message: text.passwordMaxLength },
                ]
          }
        >
          <Input.Password
            minLength={8}
            maxLength={100}
            size="large"
            disabled={isEdit} // disable edit password
            className="h-[44px]"
            showCount
          />
        </Form.Item>
        <Form.Item
          label={
            <span className="font-montserrat font-semibold">{text.role}</span>
          }
          name="role"
          rules={[{ required: true, message: text.pleaseSelectRole }]}
        >
          <Select
            size="large"
            options={Object.values(ADMIN_ROLE).map((item) => ({
              value: item,
              label: ADMIN_ROLE_MAP.get(item),
            }))}
          />
        </Form.Item>
        <Form.Item>
          <UiButton
            className="w-full"
            size={BUTTON_SIZES.LG}
            isDisabled={isPending}
            handleClick={form.submit}
            title={isEdit ? text.update : text.create}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}
