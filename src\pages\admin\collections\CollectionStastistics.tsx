import { useQuery } from '@tanstack/react-query'
import { StatisticsCard } from '@/components/card/StatisticsCard'
import { TextAdminTitle } from '@/components/texts/TextAdminTitle'
import { getCollectionStats } from '@/services/apiCall/collections'
import { getCookie } from '@/utils'
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES } from '@/constants'
import { IconDownload } from '@/components/icons'

export default function CollectionAccountStatistics() {
  const { data } = useQuery({
    queryKey: ['getCollectionStats'],
    queryFn: getCollectionStats,
    // enabled: false,
  })

  const statTitles = {
    totalCollections: 'コレクション数',
    totalWaitingForApproval: '承認待ち',
    totalRejected: '却下済み',
    totalDraft: 'ドラフト',
    totalApproved: '承認済',
  }

  const exportCsv = async () => {
    const token = getCookie('admin_access_token') || ''
    const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/v1/collections/export-csv/list-collections`

    const res = await fetch(url, {
      headers: {
        Authorization: token,
        'x-signature': process.env.NEXT_PUBLIC_GLOBAL_API_SIGNATURE || '',
      },
    })

    const csvText = await res.text()

    const bom = '\uFEFF'
    const blob = new Blob([bom + csvText], { type: 'text/csv;charset=utf-8;' })

    const blobUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = blobUrl
    link.setAttribute('download', 'dashboard-collections.csv')
    document.body.appendChild(link)
    link.click()
    link.remove()
  }

  return (
    <div>
      <div className="flex justify-between">
        <TextAdminTitle title="オーバービュー" />
        <UiButton
          title="CSV出力"
          isGradient={false}
          size={BUTTON_SIZES.FIT}
          className="!text-primary"
          handleClick={() => exportCsv()}
          icon={<IconDownload />}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2  [@media(min-width:1000px)]:grid-cols-3  [@media(min-width:1175px)]:grid-cols-5 gap-4">
        {data &&
          Object.keys(data).map((item) => (
            <StatisticsCard
              key={`${item}-${data[item]}`}
              title={statTitles[item]}
              stat={data[item]}
            />
          ))}
      </div>
    </div>
  )
}
