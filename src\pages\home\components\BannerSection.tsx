import React, { useRef } from 'react'
import { Carousel } from 'antd'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'
import Image from 'next/image'
import { useQuery } from '@tanstack/react-query'
import { PaginatedBanner } from '@/interfaces'
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES, ROUTES } from '@/constants'
import { useTranslate } from '@/hooks'
import { getBanners } from '@/services/apiCall'
import { useRouter } from 'next/router'

interface BannerSectionProps {
  className?: string
}

const BannerSection: React.FC<BannerSectionProps> = ({ className = '' }) => {
  const carouselRef = useRef<any>(null)
  const router = useRouter()
  const t = useTranslate()

  // Fetch banners using useQuery
  const { data: bannerData } = useQuery<PaginatedBanner>({
    queryKey: ['banners'],
    queryFn: () => getBanners({ pageSize: 10 }),
  })

  const banners = bannerData?.items || []

  return (
    <>
      {banners.length && (
        <section className={`relative min-h-[500px] ${className}`}>
          <div className="w-full relative bg-getBg">
            {banners.length > 1 && (
              <button
                className="absolute max-sm:hidden left-[-40px] top-1/2 -translate-y-1/2 z-10 p-2 text-white font-montserrat"
                onClick={() => carouselRef.current?.prev()}
              >
                <LeftOutlined rev={'true'} />
              </button>
            )}
            <Carousel
              ref={carouselRef}
              autoplay
              dots={{ className: 'custom-carousel-dots' }}
              arrows={true}
              autoplaySpeed={5000}
              effect="fade"
              className="bg-getBg"
            >
              {banners.map((banner, index) => (
                <div key={banner._id || index} className="relative h-[400px]">
                  <Image
                    src={banner.image}
                    alt={banner.description}
                    fill
                    className="object-cover rounded-[8px]"
                  />
                  <div className="absolute inset-0 md:p-16 sm:p-[50px] p-[35px] grid font-montserrat tracking-[0.02em] content-between">
                    <p className="font-montserrat font-semibold max-[390px]:text-3xl text-5xl leading-[120%] tracking-[0.02em] text-white line-clamp-2">
                      {banner.title}
                    </p>
                    <p className="font-montserrat font-medium text-sm leading-[170%] tracking-[0.02em] text-white line-clamp-5">
                      {banner.description}
                    </p>
                    <UiButton
                      className="max-[390px]:w-full"
                      title={t.banner_cta}
                      size={BUTTON_SIZES.LG}
                      handleClick={() =>
                        router.push(
                          banner.externalLink ||
                            `${ROUTES.marketBanner}/${banner._id || ''}`
                        )
                      }
                      isFilled={true}
                    />
                  </div>
                </div>
              ))}
            </Carousel>
            {banners.length > 1 && (
              <button
                className="absolute max-sm:hidden right-[-40px] top-1/2 -translate-y-1/2 z-10 p-2 text-white"
                onClick={() => carouselRef.current?.next()}
              >
                <RightOutlined rev={'true'} />
              </button>
            )}
          </div>
        </section>
      )}
    </>
  )
}

export default BannerSection
