import { MODAL_TYPES } from '@/components/rootModal'
import { RoleType } from './auth'
import { TYPE_NFT, TYPE_STATUS_NFT } from '@/constants/type'

export type ValueOf<T> = T[keyof T]

export type Address = `0x${string}`

export type ModalType = ValueOf<typeof MODAL_TYPES>

export type ModalState = {
  type: ModalType | null
  title?: string | null
  message?: string | string[]
  status?: string | number
  event?: (() => void) | undefined
}

export interface ICommonState {
  role: RoleType | null
  modalType: ModalType | null
  modalTitle: string | null
  message: string | string[] | null
  status: string | number | null
  isLoading: boolean
  canEdit: boolean
  canDelete: boolean
  canCreate: boolean
  canApprove: boolean
  setRole: (role: RoleType | null) => void
  setLoading: (type: boolean) => void
  setModalType: (data: ModalState) => void
}
export interface IIcon {
  width?: string | number
  height?: string | number
  color?: string
  className?: string
  onClick?: () => void
}

export interface IHeaderSearch {
  searchWord?: string
  itemType?: TYPE_NFT
  status?: TYPE_STATUS_NFT
}
