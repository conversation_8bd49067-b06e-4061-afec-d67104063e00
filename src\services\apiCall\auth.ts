import { LoginAdminParams, LoginParams } from '@/interfaces'
import { get, post } from './baseApi'
import { API_URLS } from '@/constants'

export const getAdminAccessToken = async (params: LoginAdminParams) => {
  try {
    return await post(API_URLS.adminLogin, params)
  } catch (error) {
    return error?.response?.data
  }
}

export const getUserAccessToken = async (params: LoginParams) => {
  try {
    return await post(API_URLS.userLogin, params)
  } catch (error) {
    return error
  }
}

export const getUserNonce = () => {
  try {
    return get(API_URLS.userNonce, {})
  } catch (error) {
    return error
  }
}
