# Connect Wallet Modal - UI Specification

This document describes the structure, components, and styles of the **Connect Wallet** modal.

## Overview

The modal prompts the user to agree to the Terms of Use and Privacy Policy before proceeding to connect their wallet.

---

## Components

### 1. <PERSON>er (Title)

- **Text:** `Connect wallet`
- **Position:** Top center.
- **Purpose:** Clearly states the purpose of the modal.

### 2. Close Button (Icon)

- **Icon:** "X" symbol.
- **Position:** Top-right corner.
- **Purpose:** Allows the user to close the modal without taking any action.

### 3. Description Text

- **Text:** `Please carefully read the following items before proceeding to connect your wallet.`
- **Position:** Below the header, centered.
- **Purpose:** Instructs the user to review terms before proceeding.

### 4. Checkbox with Terms Agreement

- **State:** Checked by default.
- **Label:** `I agree to the Terms of Use and Privacy Policy.`
  - **Terms of Use** and **Privacy Policy** are clickable links to their respective pages.
- **Purpose:** Ensures user consent before wallet connection.

### 5. Action Buttons

- **Cancel Button:**
  - Style: Dark background with light border, white text.
  - Purpose: Closes the modal and cancels the process.
- **Connect Button:**
  - Style: Yellow background with black text.
  - Purpose: Proceeds to connect the wallet.

### 6. Layout & Styling

- **Background:** Dark (black/charcoal) with rounded corners and drop shadow.
- **Text Color:** White, with links in yellow/orange for emphasis.
- **Primary Button (Connect):** Yellow background, black text for high visibility.
- **Secondary Button (Cancel):** Dark background, white text, bordered style.

---

## Suggested CSS Styles

```css
.modal {
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 24px;
  color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 400px;
  max-width: 90%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.close-icon {
  cursor: pointer;
  font-size: 1.2rem;
}

.modal-description {
  margin: 16px 0;
  text-align: center;
  font-size: 0.95rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.checkbox-container a {
  color: #f5c344;
  text-decoration: none;
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.button-cancel {
  background-color: transparent;
  border: 1px solid #ccc;
  color: #fff;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.button-connect {
  background-color: #f5c344;
  color: #000;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}
```

---

## Interaction Flow

1. User opens modal.
2. User checks/ensures the agreement checkbox is ticked.
3. User clicks **Connect** to proceed or **Cancel** to abort.
