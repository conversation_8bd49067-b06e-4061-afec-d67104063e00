import React from 'react'

interface ButtonComponentProps {
  beforeIcon?: JSX.Element
  afterIcon?: JSX.Element
  title: string | JSX.Element
  className?: string
  type?: 'primary' | 'secondary' | 'default' | 'link' | 'admin'
  onClick?: () => void
  disabled?: boolean
  typeSubmit?: 'button' | 'reset' | 'submit'
  style?: React.CSSProperties
}

const objButton = {
  primary: {
    className: 'bg-tailwindBrand1 text-tailwindNeutral2 ',
  },
  secondary: {
    className: 'bg-tailwindNeutral4 text-tailwindNeutral1 ',
  },
  default: {
    className: 'bg-transparent text-black border border-tailwindNeutral1',
  },
  link: {
    className: 'bg-tailwindNeutral1 text-tailwindNeutral2 min-w-[150px]',
  },
  admin: {
    className: 'bg-tailwindNeutral1 text-tailwindNeutral2',
  },
}

const objDisabled = {
  true: {
    className:
      'bg-tailwindNeutral6 text-tailwindNeutral3 cursor-not-allowed hover:opacity-100',
  },
  false: {
    className: '',
  },
}
const ButtonComponent = (props: ButtonComponentProps) => {
  return (
    <button
      type={props.typeSubmit}
      disabled={props.disabled}
      className={`getMarketplace-button
      ${objButton[props.type || 'default'].className} ${
        objDisabled[String(props.disabled)]?.className
      } ${props.className}`}
      style={props.style}
      onClick={props.onClick}
    >
      {props.beforeIcon}
      {props.title}
      {props.afterIcon}
    </button>
  )
}

export default ButtonComponent
