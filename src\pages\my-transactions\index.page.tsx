import React, { useState } from 'react'
import { useRouter } from 'next/router'
import { useQuery } from '@tanstack/react-query'
import { ArrowLeftOutlined } from '@ant-design/icons'

import { getUserTransactions } from '@/services/apiCall/transaction'
import { formatWithCurrency, shortenString } from '@/utils'
import { useTranslate } from '@/hooks'
import moment from 'moment'
import { ROUTES, SCAN_URL } from '@/constants'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'
import AdminTable from '@/components/table/AdminTable'
import { UiPagination } from '@/components/ui/Pagination'
import Head from 'next/head'

const MyTransactionsPage = () => {
  const router = useRouter()
  const trans = useTranslate()
  const [cookies] = useCookies([
    STORAGEKEY.USER_ACCESS_TOKEN,
    STORAGEKEY.WALLET_ADDRESS,
  ])

  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const transactionColumns = [
    {
      title: trans.timestamp,
      dataIndex: 'boughtAt',
      key: 'timestamp',
      className: 'text-white',
      render: (boughtAt: number) =>
        moment(new Date(boughtAt)).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: trans.transaction_hash,
      dataIndex: 'transactionHash',
      key: 'transactionHash',
      className: 'text-white',
      render: (transactionHash: string) => {
        return (
          <a
            className="hover:text-primaryHover"
            href={SCAN_URL.URL.replace('{tx}', transactionHash)}
            target="_blank"
            rel="noreferrer"
          >
            {transactionHash ? shortenString(transactionHash, 10, 8) : '-'}
          </a>
        )
      },
    },
    {
      title: trans.token_id,
      dataIndex: 'tokenId',
      key: 'tokenId',
      className: 'text-white',
      render: (tokenId: string) => (tokenId ? `#${tokenId}` : '-'),
    },
    {
      title: trans.buy_price,
      dataIndex: 'price',
      key: 'price',
      className: 'text-white',
      render: (price: number) => formatWithCurrency(price),
    },
  ]

  // Query for user transactions with pagination
  const {
    data: userTransactions,
    isLoading: transactionsLoading,
    error: transactionsError,
  } = useQuery({
    queryKey: [
      'userTransactions',
      cookies[STORAGEKEY.WALLET_ADDRESS],
      currentPage,
      pageSize,
    ],
    queryFn: () =>
      getUserTransactions({
        pageIndex: currentPage,
        pageSize: pageSize,
      }),
    enabled: !!cookies[STORAGEKEY.USER_ACCESS_TOKEN],
  })

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page)
    if (size && size !== pageSize) {
      setPageSize(size)
    }
  }

  const handleBackToProfile = () => {
    router.push(ROUTES.myProfile)
  }

  if (!cookies[STORAGEKEY.WALLET_ADDRESS]) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-white text-center">
          <p className="text-lg mb-4">{trans.please_connect_wallet}</p>
          <button
            onClick={handleBackToProfile}
            className="text-primary hover:text-primaryHover flex items-center gap-2 mx-auto"
          >
            <ArrowLeftOutlined rev="icon" />
            <span>{trans.back_to_profile}</span>
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{trans.purchase_history}</title>
      </Head>
      <div className="container mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="font-montserrat font-semibold text-3xl tracking-[0.02em] leading-[170%] text-white">
            {trans.purchase_history}
          </h1>
        </div>

        {/* Transaction Table */}
        <div className="mb-6 overflow-x-auto">
          <AdminTable
            columns={transactionColumns}
            dataSource={userTransactions?.items || []}
            pagination={false}
            loading={transactionsLoading}
            rowKey="_id"
            locale={{
              emptyText: transactionsError
                ? trans.failed_to_load_transactions
                : trans.no_transactions_found,
            }}
          />
        </div>

        {/* Pagination */}
        {userTransactions && userTransactions.totalItems > 0 && (
          <div className="flex justify-center">
            <UiPagination
              current={currentPage}
              pageSize={pageSize}
              total={userTransactions.totalItems}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper={true}
            />
          </div>
        )}
      </div>
    </>
  )
}

export default MyTransactionsPage
