import React, { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Empty } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import {
  DEFAULT_PAGE_SIZE,
  ETYPE_TIME_OPTION,
  pageConfig,
  SCAN_URL,
} from '@/constants'
import {
  getTransactionStatistics,
  getListTransaction,
} from '@/services/apiCall/transaction'
import { Transaction, TransactionStats, ListTransaction } from '@/interfaces'
import { StatisticsCard } from '@/components/card/StatisticsCard'
import { useTimeSelectBar } from '@/components/time-select-bar'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'
import moment from 'moment'
import { formatPrice } from '@/utils'
import AdminTable from '@/components/table/AdminTable'

type TransactionTableType = Transaction & { key: string }

export default function Transactions() {
  const [pageIndex, setPageIndex] = useState(1)
  const defaultTimeOption = ETYPE_TIME_OPTION.lastWeek
  const { timeOption, TimeSelectBar } = useTimeSelectBar(defaultTimeOption)

  // Fetch statistics
  const { data: stats, isLoading: statsLoading } = useQuery<TransactionStats>({
    queryKey: ['transaction-stats', timeOption],
    queryFn: () => getTransactionStatistics(timeOption || defaultTimeOption),
  })

  // Fetch paginated transactions
  const { data: transactionList, isLoading: txLoading } =
    useQuery<ListTransaction>({
      queryKey: ['transactions', timeOption, pageIndex],
      queryFn: () =>
        getListTransaction({
          pageIndex,
          pageSize: DEFAULT_PAGE_SIZE,
          timeOption,
        }),
      // keepPreviousData: true,
    })

  // Table columns
  const columns: ColumnsType<TransactionTableType> = [
    {
      title: 'タイムスタンプ',
      dataIndex: 'boughtAt',
      render: (boughtAt: number) => (
        <span>{moment(boughtAt).format('YYYY-MM-DD hh:mm:ss')}</span>
      ),
    },
    {
      title: 'ハッシュコード',
      dataIndex: 'transactionHash',
      render: (tx = '') => (
        <span title={tx}>
          <a
            className="hover:text-primaryHover"
            href={SCAN_URL.URL.replace('{tx}', tx)}
            target="_blank"
            rel="noreferrer"
          >
            {tx.slice(0, 6)}...{tx.slice(-4)}
          </a>
        </span>
      ),
    },
    {
      title: 'トークンID',
      dataIndex: 'tokenId',
      render: (tokenId: string) => <span className="font-bold">{tokenId}</span>,
    },
    {
      title: '購入者ウォレット',
      dataIndex: 'buyerEvmAddress',
      render: (addr: string) => (
        <span title={addr}>
          {addr?.slice(0, 6)}...{addr?.slice(-4)}
        </span>
      ),
    },
    {
      title: '販売価格',
      dataIndex: 'price',
      align: 'right',
      render: (price: number) => (
        <span>{`${formatPrice(price, 4)}`} GET PAY</span>
      ),
    },
  ]

  // Table data
  const tableData: TransactionTableType[] = useMemo(
    () => (transactionList?.items || []).map((tx) => ({ ...tx, key: tx._id })),
    [transactionList]
  )

  const statisticsTitles = useMemo(
    () =>
      new Map<keyof TransactionStats, string>([
        ['totalTransactions', '総取引数'],
        ['totalVolume', '総取引量'],
        ['averageSalePrice', '平均販売価格'],
      ]),
    []
  )

  const RenderStaticText = React.memo(({ text }: { text: string }) => {
    const [num, ...textRender] = text.split(' ')
    return (
      <>
        {num} <span className={`text-sm ml-2`}>{textRender.join(' ')}</span>
      </>
    )
  })

  //Set page index to 1 when time option changes
  useEffect(() => {
    setPageIndex(1)
  }, [timeOption])

  return (
    <div>
      <TextAdminHeader title="取引管理" />
      <TimeSelectBar />
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {Array.from(statisticsTitles.entries()).map(([key, title]) => {
          const value = stats?.[key]

          return (
            <StatisticsCard
              key={key}
              title={title}
              stat={
                statsLoading ? (
                  '...'
                ) : typeof value === 'number' ? (
                  ['totalVolume', 'averageSalePrice'].includes(key) ? (
                    <RenderStaticText
                      text={`${formatPrice(value, 4)} GET PAY`}
                    />
                  ) : (
                    stats?.[key]
                  )
                ) : (
                  '-'
                )
              }
            />
          )
        })}
      </div>
      {/* Recent Transactions Table */}

      <AdminTable
        className="custom-table"
        columns={columns}
        dataSource={tableData}
        loading={txLoading}
        pagination={{
          total: transactionList?.totalItems || 0,
          current: pageIndex,
          pageSize: DEFAULT_PAGE_SIZE,
          showSizeChanger: false,
          position: ['bottomCenter'],
          onChange: (page) => setPageIndex(page),
        }}
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={pageConfig.noData}
            />
          ),
        }}
        rowKey="_id"
        scroll={{ x: 'max' }}
      />
    </div>
  )
}
