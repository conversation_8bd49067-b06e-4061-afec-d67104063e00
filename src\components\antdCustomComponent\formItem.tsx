import { Controller } from 'react-hook-form'
import { Form as AntdForm } from 'antd'
import React from 'react'

const AntdFormItem = ({ name, children, control, ...props }) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <AntdForm.Item
          validateStatus={fieldState.invalid ? 'error' : ''}
          help={fieldState.error?.message}
          {...props}
        >
          {React.cloneElement(children, {
            ...field,
            value: field.value ?? '',
            onChange: field.onChange,
            onBlur: field.onBlur,
          })}
        </AntdForm.Item>
      )}
    />
  )
}

export default AntdFormItem
