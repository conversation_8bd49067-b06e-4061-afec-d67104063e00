import React from 'react'
import { Form, Input, Row, Col } from 'antd'
import UiButton from '@/components/ui/Button'
import UploadImage from '@/components/uploadImages/UploadImages'
import { BUTTON_SIZES } from '@/constants/enum'
import TextEditor from '@/components/text-editor/TextEditor'
import { APPROVAL_STATUS } from '@/constants'

const TEXT_LABELS = {
  CREATE_NEWS: 'ニュース作成',
  TITLE: 'タイトル',
  THUMBNAIL_IMAGE: 'サムネイル画像',
  CONTEXT: '内容',
  CANCEL: 'キャンセル',
  SAVE_DRAFT: 'ドラフトを保存',
  PUBLISH: '公開',
  PLEASE_ENTER_TITLE: 'タイトルを入力してください',
  PLEASE_ENTER_CONTEXT: '内容を入力してください',
  TITLE_MUST_NOT_EXCEED: 'タイトルは150文字以内で入力してください',
}

export default function NewsForm({
  form,
  loading,
  imageFile,
  setImageFile,
  editorValue,
  setEditorValue,
  onFinish,
  onSaveDraft,
  imageUrl = '',
  setImageUrl,
  disabled = false,
  canApprove = true,
  canCreate = true,
  approvalStatus,
  saveButtonLabel,
  publishButtonLabel,
}: {
  form: any
  loading: boolean
  imageFile: File | null
  setImageFile: (file: File | null) => void
  editorValue: string
  setEditorValue: (value: string) => void
  onFinish: (values: any) => void
  onSaveDraft: () => void
  imageUrl?: string
  setImageUrl?: (url: string) => void
  disabled?: boolean
  canApprove?: boolean
  canCreate?: boolean
  saveButtonLabel?: string
  publishButtonLabel?: string
  approvalStatus?: APPROVAL_STATUS
}) {
  return (
    <Row className="justify-around">
      <Col xs={24} md={10}>
        <UploadImage
          width="w-full"
          height="h-1/2"
          label={TEXT_LABELS.THUMBNAIL_IMAGE}
          required
          recommendedSize="1920 x 1080"
          value={imageFile}
          imageUrl={imageUrl}
          setImageUrl={setImageUrl}
          maxSize={2}
          onChange={setImageFile}
          disabled={disabled}
        />
      </Col>
      <Col xs={24} md={12}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="flex flex-row gap-8 w-full text-white"
        >
          <div className="flex flex-col flex-1 gap-6">
            <Form.Item
              name="title"
              label={TEXT_LABELS.TITLE}
              rules={[
                { required: true, message: TEXT_LABELS.PLEASE_ENTER_TITLE },
                { max: 150, message: TEXT_LABELS.TITLE_MUST_NOT_EXCEED },
              ]}
              className="mb-2"
            >
              <Input
                size="large"
                placeholder="タイトル"
                disabled={disabled}
                maxLength={150}
                showCount
              />
            </Form.Item>
            <Form.Item
              label={TEXT_LABELS.CONTEXT}
              name="context"
              rules={[
                { required: true, message: TEXT_LABELS.PLEASE_ENTER_CONTEXT },
              ]}
            >
              <TextEditor
                value={editorValue}
                onChange={(value) => {
                  if (!disabled) {
                    setEditorValue(value)
                    form.setFieldsValue({ context: value })
                  }
                }}
                editable={!disabled}
              />
            </Form.Item>
            {((canCreate && !disabled) ||
              (canApprove && approvalStatus === APPROVAL_STATUS.waiting)) && (
              <div className="flex flex-col gap-3">
                <UiButton
                  title={saveButtonLabel || TEXT_LABELS.SAVE_DRAFT}
                  size={BUTTON_SIZES.LG}
                  handleClick={onSaveDraft}
                  className="w-full"
                  isDisabled={loading}
                  isGradient={false}
                  isBorder={true}
                />
                <UiButton
                  title={publishButtonLabel || TEXT_LABELS.PUBLISH}
                  size={BUTTON_SIZES.LG}
                  isGradient={true}
                  handleClick={() => form.submit()}
                  className="w-full"
                  isLoading={loading}
                  isDisabled={loading}
                />
              </div>
            )}
          </div>
        </Form>
      </Col>
    </Row>
  )
}
