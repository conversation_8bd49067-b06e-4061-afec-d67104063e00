import { useCallback, useEffect, useMemo, useState } from 'react'
import { SignClient } from '@walletconnect/sign-client'
import { SignClient as SignClientType } from '@walletconnect/sign-client/dist/types/client'
import { STORAGEKEY } from '@/services/cookies'
import { useCookies } from 'react-cookie'
import { LoginParams } from '@/interfaces'
import { getUserAccessToken } from '@/services/apiCall'

const useMobileConnect = ({ isMobile }: { isMobile: boolean }) => {
  const [cookies, setCookie, removeCookie] = useCookies([
    STORAGEKEY.CONNECT_SESSION,
    STORAGEKEY.CONNECT_CLIENT,
    STORAGEKEY.CONNECT_TOPIC,
    STORAGEKEY.WALLET_ADDRESS,
    STORAGEKEY.USER_ACCESS_TOKEN,
    STORAGEKEY.USER,
  ])
  const [client, setClient] = useState<SignClientType>({} as SignClientType)
  const [session, setSession] = useState<any>(
    cookies[STORAGEKEY.CONNECT_SESSION] || ''
  )
  const [topic, setTopic] = useState<string>(
    cookies[STORAGEKEY.CONNECT_TOPIC] || ''
  )
  const getProjectId = useCallback((): string => {
    if (process.env.NEXT_PUBLIC_PROJECT_ID) {
      return process.env.NEXT_PUBLIC_PROJECT_ID as string
    }
    return ''
  }, [])
  const [isListen, setIsListen] = useState<boolean>(false)
  const isConnected = useMemo<boolean>(() => {
    return !!cookies[STORAGEKEY.USER_ACCESS_TOKEN] as boolean
  }, [cookies[STORAGEKEY.USER_ACCESS_TOKEN]])

  const handleSetSession = (session: any) => {
    setSession(session.uri)
    setCookie(STORAGEKEY.CONNECT_SESSION, session.uri)
    if (!topic && isConnected) {
      clearStore()
    }
  }

  const createSession = useCallback(
    async (client: any) => {
      if (Object.keys(client)) {
        const session = await client.connect({
          requiredNamespaces: {
            cardano: {
              chains: [
                process.env.NEXT_PUBLIC_NODE_ENV === 'production'
                  ? 'cardano:mainnet'
                  : 'cardano:preprod',
              ],
              methods: ['cbor_signTx'],
              events: [],
            },
          },
        })
        handleSetSession(session)
      }
    },
    [client]
  )

  useEffect(() => {
    const initClient = async () => {
      const client = await SignClient.init({
        projectId: getProjectId(),
        metadata: {
          name: process.env.NEXT_PUBLIC_APP_NAME ?? '',
          description: `Connect wallet to ${process.env.NEXT_PUBLIC_APP_NAME}`,
          url: process.env.NEXT_PUBLIC_APP_URL ?? '',
          icons: [`${process.env.NEXT_PUBLIC_APP_URL}/img/icons/logo.svg`],
        },
      })
      setClient(client)
      createSession(client)
    }
    isMobile &&
      (!client || !Object.keys(client).length) &&
      initClient &&
      initClient()
  }, [])

  const connectWalletMobile = useCallback(() => {
    const uri = session
    const deepLink = `${process.env.NEXT_PUBLIC_DEEP_LINK_APP}?uri=${uri}`
    window.open(deepLink, '_blank')
  }, [client, session])

  const clearStore = () => {
    removeCookie(STORAGEKEY.WALLET_ADDRESS)
    removeCookie(STORAGEKEY.USER_ACCESS_TOKEN)
    removeCookie(STORAGEKEY.USER)
    removeCookie(STORAGEKEY.CONNECT_TOPIC)
    removeCookie(STORAGEKEY.CONNECT_CLIENT)
    removeCookie(STORAGEKEY.CONNECT_SESSION)
  }

  const setCookieUserInfo = useCallback(
    (dataAccessCookie: any, walletAddress: string) => {
      setCookie(
        STORAGEKEY.WALLET_ADDRESS,
        walletAddress,
        dataAccessCookie.accessTokenExp
      )
      setCookie(
        STORAGEKEY.USER_ACCESS_TOKEN,
        dataAccessCookie.accessToken,
        dataAccessCookie.accessTokenExp
      )
      setCookie(
        STORAGEKEY.USER,
        dataAccessCookie?.user,
        dataAccessCookie.accessTokenExp
      )
    },
    []
  )

  const handleLogin = async (loginParams: LoginParams) => {
    try {
      const data = await getUserAccessToken(loginParams)
      if (data.accessToken) {
        setCookieUserInfo(data, loginParams.walletAddress)
      } else {
        clearStore()
      }
    } catch (error) {
      console.error('Error during wallet authentication:', error)
      clearStore()
    }
  }

  useEffect(() => {
    if (!client || !Object.keys(client).length || !session || isListen) return
    setIsListen(true)
    client?.on('session_connect', async (event) => {
      const topicIdConnect = (event.session as any)?.topic
      setTopic(topicIdConnect)
      setCookie(STORAGEKEY.CONNECT_TOPIC, topicIdConnect)
      const cardano = event.session.namespaces.cardano
      const ethereum = event.session.namespaces.ethereum
      const address = cardano.accounts[0]?.replace(
        process.env.NEXT_PUBLIC_NODE_ENV === 'production'
          ? 'cardano:mainnet:'
          : 'cardano:preprod:',
        ''
      )
      const [ethWallet] = ethereum.accounts
      const [, , addressEVM] = ethWallet.split(':')
      const metadata = (cardano as any)?.metadata
      const key = metadata?.signature?.key
      const signature = metadata?.signature?.signature
      const nonce = metadata?.nonce

      const loginParams: LoginParams = {
        walletAddress: address,
        signature,
        message: key, // Using nonce as the message
        nonce,
        otherWallet: addressEVM,
      }
      handleLogin(loginParams)
    })

    client.on('session_delete', (event) => {
      const topicIdDelete = (event as any)?.topic
      if (topic === topicIdDelete) {
        handleLogout(true)
      }
    })
  }, [client, topic, session, isListen])

  const handleLogout = useCallback(
    async (isClearOnly: boolean) => {
      if (Object.keys(client).length && topic) {
        if (!isClearOnly) {
          await client?.disconnect({
            topic: topic.toString(),
            reason: {
              code: 6000,
              message: 'User logged out',
            },
          })
        }
        setTopic('')
        clearStore()
        setTimeout(() => window.location.reload(), 1500)
      }
    },
    [client, topic]
  )

  return { connectWalletMobile, handleLogout }
}

export default useMobileConnect
