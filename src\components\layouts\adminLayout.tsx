import { Layout } from 'antd'
import SidebarAdmin from './sidebarAdmin'
import { useState, useLayoutEffect, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Dropdown } from 'antd'
import { ROUTES, ROLE, ADMIN_PROTECTED_ROUTES } from '@/constants'
import { logout } from '@/utils/auth'
import { LogoutIcon, IconChevronDown, IconAccount } from '@/icons'
import { useCookies } from 'react-cookie'

const { Sider, Header, Content } = Layout

const AdminLayout = ({ children }: { children: React.ReactElement }) => {
  const [email, setEmail] = useState<string>('')
  const [isDropdownVisible, setIsDropdownVisible] = useState(false)
  const router = useRouter()
  const [{ admin }] = useCookies()

  useLayoutEffect(() => {
    if (admin?.email) {
      setEmail(admin?.email)
    }

    // allow all admin to access if route is not protected
    if (!ADMIN_PROTECTED_ROUTES.has(router.pathname)) return

    const authorizedRoles = ADMIN_PROTECTED_ROUTES.get(router.pathname)
    // redirect to dashboard if not systemAdmin or not in authorizedRoles
    if (
      admin?.role !== ROLE.systemAdmin &&
      !authorizedRoles?.includes(admin?.role as ROLE)
    ) {
      router.push(ROUTES.adminDashboard)
    }
  }, [])

  useEffect(() => {
    // redirect to login if logged out in other tabs
    if (!admin) {
      router.push(ROUTES.adminLogin)
    }
  }, [admin])

  const handleLogout = () => {
    logout(ROLE.systemAdmin)
    router.push(ROUTES.adminLogin)
  }

  const menuItems = [
    {
      key: 'logout',
      icon: <LogoutIcon />,
      label: 'ログアウト',
      onClick: handleLogout,
    },
  ]
  return (
    <Layout className="overflow-hidden">
      <Sider
        className="overflow-hidden h-[100vh] fixed [&>div]:fixed"
        width="var(--width-sidebar-admin)"
      >
        <SidebarAdmin />
      </Sider>
      <Layout>
        <Header className="bg-[var(--bg-color)] text-white h-[var(--height-header)] flex flex-wrap justify-between items-center !px-3">
          <p className="font-montserrat not-italic font-semibold text-[16px] leading-[170%] tracking-[0.1em]">
            Admin Hub
          </p>
          <div className="flex items-center gap-2">
            <Dropdown
              menu={{ items: menuItems }}
              trigger={['click']}
              open={isDropdownVisible}
              onOpenChange={setIsDropdownVisible}
              placement="bottomRight"
            >
              <div className="flex items-center gap-2 cursor-pointer">
                <div className="w-8 h-8 rounded-full flex items-center justify-center">
                  <IconAccount width={32} height={32} />
                </div>
                <span className="text-sm font-medium">
                  {email || '<EMAIL>'}
                </span>
                <div className="-ml-1 pt-2">
                  <IconChevronDown color="white" />
                </div>
              </div>
            </Dropdown>
          </div>
        </Header>
        <Content className="bg-[var(--bg-color)]">
          <div className="px-12 overflow-auto">{children}</div>
        </Content>
      </Layout>
    </Layout>
  )
}
export default AdminLayout
