// components/nft/StatisticNfts.tsx
import { StatisticsCard } from '@/components/card'
import {
  DollarCircleOutlined,
  LineChartOutlined,
  TableOutlined,
} from '@ant-design/icons'
import { useTranslate } from '@/hooks/useTranslate'
import { COLLECTION_DETAIL_CONFIG } from '@/constants/collection-detail'
import { formatWithCurrencyRound } from '@/utils'
import React from 'react'
import { ROUTES } from '@/constants'

type Props = {
  collection: {
    floorPrice?: number
    totalVolume?: number
    totalApprovedNfts?: number
  }
  className?: string
}

export default function Statistics({ collection, className }: Props) {
  const t = useTranslate()

  const RenderStaticText = React.memo(({ text }: { text: string }) => {
    const [num, ...textRender] = text.split(' ')
    return (
      <>
        {num} <span className={`text-sm ml-2`}>{textRender.join(' ')}</span>
      </>
    )
  })

  const isAdmin = window.location.pathname.startsWith(ROUTES.admin)

  const statisticsData = [
    {
      title: isAdmin ? 'フロア価格' : t[COLLECTION_DETAIL_CONFIG.floorPrice],
      stat: (
        <RenderStaticText
          text={formatWithCurrencyRound(
            Number(collection?.floorPrice || 0)
          ).toString()}
        />
      ),
      icon: <DollarCircleOutlined rev={undefined} />,
    },
    {
      title: isAdmin ? '総取引量' : t[COLLECTION_DETAIL_CONFIG.totalVolume],
      stat: (
        <RenderStaticText
          text={formatWithCurrencyRound(Number(collection?.totalVolume || 0))}
        />
      ),
      icon: <LineChartOutlined rev={undefined} />,
    },
    {
      title: isAdmin ? 'アイテム' : t[COLLECTION_DETAIL_CONFIG.items],
      stat: collection.totalApprovedNfts || 0,
      icon: <TableOutlined rev={undefined} />,
    },
  ]

  return (
    <div className="max-w-screen-xl grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-10 sm:mb-20">
      {statisticsData.map((item, index) => (
        <StatisticsCard
          className={`${className} bg-getCardBg`}
          key={index}
          title={item.title}
          stat={item.stat}
          icon={item.icon}
        />
      ))}
    </div>
  )
}
