import { API_URLS, ROLE } from '../../constants'
import {
  AdminAccountStats,
  PaginatedAdmin,
  AdminSearch,
  CreateAdminBody,
} from '../../interfaces'
import { get, patch, post } from './baseApi'

export const getAdminAccountStats = (): Promise<AdminAccountStats> => {
  return get(API_URLS.adminAccountStats, {}, ROLE.systemAdmin)
}

export const getListAdmins = (query: AdminSearch): Promise<PaginatedAdmin> => {
  return get(API_URLS.adminList, query, ROLE.systemAdmin)
}

export const enableAdmin = (id: string): Promise<void> => {
  return post(
    API_URLS.enableAdmin.replace('{id}', id),
    {},
    {},
    ROLE.systemAdmin
  )
}

export const disableAdmin = (id: string): Promise<void> => {
  return post(
    API_URLS.disableAdmin.replace('{id}', id),
    {},
    {},
    ROLE.systemAdmin
  )
}

export const createAdmin = (admin: CreateAdminBody): Promise<void> => {
  return post(API_URLS.createAdmin, admin, {}, ROLE.systemAdmin)
}

export const editAdmin = (
  id: string,
  admin: Partial<CreateAdminBody>
): Promise<void> => {
  return patch(API_URLS.editAdmin.replace('{id}', id), admin, ROLE.systemAdmin)
}
