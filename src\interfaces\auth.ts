import { ROLE } from '@/constants'
import { ValueOf } from './common'

export type RoleType = ValueOf<typeof ROLE>

export type LoginParams = {
  walletAddress: string
  signature: string
  message: string
  nonce?: string
  otherWallet: string
}

export type LoginAdminParams = {
  email: string | number
  password: string | number
}

export interface IErrorResponse {
  error: string
  message: string
  statusCode: number
}
