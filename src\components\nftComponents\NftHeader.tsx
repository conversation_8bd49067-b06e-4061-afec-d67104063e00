import { Select } from 'antd'
import clsx from 'clsx'
import { useState, KeyboardEvent } from 'react'

import { Search } from '../common'
import { NftHeaderPropsType } from '@/interfaces'
import { sortValues, typeValuesNft } from '@/constants'
import { formatNumberWithComma } from '@/utils'
import { useRouter } from 'next/router'

function NftHeader(props: NftHeaderPropsType) {
  const { total, search, setSearch } = props
  const { query, push } = useRouter()
  const [searchValue, setSearchValue] = useState(search?.searchWord || '')

  const handleSearch = (key: string, value: number | string | null) => {
    const newSearch = {
      ...search,
      [key]: value,
      // reset to page 1
      pageIndex: 1,
    }
    setSearch(newSearch)
    if (value) {
      push({ query: { ...query, ...newSearch, [key]: value } })
    } else {
      // eslint-disable-next-line
      const { [key]: queryDeleted, ...rest } = query
      push({ query: { ...rest } })
    }
  }

  const handleClearSearch = () => {
    // eslint-disable-next-line
    const { pageIndex, searchWord, ...rest } = query
    push({ query: { ...rest } })
    setSearchValue('')
  }

  const handleSearchInputEnterSubmit = (
    event: KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key !== 'Enter') return
    const newSearch = {
      ...search,
      searchWord: searchValue,
      // reset to page 1
      pageIndex: 1,
    }
    setSearch(newSearch)
    // eslint-disable-next-line
    const { pageIndex, ...rest } = query
    push({ query: { ...rest, searchWord: searchValue } })
  }

  return (
    <div
      className={`flex lg:items-center lg:flex-row lg:max-w-[1260px] lg:w-full flex-col justify-between gap-4 xl:gap-8 max-sm:max-w-[470px] w-full sm:w-fit ${props.className}`}
    >
      <div className="flex items-center justify-center gap-2 ">
        {typeValuesNft.map((sort) => {
          const isActiveType = sort.value === search.itemType
          return (
            <div
              onClick={() => handleSearch('itemType', sort.value)}
              key={sort.value}
              className={clsx(
                'min-w-[40px] p-[2px] text-center cursor-pointer rounded',
                isActiveType
                  ? 'bg-tailwindBrand1 text-white font-bold'
                  : 'text-black hover:text-tailwindBrand1'
              )}
            >
              {sort.name}
            </div>
          )
        })}
      </div>
      <div className="">
        <Search
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          onKeyDown={(e) => handleSearchInputEnterSubmit(e)}
          placeholder="NFTのタイトル、発行者名で検索する。"
          className="border border-solid border-tailwindNeutral3 rounded-md lg:w-[28vw] xl:w-[22vw] max-w-[700px]"
          handleClearAll={handleClearSearch}
        />
      </div>
      <div className="lg:w-[250px] flex items-center justify-end gap-2 max-lg:justify-between">
        <Select
          value={search.sortCondition}
          className="lg:w-32 w-[60vw] max-w-[200px] h-10 text-tailwindNeutral1 border-tailwindNeutral3"
          onChange={(value) => handleSearch('sortCondition', String(value))}
          options={sortValues}
        />
        <div>{formatNumberWithComma(total) || 0} 件</div>
      </div>
    </div>
  )
}

export default NftHeader
