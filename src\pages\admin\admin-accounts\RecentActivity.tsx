import React from 'react'
import Link from 'next/link'
import { ListActivityItem } from '../../../components/list-item/ListActivityItem'
import { TextAdminTitle } from '../../../components/texts/TextAdminTitle'
import { IconChevronDown } from '../../../icons'
import { PaginatedActivity } from '../../../interfaces/activity'
import { getListActivity } from '../../../services/apiCall'
import { getActivityContent } from '@/utils/activity'
import { DEFAULT_PAGE_SIZE, ROUTES } from '@/constants'
import { useQuery } from '@tanstack/react-query'

export default function RecentActivity() {
  const { data, isLoading, isError } = useQuery<PaginatedActivity>({
    queryKey: ['admin-activity-recent', 1, DEFAULT_PAGE_SIZE],
    queryFn: () =>
      getListActivity({ pageSize: DEFAULT_PAGE_SIZE, pageIndex: 1 }),
  })
  const MAX_ACTIVITY_VISIBLE = 2

  const text = {
    adminAccountManagement: 'アカウント管理',
    allActivity: 'すべてのアクティビティ',
    failedToGetActivity: 'アクティビティの取得に失敗しました',
    noActivity: 'アクティビティがありません',
    showMore: 'もっと見る',
  }

  return (
    <div>
      <TextAdminTitle title={text.adminAccountManagement} />
      <div className="bg-[var(--bg-card)] rounded-[8px] py-3 px-5">
        {isLoading ? (
          <div className="text-center py-4">Loading...</div>
        ) : isError ? (
          <div className="text-center py-4">{text.failedToGetActivity}</div>
        ) : !data || !data.items || data.items.length === 0 ? (
          <div className="text-center py-4">{text.noActivity}</div>
        ) : (
          data.items
            .slice(0, MAX_ACTIVITY_VISIBLE)
            .map((item) => (
              <ListActivityItem
                key={item._id}
                title={getActivityContent(item) || ''}
                createdAt={item.createdAt}
              />
            ))
        )}
        {data && data.items.length > MAX_ACTIVITY_VISIBLE && (
          <div className="flex justify-center">
            <Link
              href={ROUTES.adminActivity}
              className="text-primary flex hover:text-primaryHover"
            >
              <IconChevronDown />
              <p>{text.showMore}</p>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
