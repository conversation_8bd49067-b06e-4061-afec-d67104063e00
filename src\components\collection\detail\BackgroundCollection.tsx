import StandardBadge from '@/components/badge/StandardBadge'
import Image from 'next/image'

type Props = {
  backgroundImage: string
  collectionIcon?: string
  name: string
  standard: string
  description: string
  className?: string
}

export default function BackgroundCollection({
  backgroundImage,
  collectionIcon,
  name,
  standard,
  description,
  className = '',
}: Props) {
  return (
    <div
      className={`max-w-screen-xl relative xl:w-full min-h-[350px] my-8 ${className}`}
    >
      <Image
        src={backgroundImage}
        alt="Background Decoration"
        className="h-full object-cover pointer-events-none rounded-lg"
        priority
        fill
      />

      <div className="relative z-10 sm:p-[50px] p-[35px]">
        <div className="flex items-center gap-2">
          <h1 className="font-semibold text-[48px] leading-[120%] font-montserrat text-white tracking-[0.02em]">
            {name}
          </h1>
          <StandardBadge standard={standard} />
        </div>

        <pre className="mt-5 text-wrap ellipsis-text not-italic font-medium text-[14px] leading-[170%] text-white">
          {description}
        </pre>
      </div>
      {collectionIcon && (
        <div className="absolute w-full flex justify-start bottom-3 px-12 pt-[35px] overflow-hidden">
          <div className="relative w-16 h-16">
            <Image
              src={collectionIcon}
              alt="Collection Icon"
              fill
              className="object-fill inset-shadow-border rounded cursor-not-allowed border border-gray-400"
              priority
            />
          </div>
        </div>
      )}
    </div>
  )
}
