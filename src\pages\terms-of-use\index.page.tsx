import { StaticPageCard } from '@/components/card'
import { StaticPageLayout } from '@/components/layouts/staticPageLayout'
import { getCurrentLanguage } from '@/utils'
import { IMAGE } from '@/utils/string'
import { TermsOfUseEn } from './TermsOfUseEn'
import { TermsOfUseJa } from './TermsOfUseJa'

function TermsOfUse() {
  const lang = getCurrentLanguage()
  const title =
    lang === 'en' ? 'Glitters Terms of Service' : 'グリッターズ利用規約 '

  return (
    <StaticPageLayout title={title}>
      <StaticPageCard
        title={title}
        imageUrl={IMAGE.whatIsMinting}
        createdAt="2025-08-29"
      >
        {lang === 'en' ? <TermsOfUseEn /> : <TermsOfUseJa />}
      </StaticPageCard>
    </StaticPageLayout>
  )
}

export default TermsOfUse
