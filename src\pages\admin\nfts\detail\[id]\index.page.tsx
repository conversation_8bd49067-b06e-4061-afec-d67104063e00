import { useEffect } from 'react'
import { toast } from 'react-toastify'
import { useRouter } from 'next/router'
import { useQuery } from '@tanstack/react-query'

import FormNft from '../../FormNFT'
import { getNftDetailForAdmin } from '@/services/apiCall/nft'
import { MESSAGE_FORM } from '@/utils/string'

const MyNFTEdit = () => {
  const { query } = useRouter()
  const id = query.id as string

  const { data: dataTable, isError } = useQuery({
    queryKey: ['admin-nft-detail', id],
    queryFn: () => (id ? getNftDetailForAdmin(id) : Promise.resolve(undefined)),
    enabled: !!id,
  })

  useEffect(() => {
    if (isError) {
      toast.error(MESSAGE_FORM.errorCallServer)
    }
  }, [isError])

  return (
    <div>
      <FormNft data={dataTable} mode="view" />
    </div>
  )
}

export default MyNFTEdit
