export enum ACCOUNT_STATUS {
  active = 'active',
  blocked = 'blocked',
}

export const ACCOUNT_STATUS_MAP = new Map<string, string>([
  [ACCOUNT_STATUS.active, '有効'],
  [ACCOUNT_STATUS.blocked, '無効'],
])

export enum USER_ROLE {
  commonUser = 'commonUser',
  approver = '承認者',
  operator = '担当者',
  systemAdmin = '管理者',
}

export enum ADMIN_ROLE {
  approver = 'approver',
  operator = 'operator',
  systemAdmin = 'systemAdmin',
}

export const ADMIN_ROLE_MAP = new Map<string, string>([
  [ADMIN_ROLE.approver, '承認者'],
  [ADMIN_ROLE.operator, '担当者'],
  [ADMIN_ROLE.systemAdmin, '管理者'],
])
