# 📰 Guide to Building a News List UI from API

This guide walks you through building a news list UI using an API response with the following structure:

```json
{
  "totalItems": 21,
  "pageIndex": 1,
  "pageSize": 12,
  "items": [ ... ]
}
```

---

## 📌 Goals

- Display news articles from the API
- Each article shows: thumbnail image, title, and publish date
- Add pagination to navigate pages
- Use a clean dark-mode UI

---

## 🧱 1. Suggested Folder Structure

```
/pages/news
  └── index.page.tsx              // Main news listing page

/page/news/
  ├── NewsList.tsx          // Renders the list of articles and pagination
  ├── NewsCard.tsx          // Individual news card component
```

---

## ⚙️ 2. Implementation Steps

### Step 1: Fetch API data

- Create state for `items`, `pageIndex`, and `totalItems`
- Use `useGet` to fetch data when `pageIndex` changes

### Step 2: Create `NewsCard` component

- Accepts props: `title`, `imageUrl`, `createdAt`
- Displays image on the left, content on the right
- Format date using JavaScript or a library

### Step 3: Build `NewsList` component

- Loop through `items` and render each with `NewsCard`

### Step 5: UI Styling

- Must Use TailwindCSS

---

## 📦 Required Props

### `NewsCard`

| Prop        | Type   | Description             |
| ----------- | ------ | ----------------------- |
| `title`     | string | News article title      |
| `imageUrl`  | string | URL of the thumbnail    |
| `createdAt` | string | ISO string publish date |

## 📌 Expected Outcome

- Page titled "News"
- Correctly rendered articles from the API
- Pagination at the bottom
- Clean, responsive
