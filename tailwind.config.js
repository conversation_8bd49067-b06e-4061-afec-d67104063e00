/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      screens: {
        xl: '1366px',
      },
      colors: {
        getBg: ' #0A0A0A',
        getCardBg: ' #1c1c1c', // card background color
        primary: ' #F2C157',
        primaryHover: 'rgb(193, 154, 70)',
        subText: '',
        tailwindBgMain: '#F5F3ED',
        tailwindBgSub: '#D9D9D9',
        tailwindBrand1: '#F2B516', // button , active link
        tailwindBrand2: '#EA652D', // button orange
        tailwindNeutral1: '#000000', // footer
        tailwindNeutral2: '#FFFFFF', // header
        tailwindNeutral3: '#9E9E9E', // place holder ,border
        tailwindNeutral4: '#E3E3E3', // button
        tailwindNeutral6: '#696969', // button
        getMarketplaceBtn: '#F2C157',
        tailwindBlue: ' #00A3FF', //text loading, link
        tailwindCompleted: '#52DE20', // text completed
        tailwindFailed: '#FF3030', // text failed
      },
      textColor: {
        primary: '#F2C157',
        primaryHover: 'rgba(242, 193, 87, 0.75)',
        secondary: '#ffffff80',
      },
      fontFamily: {
        sans: ['Noto Sans JP'],
        montserrat: ['Montserrat'],
      },
      borderColor: {
        default: ' #2f2f2f',
      },
    },
  },

  plugins: [],
}
