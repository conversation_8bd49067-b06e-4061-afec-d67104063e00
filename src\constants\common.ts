import { ENFT_TYPE, ETYPE_GIFT, METADATA_STATUS, SORT_TYPE } from './enum'

export const MAX_MOBILE_SIZE = 430

export enum textNotifications {
  DELETE_SUCCESS = '削除しました。',
  CHANGE_SUCCESS = '変更しました。',
  LOGOUT_SUCCESS = 'ログアウト成功',
  CREATE_SUCCESS = '作成しました。',
  CREATE_SUCCESS_EN = 'Created successfully',
  REQUIRE = 'を入力してください。',
  MAX_INPUT = '文字以内で入力してください。',
  CREATE_FAILED = 'Create Failed',
  CHANGE_FAILED = 'Update Failed',
  DELETE_FAILED = 'Delete Failed',
}

export const sortValues = [
  { value: 'LATEST_SOLD', label: '最新販売' },
  { value: 'OLDEST_CREATED', label: '最古販売' },
]
export const sortNftType = [
  { value: 'NFTのタイプ', label: 'NFTのタイプ', disabled: true },
  { value: 'active', label: 'Active' },
  { value: 'disable', label: 'Disable' },
]

export const typeValuesNft = [
  {
    name: 'All',
    value: null,
  },
  {
    name: 'name',
    value: 1,
  },
  {
    name: 'ふるさと納税版',
    value: 2,
  },
  {
    name: 'name',
    value: 3,
  },
]

export const sortValuesNew = [
  {
    name: '全て',
    value: '全て',
  },
  {
    name: '公式',
    value: '公式',
  },
  {
    name: 'プライベート',
    value: 'プライベート',
  },
]

export enum APPROVAL_STATUS {
  draft = 'draft',
  waiting = 'waiting',
  approved = 'approved',
  rejected = 'rejected',
}

export const renderTypeNft = {
  [ENFT_TYPE.all]: '全て',
  [ENFT_TYPE.ふるさと納税版]: 'ふるさと納税版',
}

export const renderMetadataStatus = {
  [METADATA_STATUS.all]: '全て',
  [METADATA_STATUS.notSell]: '販売停止',
  [METADATA_STATUS.selling]: '販売中',
  [METADATA_STATUS.soldOut]: '売切れ',
}

export const renderApprovalStatus = {
  [APPROVAL_STATUS.draft]: 'ドラフト',
  [APPROVAL_STATUS.waiting]: '承認待ち',
  [APPROVAL_STATUS.approved]: '承認済',
  [APPROVAL_STATUS.rejected]: '却下済み',
}

export const renderSortCondition = {
  [SORT_TYPE.lastCreated]: '最新作成',
  [SORT_TYPE.oldCreated]: '最古作成',
}

export const renderStatusGift = {
  [ETYPE_GIFT.received]: '受け取った',
  [ETYPE_GIFT.notReceived]: '受信していない',
}

export const MAX_SIZE_FILE = 10
export const MAX_SIZE_FILE_NFT = 50

export const LENGTH_BIT_IMAGE = 1024 * 1024

export const MIN_LENGTH_PASSWORD = 8

export const MAX_LENGTH_INPUT = {
  name: 100,
  maximumNumber: 5,
  tag: 255,
  description: 255,
  introduction: 3000,
  creatorName: 50,
  creditCard: 7,
  keyAttributes: 255,
  valueAttributes: 255,
  productId: 25,
}

export const pageConfig = {
  pageOne: 1,
  noData: 'データが見つかりません。',
}

export const PAGE_INFO_MY_NFT = {
  index: 1,
  size: 50,
}

export const PAGE_INFO_GIFT = {
  index: 1,
  size: 50,
  sortCondition: SORT_TYPE.lastCreated,
}

export const PAGE_ADMIN_DASHBOARD = {
  index: 1,
  size: 12,
  total: 0,
}

export const SUB_MENU_USER = {
  myProfile: 'my-profile',
  disconnect: 'disconnect',
}

export const LINK_MUMBAI_POLYGON_ADDRESS = `${process.env.NEXT_PUBLIC_POLYGON_BLOCK_EXPLORER_URL}/address`

export const LINK_MUMBAI_POLYGON_TX = `${process.env.NEXT_PUBLIC_POLYGON_BLOCK_EXPLORER_URL}/tx`

export const EXTENSION_INSTALL_URL =
  process.env.NEXT_PUBLIC_GET_WALLET_OFFICIAL_SITE

export const DEFAULT_PAGE_USERS_MANAGER = {
  defaultPage: 1,
  defaultPageSize: 10,
  tableHeight: 500,
}

export const DEFAULT_HOMEPAGE = {
  bannerPageSize: 200,
  defaultImageBannerIndex: 0,
}

export const DEFAULT_NFT = {
  pageSize: 20,
}

export enum SOCKET_EVENTS {
  PAYMENT_SUCCESS = 'PAYMENT_SUCCESS',
  MINT_ERROR = 'MINT_ERROR',
}

export enum CONTRACT_TYPE {
  ERC721 = 'ERC-721',
  ERC1155 = 'ERC-1155',
}

export const CONTRACT_TYPES = ['ERC-721', 'ERC-1155']

export const NO_CACHING_TIME = 0

export const DEFAULT_CONFIG_PARAMS = {}

export const MAX_LENGTH_USER_NAME = 50

export const DEBOUNCE_TIME = 500

export const CACHING_TIME = 1000 * 60 * 2

export const FALLBACK_IMAGE =
  'data:image/png;base64,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'

export const DEFAULT_PAGE_SIZE = 10

export const SCAN_URL = {
  URL:
    process.env.NEXT_PUBLIC_NODE_ENV === 'development'
      ? 'https://sepolia.etherscan.io/tx/{tx}'
      : 'https://etherscan.io/tx/{tx}',
}

export const ERROR_WALLET = 'Could not establish connection'
export const CHROME_ERROR = 'CHROME_ERROR'
