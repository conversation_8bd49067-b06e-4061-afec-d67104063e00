import { useQuery } from '@tanstack/react-query'
import { StatisticsCard } from '../../../components/card/StatisticsCard'
import { TextAdminTitle } from '../../../components/texts/TextAdminTitle'
import { getAdminAccountStats } from '../../../services/apiCall/admin'

export default function AdminAccountStatistics() {
  const { data } = useQuery({
    queryKey: ['getAdminAccountStats'],
    queryFn: getAdminAccountStats,
    // enabled: false,
  })

  const statTitles = {
    totalAccounts: 'アカウント合計',
    totalActiveAccounts: '有効なアカウント',
    totalDisabledAccounts: '無効なアカウント',
    totalSystemAdminAccounts: '管理者総数',
    totalOperatorAccounts: '担当者総数',
    totalApproverAccounts: '承認者総数',
  }

  return (
    <div>
      <TextAdminTitle title="アカウント統計" />
      <div className="grid grid-cols-1 md:grid-cols-2  [@media(min-width:1000px)]:grid-cols-3 [@media(min-width:1100px)]:grid-cols-4 [@media(min-width:1420px)]:grid-cols-6 gap-4">
        {data &&
          Object.keys(data).map((item) => (
            <StatisticsCard
              key={`${item}-${data[item]}`}
              title={statTitles[item]}
              stat={data[item]}
            />
          ))}
      </div>
    </div>
  )
}
