import { Editor } from '@tinymce/tinymce-react'
import { LegacyRef, useRef, useState } from 'react'
import { Editor as TinyMCEEditor } from 'tinymce'
import { config } from '@/utils/tinyMCE'
import { uploadBannerFile, uploadImageEditor } from '@/services/apiCall/banner'
import ModalUploadFile from '@/components/modal/ModalUploadFile'

interface TextEditorProps {
  value: string
  onChange: (value: string) => void
  className?: string
  maxLength?: number
  editable?: boolean
}

export default function TextEditor({
  value,
  onChange,
  className,
  maxLength,
  editable = true,
}: TextEditorProps) {
  const editorRef = useRef<TinyMCEEditor | null>(null)
  const [isModalUpload, setOpenModalUpload] = useState(false)

  const handleChangeText = (content: string, editor: TinyMCEEditor) => {
    if (maxLength && editor.getContent({ format: 'text' }).length > maxLength) {
      // Optionally show error or prevent update
      return
    }
    if (editable) {
      onChange(content)
    }
  }

  return (
    <>
      <div className={className}>
        <Editor
          apiKey={process.env.NEXT_PUBLIC_NODE_TINYMCE}
          value={value}
          onInit={(_, editor) => {
            editorRef.current = editor
            return
          }}
          ref={editorRef as LegacyRef<Editor>}
          onEditorChange={handleChangeText}
          init={{
            ...config,
            images_upload_handler: uploadImageEditor,
            images_file_types: 'jpg,webp,png,gif,jpeg',
            toolbar_mode: 'sliding',
            readonly: !editable,
            toolbar: editable ? config.toolbar : false,
            menubar: editable ? config.menubar : false,
            setup: function (editor) {
              editor.ui.registry.addButton('uploadButton', {
                icon: 'upload',
                tooltip: 'ファイルの挿入/編集',
                onAction: function () {
                  setOpenModalUpload(true)
                },
              })
            },
          }}
        />
      </div>
      <ModalUploadFile
        isModalOpen={isModalUpload}
        setModalOpen={setOpenModalUpload}
        editorRef={editorRef}
        funcUpload={uploadBannerFile}
      />
    </>
  )
}
