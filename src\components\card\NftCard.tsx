import { useTranslate } from '@/hooks'
import UiCard from '../ui/Card'
import { CARD_SIZES } from '@/constants'

type NftCardProps = {
  name: string
  imageUrl?: string
  videoUrl?: string
  price: string
  createdAt?: Date
  isSelected?: boolean
  variant?: string
  size?: CARD_SIZES
  href?: string
}

export function NftCard({
  name,
  imageUrl,
  videoUrl,
  price,
  createdAt,
  size = CARD_SIZES.MD,
  href,
}: NftCardProps) {
  const trans = useTranslate()
  return (
    <UiCard
      title={name}
      image={imageUrl}
      video={videoUrl}
      price={price}
      priceLabel={trans.nft_price}
      size={size}
      isSelected={false}
      href={href}
      variant="default"
    >
      <div className="text-sm text-gray-500 mt-2">
        {createdAt?.toLocaleDateString()}
      </div>
    </UiCard>
  )
}
