import { Input, Space } from 'antd'
import UiButton from '@/components/ui/Button'
import { IconPen, IconSearch } from '../../../icons'
import moment from 'moment'
import { useState, useEffect } from 'react'
import { getNewsForAdmin, deleteNews } from '@/services/apiCall/news'
import { OverlayLoading } from '@/components/common'
import UiModal from '@/components/ui/Modal'
import { ROLE, DEFAULT_PAGE_SIZE } from '@/constants'
import { useStore } from '@/store'
import { useDebounce, useDelete } from '@/hooks'
import { useQuery } from '@tanstack/react-query'
import { getPermission } from '@/utils'
import router from 'next/router'
import { APPROVAL_STATUS } from '@/constants'
import IconDelete from '@/components/icons/IconDelete'
import AdminTable from '@/components/table/AdminTable'
import ApprovalStatusBadge from '@/components/badge/ApprovalStatusBadge'

interface NewsItem {
  _id?: string
  title?: string
  createdBy?: { name?: string }
  status?: string
  createdAt?: string | Date
  updatedAt?: string | Date
  approvedBy?: { email?: string }
  approvedAt?: string | Date
}

interface NewsList {
  items?: NewsItem[]
  totalItems?: number
}

export default function ListNews() {
  const newsLabels = {
    news: 'ニュース',
    createNews: 'ニュース作成',
    searchNewsByTitle: 'タイトルや作成者でニュースを検索',
    title: 'タイトル',
    description: '説明',
    status: '状態',
    createdDate: '作成日',
    action: 'アクション',
    all: 'すべて',
    view: '詳細',
    creator: '作成者',
    approver: '承認者',
    approveDate: '承認日',
    updatedDate: '更新日',
  }

  const role = useStore((state) => state.role)
  const { canCreate } = getPermission({})

  const [searchTitle, setSearchTitle] = useState('')
  const debouncedSearchTitle = useDebounce<string>(searchTitle, 300)
  const [page, setPage] = useState(1)
  const pageSize = DEFAULT_PAGE_SIZE
  const [queryParams, setQueryParams] = useState({
    pageIndex: 1,
    searchWord: '',
  })
  const [confirmVisible, setConfirmVisible] = useState(false)
  const [selectedNews, setSelectedNews] = useState<NewsItem | null>(null)
  const { mutate, isPending } = useDelete({
    queryKey: ['newsList'],
    callback: (ids: string | string[]) => {
      const id = Array.isArray(ids) ? ids[0] : ids
      return deleteNews(id, role as ROLE.systemAdmin | ROLE.operator)
    },
  })

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      searchWord: debouncedSearchTitle,
      pageIndex: 1,
      pageSize,
    }))
    setPage(1)
  }, [debouncedSearchTitle])

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageIndex: page,
      pageSize,
    }))
  }, [page])

  const { data: listNews, isLoading } = useQuery<NewsList>({
    queryKey: ['newsList', queryParams],
    queryFn: () =>
      getNewsForAdmin(
        queryParams,
        role as ROLE.approver | ROLE.operator | ROLE.systemAdmin
      ),
  })

  const handleDelete = (news: NewsItem) => {
    setSelectedNews(news)
    setConfirmVisible(true)
  }

  const columns = [
    {
      title: newsLabels.title,
      dataIndex: 'title',
      key: 'title',
      render: (text: string) => (
        <span style={{ whiteSpace: 'pre-line' }}>
          {text?.replace(/([.!?])\s*/g, '$1\n')}
        </span>
      ),
    },
    {
      title: newsLabels.creator,
      dataIndex: ['createdBy', 'email'],
      key: 'creator',
      render: (_: any, record: any) => record.createdBy?.email || '-',
    },
    {
      title: newsLabels.status,
      dataIndex: 'approvalStatus',
      key: 'status',
      render: (approvalStatus: APPROVAL_STATUS) => (
        <ApprovalStatusBadge status={approvalStatus} />
      ),
    },
    {
      title: newsLabels.createdDate,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: Date | string) =>
        moment(createdAt).format('YYYY-MM-DD'),
    },
    {
      title: newsLabels.updatedDate,
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (updatedAt: Date | string) =>
        moment(updatedAt).format('YYYY-MM-DD'),
    },
    {
      title: newsLabels.approver,
      dataIndex: ['approvedBy', 'email'],
      key: 'approver',
      render: (_: any, record: any) => record.approvedBy?.email || '-',
    },
    {
      title: newsLabels.approveDate,
      dataIndex: 'approvedAt',
      key: 'approvedAt',
      render: (approvedAt: Date | string) =>
        approvedAt ? moment(approvedAt).format('YYYY-MM-DD') : '-',
    },
    {
      title: newsLabels.action,
      key: 'action',
      render: (_: any, record: any) => {
        const { canEdit, canDelete } = getPermission({
          approvalStatus: record.approvalStatus,
          creatorId: record.createdBy?._id || '',
          deleteApproved: true,
        })

        return (
          <Space className="flex items-center justify-around gap-2">
            <UiButton
              className="w-full !text-primary"
              title={
                <span className="flex items-center gap-1">
                  <span className="mr-1">{<IconPen />}</span>
                </span>
              }
              handleClick={() => {
                router.push(`/admin/news/edit/${record._id}`)
              }}
              isGradient={false}
              isDisabled={!canEdit}
            />
            <UiButton
              className="w-full flex !text-primary"
              title={
                <span className="flex items-center gap-1">
                  <span className="mr-1">{<IconDelete />}</span>
                </span>
              }
              handleClick={() => handleDelete(record)}
              isGradient={false}
              isDisabled={
                !canDelete || record.approvalStatus === APPROVAL_STATUS.waiting
              }
            />
            <UiButton
              className="w-full flex w-1/2 !text-primary"
              title={
                <span className="flex items-center gap-1">
                  <span className="mr-1">{newsLabels.view}</span>
                </span>
              }
              handleClick={() => {
                router.push(`/admin/news/view/${record._id}`)
              }}
              isGradient={false}
            />
          </Space>
        )
      },
    },
  ]

  return (
    <div>
      <div className="mb-6 flex-wrap md:flex justify-between">
        <Input
          size="large"
          placeholder={`${newsLabels.searchNewsByTitle}...`}
          suffix={<IconSearch height={20} width={20} />}
          className="w-full md:w-[60%] lg:w-1/3"
          value={searchTitle}
          onChange={(e) => setSearchTitle(e.target.value)}
          allowClear
        />
        {canCreate && (
          <UiButton
            // size={BUTTON_SIZES.NORMAL}
            title={newsLabels.createNews}
            handleClick={() => {
              router.push('/admin/news/add')
            }}
            className="flex justify-center items-center rounded-lg w-full md:w-[35%] lg:w-[214px] !h-11 mt-2 md:mt-0"
          />
        )}
      </div>
      <AdminTable
        className="custom-table"
        columns={columns}
        dataSource={listNews?.items || []}
        rowKey="_id"
        loading={isLoading}
        pagination={{
          pageSize,
          current: page,
          total: listNews?.totalItems || 0,
          position: ['bottomCenter'],
          onChange: (p) => setPage(p),
        }}
        scroll={{ x: 'max' }}
      />
      <UiModal
        header={''}
        isOpen={confirmVisible}
        onClose={() => {
          setConfirmVisible(false)
          setSelectedNews(null)
        }}
        cancelButton={{
          isShow: true,
          title: 'キャンセル',
          action: () => {
            setConfirmVisible(false)
            setSelectedNews(null)
          },
        }}
        confirmButton={{
          isShow: true,
          title: 'はい',
          action: () => {
            if (!selectedNews?._id) return
            mutate(selectedNews._id, {
              onSuccess: () => {
                setConfirmVisible(false)
                setSelectedNews(null)
              },
              onError: () => {
                setConfirmVisible(false)
                setSelectedNews(null)
              },
            })
          },
        }}
      >
        {selectedNews ? (
          <p className="text-center text-sm font-semibold">
            <span className="text-red-500">{`「${selectedNews.title}」`}</span>
            を削除しますか?
          </p>
        ) : (
          ''
        )}
      </UiModal>
      {isPending && <OverlayLoading />}
    </div>
  )
}
