# Project Development Rules

This document defines the key rules and coding standards for working on this project.

---

## 1. CSS Styling

- **Tailwind CSS** must be used for all styling.
- Avoid writing custom CSS files unless absolutely necessary.

---

## 2. UI Components

- **Ant Design (antd)** components should be used in most cases for UI elements.
- **Custom components** located in `@components/Ui` should be used where applicable.
- Avoid creating new UI components if an equivalent already exists in `antd` or `@components/Ui`.

---

## 3. Code Preservation

- **Do not delete** any code for features that are not explicitly requested for modification.
- **Do not remove** or alter existing code comments, as they may contain important documentation or instructions.

---

## 4. General Guidelines

- Follow the existing code structure and naming conventions.
- Keep the code clean, readable, and maintainable.
- Always test thoroughly before committing changes.

---

**Note:** Any violation of these rules may result in code review rejection.
