import Image from 'next/image'
import { shortenString } from '@/utils'
import { UserDetail } from '@/interfaces'
import { STORAGEKEY, getCookie } from '@/services/cookies'

import DefaultAvatar from 'public/images/defaultAvatar.png'
import { useCookies } from 'react-cookie'

export const ConnectItemMobile = ({
  setShowMobileInfo,
}: {
  setShowMobileInfo: (value: boolean) => void
}) => {
  const userInfo: UserDetail = getCookie(STORAGEKEY.USER)
  const [cookies] = useCookies([STORAGEKEY.WALLET_ADDRESS])
  const address = cookies[STORAGEKEY.WALLET_ADDRESS]

  return (
    <div className="text-white py-6">
      <div className="flex items-center border-b pb-9 border-dashed border-[#424955]">
        <div className="w-16 h-16 block mr-3 rounded-full border border-white overflow-hidden">
          <Image
            className="w-full h-full object-cover"
            src={userInfo?.avatar ?? DefaultAvatar}
            width={60}
            height={60}
            alt="user"
          />
        </div>
        <div>{shortenString(address as string)}</div>
      </div>
      <ul className="flex flex-col pb-3 border-dashed border-[#424955]">
        <li className="text-lg font-medium block py-6 text-left cursor-pointer">
          {'trans.settings'}
        </li>
        <li
          className="text-lg font-medium block py-6 text-left cursor-pointer text-black"
          onClick={() => {
            setShowMobileInfo(false)
          }}
        >
          {'trans.disconnect'}
        </li>
      </ul>
    </div>
  )
}
