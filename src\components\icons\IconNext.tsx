import * as React from 'react'

type Props = {
  size?: number | string
  stroke?: number
  color?: string
  className?: string
}

function IconNextBase({
  size = 16,
  stroke = 1.5,
  color = '#F2C157',
  className = '',
}: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 8 16"
      width={size}
      height={size}
      fill="none"
      className={className}
    >
      <path
        d="M0.999999 1L6.33062 7.21905C6.71581 7.66844 6.7158 8.33156 6.33062 8.78095L1 15"
        stroke={color}
        strokeWidth={stroke}
        strokeLinecap="round"
      />
    </svg>
  )
}

const IconNext = React.memo(IconNextBase)

export default IconNext
