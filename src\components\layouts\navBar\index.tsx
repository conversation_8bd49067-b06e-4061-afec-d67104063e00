import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import clsx from 'clsx'
import { HiOutlineViewList, HiOutlineX } from 'react-icons/hi'
import { ConnectItem } from './connectItem'
import LanguageSwitcher from './languageSwitcher'
import BalanceDisplay from './BalanceDisplay'
import { IMAGE } from '@/utils/string'
import { useViewport } from '@/hooks'

interface IProps {
  isHideNavLinks?: boolean
}

const MAX_WIDTH_MOBILE_RESPONSIVE = 1366

const NavBar: React.FC<IProps> = (): JSX.Element => {
  const [isShowMobileMenu, setShowMobileMenu] = useState(false)
  const [isShowMobileInfo, setShowMobileInfo] = useState(false)
  const { windowWidth } = useViewport()
  const isMobile = windowWidth < 768

  return (
    <>
      <nav className="max-w-[1920px] h-full w-[100vw] flex justify-between items-center shadow absolute px-[20px] z-10 bg-getBg opacity-90">
        {/* Left side - Logo */}
        <Link className="block" href="/">
          <Image src={IMAGE.logoBrand} alt="logo" height={47.65} />
        </Link>

        {/* Right side - Language Switcher, Balance & Connect Wallet */}
        <div className="flex items-center gap-4">
          {/* Language Switcher */}
          <LanguageSwitcher className="hidden lg:block" />

          {/* Balance Display */}
          <div className="hidden lg:block">
            <BalanceDisplay />
          </div>

          {/* Connect Wallet Button */}
          <div className="hidden lg:block">
            <ConnectItem />
          </div>

          {/* MOBILE: Menu list button */}
          <div className="flex items-center lg:hidden">
            {/* MOBILE: Open menu button */}
            <button
              onClick={() => setShowMobileMenu(true)}
              className={clsx('block', {
                hidden: isShowMobileMenu || isShowMobileInfo,
              })}
              aria-label="menu"
            >
              <HiOutlineViewList className="h-8 w-8 text-yellow-400" />
            </button>
            {/* MOBILE: Close menu button */}
            <button
              onClick={() => {
                setShowMobileMenu(false)
                setShowMobileInfo(false)
              }}
              className={clsx('block min-w-[30px]', {
                hidden: !isShowMobileMenu && !isShowMobileInfo,
              })}
              aria-label="menu"
            >
              <HiOutlineX className="h-8 w-8 text-yellow-400" />
            </button>
          </div>
        </div>
      </nav>

      {/* Menu NavLink Mobile */}
      {isMobile && (
        <div
          className={clsx(
            'w-full full-container px-6 transition-all translate-x-[100%] fixed top-0 right-0 bg-getBg mt-[var(--height-header)] pb-10 xl:hidden',
            { '!translate-x-0': isShowMobileMenu }
          )}
        >
          {/* Mobile Language Switcher */}
          <div className="flex justify-center py-4 border-b border-neutral-700">
            <LanguageSwitcher />
          </div>

          {/* Mobile Balance Display */}
          <div className="flex justify-center py-4 border-b border-neutral-700">
            <BalanceDisplay />
          </div>

          <ConnectItem
            isMobile
            isNotConnect={
              windowWidth < MAX_WIDTH_MOBILE_RESPONSIVE ? false : true
            }
            setShowMobileMenu={setShowMobileMenu}
          />
        </div>
      )}
    </>
  )
}

export default React.memo(NavBar)
