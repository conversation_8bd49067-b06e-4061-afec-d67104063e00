# Create NFT Page Refactor Plan (v2)

## 1. Overview

This plan describes the refactor of the "Create NFT" admin page. The goal is to update the form fields to match the new `CreateNftPayload` API contract, modernize the layout to match the provided image, and retain the robust image upload logic from the existing implementation. The new page will provide a clean, admin-friendly UI for creating NFTs, with improved UX and field validation.

---

## 2. Example Layout

**Reference:**

- See attached image for the intended layout (side-by-side form and preview, dark theme, grouped fields, etc.).

**Key UI Elements:**

- Sidebar navigation (unchanged)
- Main content: "Create an NFT" title
- Left: Image upload area (drag & drop, preview, file type/size info)
- Right: Form fields in vertical stack, grouped by logical section
- Traits (attributes) as dynamic tag-like inputs
- Date/time pickers for sale start/end
- Preview card on the right (optional, for live preview)
- Action buttons: Save Draft, Create

---

## 3. Flow

- **Layout:**
  - Use a two-column layout: left for image upload, right for form fields.
  - Use Tailwind CSS for styling, matching the dark theme and spacing from the image.
- **Form:**
  - Use React Hook Form (or Ant Design Form if legacy code is reused) for state and validation.
  - Fields must match `CreateNftPayload`:
    - Collection (dropdown, required)
    - Name (text, required)
    - Description (textarea, required)
    - Tag (tags input, optional)
    - External link (URL, optional)
    - Supply (number, required)
    - Price (number, required)
    - Sale Start Date (date input, required)
    - Sale Start Time (time input, required)
    - Sale End Date (date input, required)
    - Sale End Time (time input, required)
    - Traits (dynamic key-value pairs, optional)
    - isDraft (hidden, set by Save Draft/Create)
  - Image upload logic and preview: reuse from `FormNFT.tsx`.
- **State Management:**
  - Local state for form, image, and preview.
  - Use Zustand/global state only if needed for collections/options.
- **API:**
  - On submit, construct a `CreateNftPayload` object and POST to the API.
  - Image upload: POST image first, get `imageId`, then include in payload.
- **Validation:**
  - Required fields, correct types, URL validation, numeric checks, etc.
- **UX:**
  - Show loading spinners, error messages, and success toasts.
  - Disable Create button while submitting.

---

## 4. Version Note

- **Next.js:** 13.x (Pages Router)
- **TypeScript:** Yes
- **UI:** Tailwind CSS, Ant Design (legacy)
- **State:** Local/React Hook Form
- **Plan Action:** Refactor the Create NFT page to match new API and UI/UX requirements. Migrate fields, update layout, and keep robust image upload logic.

---

## 5. API Spec

### Endpoint

- `POST /api/nft` (or as defined in `API_URLS`)

### Request: `CreateNftPayload`

```ts
export type CreateNftPayload = {
  maximumNumber: number // Supply
  name: string
  description: string
  imageId: string
  tags: string[]
  attributes: { traitType: string; value: string }[]
  collection: string
  price: number
  saleStartAt: string // ISO string, combine date+time
  saleEndAt: string // ISO string, combine date+time
  isDraft: boolean
}
```

### Example Payload

```json
{
  "maximumNumber": 10,
  "name": "My NFT",
  "description": "Description here...",
  "imageId": "abc123",
  "tags": ["art", "rare"],
  "attributes": [
    { "traitType": "Color", "value": "Blue" },
    { "traitType": "Size", "value": "Large" }
  ],
  "collection": "col1",
  "price": 100,
  "saleStartAt": "2025-07-14T12:34:56Z",
  "saleEndAt": "2025-07-15T12:34:56Z",
  "isDraft": false
}
```

---

## 6. Summary List of Files

### Files to Create

- [ ] `docs/plan_create-nft_v2.md` (this plan)

### Files to Modify

- [ ] `src/pages/admin/nfts/FormNFT.tsx` – Refactor form fields, layout, and logic
- [ ] `src/pages/admin/nfts/add/index.page.tsx` – Ensure it uses the updated form
- [ ] `src/constants/routes.ts` – Confirm/create route if needed
- [ ] `src/services/apiCall/nft.ts` – Ensure API call matches new payload
- [ ] `public/locales/ja/common.json` – Add/adjust translations

---

## Implementation Steps

1. **Backup** the current `FormNFT.tsx` for reference.
2. **Update Form Fields:**
   - Remove legacy/unused fields.
   - Add new fields per `CreateNftPayload`.
   - Split `saleStartAt` and `saleEndAt` into date and time inputs, then combine on submit.
3. **Refactor Layout:**
   - Use a two-column layout as in the image.
   - Place image upload on the left, form fields on the right.
   - Group fields and add section headers as in the image.
   - Add preview card if desired.
4. **Retain Image Upload Logic:**
   - Reuse the upload/preview code from `FormNFT.tsx`.
   - Ensure uploaded image returns an `imageId` for the payload.
5. **Traits/Attributes:**
   - Implement as dynamic key-value pairs (add/remove rows).
6. **Validation:**
   - Add required/optional validation per API spec.
   - Validate date/time, numbers, and URLs.
7. **API Integration:**
   - On submit, upload image (if new), get `imageId`, then submit form data.
   - Support both "Save Draft" (`isDraft: true`) and "Create" (`isDraft: false`).
8. **Styling:**
   - Use Tailwind CSS for dark theme, spacing, and typography.
   - Match the image as closely as possible.
9. **i18n:**
   - Update/add Japanese translations for new/changed fields.
10. **Testing:**
    - Test all form flows, validation, and error handling.
11. **Docs:**
    - Update architecture docs if needed.

---

## 7. Other Notes

- **Accessibility:** Ensure keyboard navigation, ARIA labels, and alt text for images.
- **Performance:** Use dynamic imports for heavy components if needed.
- **UX:** Show loading, error, and success states. Disable buttons while submitting.
- **Preview:** Optionally, add a live preview card for the NFT.

---

## 8. Checklist

- [ ] Plan reviewed and approved
- [ ] All new/modified files listed
- [ ] API contracts confirmed
- [ ] UI/UX reviewed (matches image)
- [ ] Code follows project conventions
- [ ] Accessibility and i18n checked
- [ ] Tests updated/added
