import { ETYPE_TIME_OPTION } from '@/constants'
import { Paginated } from '.'

export type Transaction = {
  _id: string
  transactionHash: string
  sellerAddress: string
  buyerAddress: string
  boughtAt: number // Unix timestamp or milliseconds
  tokenId: string
  price: number
}

export type TransactionStats = {
  totalTransactions: number
  totalVolume: number
  averageSalePrice: number
  timeOption: ETYPE_TIME_OPTION
}

export type ListTransaction = Paginated<Transaction>
