import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { TNewsItem } from '@/interfaces'
import { FALLBACK_IMAGE } from '@/constants'
import { formatContext } from '@/utils/tinyMCE'
import moment from 'moment'

interface NewsCardProps {
  news: TNewsItem
  className?: string
}

const NewsCard: React.FC<NewsCardProps> = ({ news, className = '' }) => {
  return (
    <Link href={`/news/${news._id}`}>
      <div
        className={`group overflow-hidden transition-all duration-300 bg-getCardBg rounded-md hover:bg-neutral-700 ${className}`}
      >
        <div className="flex flex-col gap-4 p-5">
          {/* News Image */}
          <div className="relative h-[180px] flex-shrink-0 aspect-square overflow-hidden">
            <Image
              src={news.imageUrl || FALLBACK_IMAGE}
              alt={news.title}
              fill
              className="object-cover rounded-md hover:scale-105 transition-all duration-300"
            />
          </div>

          {/* News Content */}
          <div className="flex-1 min-w-0">
            <h4 className="font-montserrat font-semibold text-[13px] leading-[140%] tracking-[0.02em] text-white mb-2 line-clamp-2 transition-colors">
              {news.title}
            </h4>
            <p className="font-montserrat font-normal text-[13px] leading-[140%] tracking-[0.02em] text-white mb-2 line-clamp-2">
              {formatContext(news.context)}
            </p>
            <div className="font-montserrat font-normal text-[12px] leading-[140%] tracking-[0.02em] text-white">
              {news.createdAt
                ? moment(news.createdAt).format('YYYY-MM-DD')
                : '2024-07-14'}
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}

export default React.memo(NewsCard)
