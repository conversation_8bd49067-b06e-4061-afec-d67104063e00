import { ROLE } from './auth'

export const ROUTES = {
  //admin
  admin: '/admin',
  adminDashboard: '/admin/dashboard',
  adminCreateCollection: '/admin/collections/create',
  adminCreateNft: '/admin/nfts/add',
  adminCreateBanner: '/admin/banners/add',
  adminCreateNew: '/admin/news/add',
  adminCollections: '/admin/collections',
  adminNfts: `/admin/nfts`,
  adminApproval: `/admin/approval`,
  adminUsers: '/admin/users',
  adminTransactions: '/admin/transactions',
  adminNews: '/admin/news',
  adminCreateNews: '/admin/news/add',
  adminAdminAccounts: '/admin/admin-accounts',
  adminSettingConfig: '/admin/setting',
  adminActivity: '/admin/activity',
  adminBanners: '/admin/banners',
  adminLogin: '/admin/login',
  //home public
  home: '/',
  aboutUs: '/about-us',
  termsOfUse: '/terms-of-use',
  faqs: '/faqs',
  privacyPolicy: '/privacy-policy',
  contact: '/contact',
  commercialTransactionsLaw: '/commercial-transactions-law',
  nfts: '/nfts',
  claimNft: '/claim-nft',
  //user
  myInfo: '/my-info',
  myProfile: '/my-profile',
  userNftDetail: '/my-profile/nft',
  marketCollections: '/collections',
  myNfts: '/my-nfts',
  myTransactions: '/my-transactions',
  marketBanner: '/banners',
  collections: '/collections',
  knowledge: '/knowledge',
  news: '/news',
} as const

export const ADMIN_PROTECTED_ROUTES = new Map<string, string[]>([
  [ROUTES.adminCreateBanner, [ROLE.operator]],
  [`${ROUTES.adminBanners}/edit/[id]`, [ROLE.operator]],
  [ROUTES.adminCreateCollection, [ROLE.operator]],
  [`${ROUTES.adminCollections}/edit/[id]`, [ROLE.operator]],
  [ROUTES.adminCreateNews, [ROLE.operator]],
  [`${ROUTES.adminNews}/edit/[id]`, [ROLE.operator]],
  [ROUTES.adminCreateNft, [ROLE.operator]],
  [`${ROUTES.adminNfts}/edit/[id]`, [ROLE.operator]],
  [ROUTES.adminActivity, []],
  [ROUTES.adminAdminAccounts, []],
])
