import { UserBalance } from '@/interfaces'
import { create } from 'zustand'

interface WalletState {
  isConnected: boolean
  setIsConnected: (isConnected: boolean) => void
  balance: Omit<UserBalance, 'walletAddress'> | null
  setBalance: (updatedBalance: Omit<UserBalance, 'walletAddress'>) => void
  successPayment: number
  increaseSuccessPayment: () => void
}

export const walletStore = create<WalletState>((set) => ({
  isConnected: false,
  setIsConnected: (isConnected: boolean) => set({ isConnected }),
  balance: null,
  setBalance: (updatedBalance: Omit<UserBalance, 'walletAddress'>) =>
    set({ balance: updatedBalance }),
  successPayment: 0,
  increaseSuccessPayment: () =>
    set((state) => ({ ...state, succesPayment: state.successPayment + 1 })),
}))
