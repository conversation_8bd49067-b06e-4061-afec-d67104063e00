// Sitemap utility functions

export interface SitemapUrl {
  url: string
  lastmod?: string
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority?: string
}

export const generateSitemapUrl = ({
  url,
  lastmod,
  changefreq = 'weekly',
  priority = '0.7'
}: SitemapUrl): string => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com'
  
  return `
  <url>
    <loc>${baseUrl}${url}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`
}

export const generateSitemapXML = (urls: string[]): string => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.join('')}
</urlset>`
}

export const generateSitemapIndex = (sitemaps: Array<{ loc: string; lastmod?: string }>): string => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com'
  
  const sitemapEntries = sitemaps.map(({ loc, lastmod }) => `
  <sitemap>
    <loc>${baseUrl}${loc}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
  </sitemap>`).join('')

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries}
</sitemapindex>`
}

// Static routes configuration
export const STATIC_ROUTES: SitemapUrl[] = [
  { url: '', priority: '1.0', changefreq: 'daily' },
  { url: '/collections', priority: '0.9', changefreq: 'daily' },
  { url: '/news', priority: '0.8', changefreq: 'daily' },
  { url: '/knowledge', priority: '0.7', changefreq: 'weekly' },
  { url: '/about-us', priority: '0.6', changefreq: 'monthly' },
  { url: '/contact', priority: '0.6', changefreq: 'monthly' },
  { url: '/faqs', priority: '0.6', changefreq: 'monthly' },
  { url: '/privacy-policy', priority: '0.5', changefreq: 'yearly' },
  { url: '/terms-of-use', priority: '0.5', changefreq: 'yearly' },
  { url: '/commercial-transactions-law', priority: '0.5', changefreq: 'yearly' },
  { url: '/knowledge/what-is-an-nft', priority: '0.7', changefreq: 'monthly' },
  { url: '/knowledge/what-is-minting', priority: '0.7', changefreq: 'monthly' },
  { url: '/knowledge/what-is-a-get-wallet', priority: '0.7', changefreq: 'monthly' },
  { url: '/knowledge/how-to-stay-protected-in-web3', priority: '0.7', changefreq: 'monthly' },
]

// Cache configuration
export const SITEMAP_CACHE_CONFIG = {
  static: 86400, // 24 hours
  collections: 3600, // 1 hour
  news: 1800, // 30 minutes
  index: 3600, // 1 hour
}
