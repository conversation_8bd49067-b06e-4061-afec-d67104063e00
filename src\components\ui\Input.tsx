import { INPUT_SIZES, INPUT_VARIANTS } from '@/constants'
import React, { forwardRef, ReactNode } from 'react'
import { UseFormRegisterReturn } from 'react-hook-form'

interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  // Visual customization
  variant?: INPUT_VARIANTS
  size?: INPUT_SIZES

  // Content
  label?: string
  helperText?: string
  error?: string

  // Icons and addons
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  leftAddon?: ReactNode
  rightAddon?: ReactNode

  // Styling
  className?: string
  inputClassName?: string
  labelClassName?: string
  containerClassName?: string

  // States
  isLoading?: boolean
  isDisabled?: boolean
  isRequired?: boolean

  // React Hook Form integration
  registration?: UseFormRegisterReturn
}

const UiInput = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      // Variants and sizing
      variant = INPUT_VARIANTS.DEFAULT,
      size = INPUT_SIZES.LG,

      // Content
      label,
      helperText,
      error,

      // Icons and addons
      leftIcon,
      rightIcon,
      leftAddon,
      rightAddon,

      // Styling
      className = '',
      inputClassName = '',
      labelClassName = '',
      containerClassName = '',

      // States
      isLoading = false,
      isDisabled = false,
      isRequired = false,

      // React Hook Form
      registration,

      // Input props
      type = 'text',
      placeholder,
      ...rest
    },
    ref
  ) => {
    const getSizeClasses = () => {
      switch (size) {
        case 'sm':
          return 'h-[2.5rem] px-3 text-sm'
        case 'lg':
          return 'h-[2.75rem] px-4 text-lg'
        default:
          return 'h-[2rem] px-3 text-base'
      }
    }

    const getVariantClasses = () => {
      const baseClasses =
        'w-full transition-all duration-200 focus:outline-none focus:ring-2'

      switch (variant) {
        case 'search':
          return `${baseClasses} bg-gray-900/50 border border-gray-700/50 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/20`
        case 'outline':
          return `${baseClasses} bg-transparent border-2 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20`
        case 'filled':
          return `${baseClasses} bg-gray-100 border border-transparent text-gray-900 placeholder-gray-500 focus:bg-white focus:border-blue-500 focus:ring-blue-500/20`
        default:
          return `${baseClasses} bg-gray-900/30 border border-gray-700/30 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/20`
      }
    }

    const getErrorClasses = () => {
      if (error) {
        return 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
      }
      return ''
    }

    const getDisabledClasses = () => {
      if (isDisabled) {
        return 'opacity-50 cursor-not-allowed bg-gray-100'
      }
      return ''
    }

    const inputClasses = `
    ${getSizeClasses()}
    ${getVariantClasses()}
    ${getErrorClasses()}
    ${getDisabledClasses()}
    ${leftIcon || leftAddon ? 'pl-10' : ''}
    ${rightIcon || rightAddon || isLoading ? 'pr-10' : ''}
    ${inputClassName}
  `.trim()

    return (
      <div className={`w-full ${containerClassName}`}>
        {/* Label */}
        {label && (
          <label
            className={`block text-sm font-medium mb-2 ${
              variant === 'outline' || variant === 'filled'
                ? 'text-gray-700'
                : 'text-gray-300'
            } ${labelClassName}`}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Input Container */}
        <div className={`relative ${className}`}>
          {/* Left Addon */}
          {leftAddon && (
            <div className="absolute left-0 top-0 h-full flex items-center pl-3">
              {leftAddon}
            </div>
          )}

          {/* Left Icon */}
          {leftIcon && !leftAddon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {leftIcon}
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            type={type}
            placeholder={placeholder}
            disabled={isDisabled}
            className={inputClasses}
            {...registration}
            {...rest}
          />

          {/* Right Content */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
            {/* Loading Spinner */}
            {isLoading && (
              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
            )}

            {/* Right Icon */}
            {rightIcon && !isLoading && (
              <div className="text-gray-400">{rightIcon}</div>
            )}

            {/* Right Addon */}
            {rightAddon && !isLoading && rightAddon}
          </div>
        </div>

        {/* Helper Text or Error */}
        {(helperText || error) && (
          <div
            className={`mt-1 text-sm ${
              error ? 'text-red-500' : 'text-gray-500'
            }`}
          >
            {error || helperText}
          </div>
        )}
      </div>
    )
  }
)

UiInput.displayName = 'Input'

export default React.memo(UiInput)
