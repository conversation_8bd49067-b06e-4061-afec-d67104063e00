import { memo, ReactNode } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import clsx from 'clsx'
import Image from 'next/image'
import { Row } from 'antd'

import { ROLE, ROUTES } from '@/constants'
import { IMAGE } from '@/utils/string'
import {
  IconBannerManagement,
  IconCreateCollection,
  IconDiamond,
  IconDashboard,
  IconSpeaker,
  IconCreditCard,
  IconUserMangement,
  IconUserCircle,
} from '@/icons'
import { useStore } from '@/store'

export const SidebarAdmin = () => {
  const { pathname } = useRouter()

  const { role } = useStore()

  const sidebarItem: {
    name: string
    link: string
    isActive: boolean
    icon: ReactNode
    roles: string[]
  }[] = [
    {
      name: 'ダッシュボード',
      link: ROUTES.adminDashboard,
      isActive: true,
      icon: <IconDashboard />,
      roles: [ROLE.operator, ROLE.approver, ROLE.systemAdmin],
    },
    {
      name: 'コレクション管理',
      link: ROUTES.adminCollections,
      isActive: true,
      icon: <IconCreateCollection />,
      roles: [ROLE.operator, ROLE.approver, ROLE.systemAdmin],
    },
    {
      name: 'NFT管理',
      link: ROUTES.adminNfts,
      isActive: true,
      icon: <IconDiamond />,
      roles: [ROLE.operator, ROLE.approver, ROLE.systemAdmin],
    },
    {
      name: 'バナー管理',
      link: ROUTES.adminBanners,
      isActive: true,
      icon: <IconBannerManagement />,
      roles: [ROLE.operator, ROLE.approver, ROLE.systemAdmin],
    },
    {
      name: 'ユーザー管理',
      link: ROUTES.adminUsers,
      isActive: true,
      icon: <IconUserMangement />,
      roles: [ROLE.operator, ROLE.approver, ROLE.systemAdmin],
    },
    {
      name: '取引管理',
      link: ROUTES.adminTransactions,
      isActive: true,
      icon: <IconCreditCard />,
      roles: [ROLE.operator, ROLE.approver, ROLE.systemAdmin],
    },
    {
      name: 'ニュース管理',
      link: ROUTES.adminNews,
      isActive: true,
      icon: <IconSpeaker />,
      roles: [ROLE.operator, ROLE.approver, ROLE.systemAdmin],
    },
    {
      name: 'アカウント管理',
      link: ROUTES.adminAdminAccounts,
      isActive: true,
      icon: <IconUserCircle />,
      roles: [ROLE.systemAdmin],
    },
  ]

  return (
    <section className="bg-[var(--bg-color)] relative h-full flex flex-col  w-[var(--width-sidebar-admin)]">
      <div className="h-[80px] flex justify-start items-center pl-5">
        <Link
          href={ROUTES.adminDashboard}
          className="pr-4 border-r border-white border-opacity-15 flex items-center h-1/2"
        >
          <Image
            alt="logo"
            src={IMAGE.logoBrand}
            width={243}
            height={40}
            priority={true}
          />
        </Link>
      </div>
      <ul className="sticky top-[var(--height-header)] left-0 bg-[var(--bg-card)] mx-[18px] rounded-[8px]">
        {sidebarItem
          .filter((item) => item.isActive)
          .map((item, index) => {
            const isActive = pathname?.includes(
              item.link.slice(0, item.link.indexOf('?'))
            )
            if (item.roles.includes(role || ''))
              return (
                <li key={index} className="mr-3">
                  <Link
                    href={item.link}
                    className={clsx(
                      'py-5 px-3 text-base font-medium flex items-center justify-between',
                      {
                        'text-white hover:scale-110 hover:text-white duration-100 transition-all ease-in':
                          !isActive,
                        'text-tailwindBrand1 hover:text-tailwindBrand1':
                          isActive,
                      }
                    )}
                    aria-current="page"
                  >
                    <Row className="gap-3 !font-montserrat">
                      {item?.icon}
                      {item.name}
                    </Row>
                  </Link>
                </li>
              )
          })}
      </ul>
    </section>
  )
}

export default memo(SidebarAdmin)
