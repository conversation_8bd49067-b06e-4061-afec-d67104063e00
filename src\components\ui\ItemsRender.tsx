import React from 'react'
import { IconPrev, IconNext } from '@/components/icons'

export type ItemsRenderConfig = {
  prev?: React.ReactNode
  next?: React.ReactNode
}

export function createItemRender(config?: ItemsRenderConfig) {
  const finalConfig = {
    prev: <IconPrev />,
    next: <IconNext />,
    ...config,
  }

  return (
    _: number,
    type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next',
    originalElement: React.ReactNode
  ): React.ReactNode => {
    if (type === 'prev') {
      return (
        <button className="ant-pagination-item-link custom-pagination-btn">
          {finalConfig.prev}
        </button>
      )
    }
    if (type === 'next') {
      return (
        <button className="ant-pagination-item-link custom-pagination-btn">
          {finalConfig.next}
        </button>
      )
    }
    return originalElement
  }
}
