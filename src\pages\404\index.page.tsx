import Image from 'next/image'
import { useRouter } from 'next/router'

import { IMAGE } from '@/utils/string'
import { ROUTES } from '@/constants'

const NotFound = () => {
  const { push, asPath } = useRouter()
  const goHomePage = () => {
    const isAdminPage = asPath.includes('/admin')
    push(isAdminPage ? ROUTES.adminDashboard : ROUTES.home)
  }

  return (
    <div className="flex mt-8  items-center h-[70vh] flex-col  justify-center gap-16 px-6 py-28 md:px-24 md:py-20  lg:gap-28 lg:py-32">
      <div className="w-full lg:w-1/2 flex flex-col">
        <h1 className="py-4 text-3xl font-extrabold text-gray-800 lg:text-4xl">
          お探しのコンテンツは存在しません。
        </h1>
        <p className="py-4 text-base text-gray-800">
          コンテンツが削除されたか、ご入力いただいたリンクが間違っている可能性があります。
        </p>
        <p className="py-2 text-base text-gray-800">
          申し訳ございませんが、どうすればいいかは当社のホームページへアクセスしてください。
        </p>
        <button
          onClick={goHomePage}
          className="w-fit cursor-pointer rounded-md border bg-indigo-600 px-2 py-3 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-700 focus:ring-opacity-50 sm:px-16 "
        >
          ホームページに戻る
        </button>
      </div>
      <div className="w-full lg:w-1/2">
        <Image
          className=""
          width={300}
          height={250}
          src={IMAGE.default404}
          alt="not found"
        />
      </div>
    </div>
  )
}

export default NotFound
