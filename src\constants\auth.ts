export enum ROLE {
  user = 'commonUser',
  operator = 'operator',
  approver = 'approver',
  systemAdmin = 'systemAdmin',
}

export const adminUrlRoles = {
  admin: ['systemAdmin', 'admin', 'operator'],
  adminDashboard: ['systemAdmin', 'admin', 'operator'],
  adminMyNFT: ['systemAdmin', 'admin', 'operator'],
  adminTags: ['systemAdmin', 'admin', 'operator'],
  adminAdministrator: ['systemAdmin'],
  adminNews: ['systemAdmin'],
  adminBanners: ['systemAdmin'],
  adminGifts: ['systemAdmin'],
  adminOperators: ['systemAdmin'],
  adminUsers: ['systemAdmin', 'admin'],
  adminMyInfo: ['admin', 'operator'],
}
