export interface UserDetail {
  avatar: string
  avatarId: string
  createdAt: string
  lastConnectedTime: string
  nonce: number
  role: string
  status: string
  updatedAt: string
  walletAddress: string
  otherWallet: string
  _id: string
  name?: string
  biography?: string
  aboutMe?: string
  xUrl?: string
  instagramUrl?: string
  facebookUrl?: string
  totalNftValue?: number
}
export interface Users {
  items: UserDetail[]
  pageIndex: number
  pageSize: number
  totalItems: number
}
export interface UserSearchInput {
  search: string
  page: number
}
export interface ColumnUsersTableType {
  key: React.ReactNode
  name: string
  walletAddress: string
  otherWallet: string
}

export type UserBalance = {
  walletAddress: string
  getPay: number
  getToken: number
}
