interface PropsType {
  className?: string
  children: React.ReactNode
  require?: boolean
}
export default function Text(props: PropsType) {
  const { className, children = false, require, ...rest } = props
  return (
    <span className={`text-sm text-white ${className}`} {...rest}>
      {children}
      {require && (
        <span className={`ml-1 font-bold text-red-500 ${className}`} {...rest}>
          *
        </span>
      )}
    </span>
  )
}
