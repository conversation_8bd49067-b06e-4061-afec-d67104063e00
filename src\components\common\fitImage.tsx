import { memo } from 'react'
import Image from 'next/image'
import clsx from 'clsx'

import NoImage from 'public/images/no-image.png'

interface IProps {
  width: number
  height: number
  alt: string
  src?: string
  className?: string
}

const FitImage = (props: IProps) => {
  const styles = {
    width: `${props.width}px`,
    height: `${props.height}px`,
  }

  return (
    <div
      style={styles}
      className={clsx('rounded-3xl overflow-hidden', props?.className ?? '')}
    >
      <Image
        alt={props.alt}
        src={props?.src ?? NoImage}
        width={props.width}
        height={props.height}
        className="w-full h-full object-cover"
      />
    </div>
  )
}

export default memo(FitImage)
