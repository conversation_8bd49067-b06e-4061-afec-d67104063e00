import * as Yup from 'yup'

const ERROR_MESSAGE = {
  TITLE_REQUIRED: 'タイトルは必須です',
  TITLE_MAX: 'タイトルは100文字以内で入力してください',
  DESCRIPTION_REQUIRED: '説明が必要です',
  DESCRIPTION_MAX: '説明は1000文字以内で入力してください',
  BANNER_IMAGE_REQUIRED: 'バナー画像は必須です',
  MUST_BE_VALID_URL: '外部リンクは有効なURLである必要があります',
  EITHER_COLLECTION_OR_EXTERNAL_LINK:
    'コレクションまたは外部リンクのいずれかが必要です',
  MUST_PROVIDED_BUT_NOT_BOTH:
    '外部リンクまたはコレクションのいずれかを提供する必要がありますが、両方を提供することはできません。',
  EXTERNAL_LINK_MAX: '外部リンクは255文字以内である必要があります',
}

export const bannerSchema = Yup.object()
  .shape({
    title: Yup.string()
      .trim()
      .required(ERROR_MESSAGE.TITLE_REQUIRED)
      .max(100, ERROR_MESSAGE.TITLE_MAX),
    description: Yup.string()
      .trim()
      .required(ERROR_MESSAGE.DESCRIPTION_REQUIRED)
      .max(1000, ERROR_MESSAGE.DESCRIPTION_MAX),
    imageId: Yup.string().required(ERROR_MESSAGE.BANNER_IMAGE_REQUIRED),
    collection: Yup.string().trim(),
    externalLink: Yup.string()
      .trim()
      .url(ERROR_MESSAGE.MUST_BE_VALID_URL)
      .max(255, ERROR_MESSAGE.EXTERNAL_LINK_MAX)
      .nullable(),
  })
  .test(
    'collection-or-externalLink',
    'Either collection or externalLink is required',
    function (value) {
      const { collection, externalLink } = value || {}

      if (!collection && !externalLink) {
        return this.createError({
          path: 'collection',
          message: ERROR_MESSAGE.EITHER_COLLECTION_OR_EXTERNAL_LINK,
        })
      }

      if (collection && externalLink) {
        return this.createError({
          path: 'collection',
          message: ERROR_MESSAGE.MUST_PROVIDED_BUT_NOT_BOTH,
        })
      }
      return true
    }
  )

export const defaultValues = {
  title: '',
  description: '',
  imageId: '',
  collection: '',
  externalLink: '',
}

export const configPage = {
  ModalCancelCreateText: {
    createBanner: 'バナー作成をキャンセルしてもよろしいですか。',
    editBanner: 'このバナーの編集をキャンセルしてもよろしいですか。',
  },
}
