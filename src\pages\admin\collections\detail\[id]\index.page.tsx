import {
  BackgroundCollection,
  ListNfts,
  Statistics,
} from '@/components/collection/detail'
import { useRouter } from 'next/router'
import { useGet } from '@/hooks/useGet'
import { getCollection } from '@/services/apiCall/collections'
import { getNftForAdmin } from '@/services/apiCall'
import Spinner from '@/components/ui/Spinner'
import { useState } from 'react'

export default function DetailCollection() {
  const router = useRouter()
  const { id } = router.query

  const isReady = router.isReady && typeof id === 'string'

  const [pageIndex, setPageIndex] = useState(1)
  const PAGE_SIZE = 20

  const { data: collection, isLoading: collectionLoading } = useGet({
    queryKey: ['collection', id],
    callback: () => getCollection(id as string),
    enabled: isReady,
  })

  const { data: nftData, isLoading: nftLoading } = useGet({
    queryKey: ['nftMetadata', id, pageIndex],
    callback: () =>
      getNftForAdmin({
        collection: id as string,
        pageIndex,
        pageSize: PAGE_SIZE,
      }),
    enabled: isReady,
  })

  if (!isReady || collectionLoading)
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner size="h-12 w-12" />
      </div>
    )

  if (!collection) {
    return <div className="text-center text-red-500">Collection not found</div>
  }

  return (
    <div className="max-w-screen-xl m-auto">
      <BackgroundCollection
        backgroundImage={collection.backgroundImage}
        name={collection.name}
        standard={collection.standard}
        description={collection.description}
        collectionIcon={collection.logoImage}
      />
      <Statistics collection={collection} />
      <ListNfts
        collectionId={id as string}
        nfts={nftData?.items || []}
        total={nftData?.totalItems || 0}
        loading={nftLoading}
        pageSize={PAGE_SIZE}
        current={pageIndex}
        onPageChange={setPageIndex}
        isManagement={true}
      />
    </div>
  )
}
