import React, { useState } from 'react'
import { useRouter } from 'next/router'
import { Avatar } from 'antd'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { toast } from 'react-toastify'
import { useQuery } from '@tanstack/react-query'

import { adminGetUserDetail } from '@/services/apiCall/users'
import { getUserOwnedNfts } from '@/services/apiCall/nft-minted'
import { shortenString } from '@/utils'
import { IconCopy, DefaultAvatar } from '@/icons'
import { useTranslate } from '@/hooks'
import { SocialMediaButtons } from '@/components/buttons'
import { MyNftCard } from '@/components/card/MyNftCard'
import moment from 'moment'
import { UiPagination } from '@/components/ui/Pagination'
import { useStore } from '@/store'
import { ROLE } from '@/constants'
import { TextAdminHeader } from '@/components/texts/TextAdminHeader'

// Text object with Japanese translations
const text = {
  usersDetailInformation: 'ユーザーの詳細情報',
  userName: 'ユーザー名',
  walletAddress: 'ウォレットアドレス',
  timeOfParticipationUtc: '参加日時（UTC）',
  ownedNfts: '所有しているNFT',
  loading: '読み込み中',
  failedToLoadUserData: 'ユーザーデータの読み込みに失敗しました',
  invalidWalletAddress: '無効なウォレットアドレス',
  noNftsOwned: 'まだNFTを所有していません',
}

interface UserDetailProps {
  walletAddress: string
}

function UserDetailComp({ walletAddress }: UserDetailProps) {
  const trans = useTranslate()
  const router = useRouter()
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 8 // Show 8 NFTs per page as per the image
  const role = useStore((state) => state.role)

  // Query for user profile data
  const {
    data: userProfile,
    isLoading: profileLoading,
    error: profileError,
  } = useQuery({
    queryKey: ['adminUserProfile', walletAddress],
    queryFn: () => adminGetUserDetail(walletAddress, role as ROLE),
    enabled: !!walletAddress && !!role,
    retry: false,
    refetchOnMount: false,
    refetchInterval: false,
    refetchOnWindowFocus: false,
  })

  // Query for user NFTs with pagination
  const {
    data: userNfts,
    isLoading: nftsLoading,
    error: nftsError,
  } = useQuery({
    queryKey: ['userNfts', walletAddress, currentPage],
    queryFn: () =>
      getUserOwnedNfts(
        walletAddress,
        {
          pageIndex: currentPage,
          pageSize: pageSize,
        },
        role as ROLE
      ),
    enabled: !!walletAddress && !!role,
  })

  const handleCopyAddress = () => {
    toast.success(
      router.pathname.includes('admin')
        ? 'ウォレットアドレスがクリップボードにコピーされました！'
        : trans.wallet_address_copied
    )
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  if (profileLoading || nftsLoading) {
    return (
      <div className="min-h-container mt-8 mr-4 px-14">
        <div className="text-white text-center">{text.loading}</div>
      </div>
    )
  }

  if (profileError || nftsError) {
    return (
      <div className="min-h-container mt-8 mr-4 px-14">
        <div className="text-white text-center">
          {text.failedToLoadUserData}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-container mr-4">
      {/* User's Detail Information Section */}
      <div className="mb-10">
        <TextAdminHeader title={text.usersDetailInformation} />
        <div className="flex flex-col md:flex-row items-start gap-6">
          {/* Profile Picture */}
          <div className="flex-shrink-0 mx-auto md:mx-0 space-y-4">
            {userProfile?.avatar ? (
              <Avatar size={158} src={userProfile.avatar} className="border-4">
                {userProfile?.name?.charAt(0) || 'U'}
              </Avatar>
            ) : (
              <div className="w-[158px] h-[158px] rounded-full border-4 border-neutral-300 overflow-hidden">
                <DefaultAvatar width="158" height="158" />
              </div>
            )}
            <SocialMediaButtons user={userProfile} />
          </div>

          {/* User Information */}
          <div className="flex-1 space-y-4 text-center md:text-left">
            {/* User Name */}
            <div>
              <h2 className="font-montserrat font-semibold text-2xl tracking-[0.02em] leading-[120%] text-white">
                {userProfile?.name || '-'}
              </h2>
            </div>

            {/* Wallet Address */}
            <div className="space-y-2">
              <p className="font-montserrat font-normal text-xs tracking-[0.02em] leading-[170%] text-white/50">
                {text.walletAddress}
              </p>
              <div className="flex items-center gap-2">
                <p className="text-white text-sm font-mono">
                  {walletAddress
                    ? shortenString(walletAddress, 20, 10)
                    : '012345678...76543210'}
                </p>
                <CopyToClipboard
                  text={walletAddress || ''}
                  onCopy={handleCopyAddress}
                >
                  <button className="text-primary hover:text-primaryHover">
                    <IconCopy />
                  </button>
                </CopyToClipboard>
              </div>
            </div>

            {/* Time of Participation */}
            <div className="space-y-2">
              <p className="font-montserrat font-normal text-xs tracking-[0.02em] leading-[170%] text-white/50">
                {text.timeOfParticipationUtc}
              </p>
              <p className="font-montserrat font-normal text-sm tracking-[0.02em] leading-[170%] text-white">
                {userProfile?.createdAt
                  ? moment(userProfile.createdAt)
                      .utc()
                      .format('YYYY-MM-DD hh:mm:ss a')
                  : '-'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Owned NFTs Section */}
      <div className="mb-8">
        <h2 className="text-white text-[18px] font-semibold mb-6">
          {text.ownedNfts}
        </h2>

        {userNfts?.items && userNfts.items.length > 0 ? (
          <>
            {/* NFT Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
              {userNfts.items.map((nft, index) => (
                <MyNftCard
                  key={`${nft.name}-${index}`}
                  name={nft.name}
                  imageUrl={nft.image}
                />
              ))}
            </div>

            {/* Pagination */}
            {userNfts.totalItems > pageSize && (
              <div className="flex justify-center">
                <UiPagination
                  current={currentPage}
                  total={userNfts.totalItems}
                  pageSize={pageSize}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showLessItems={true}
                />
              </div>
            )}
          </>
        ) : (
          <div className="text-white text-center py-8">{text.noNftsOwned}</div>
        )}
      </div>
    </div>
  )
}

const UserDetail = React.memo(UserDetailComp)

// Main page component that gets wallet address from URL
function UserDetailPage() {
  const router = useRouter()
  const { id: walletAddress } = router.query

  // Show loading while router is not ready
  if (router.isFallback) {
    return (
      <div className="min-h-container mt-8 mr-4 px-14">
        <div className="text-white text-center">{text.loading}</div>
      </div>
    )
  }

  if (!walletAddress || typeof walletAddress !== 'string') {
    return (
      <div className="min-h-container mt-8 mr-4 px-14">
        <div className="text-white text-center">
          {text.invalidWalletAddress}
        </div>
      </div>
    )
  }

  return <UserDetail walletAddress={walletAddress} />
}

export default UserDetailPage
