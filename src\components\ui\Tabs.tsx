import React, { useState, ReactNode } from 'react'
export interface TabItem {
  id: string
  label: string
  content: ReactNode
  disabled?: boolean
}

export interface TabsProps {
  tabs: TabItem[]
  defaultActiveTab?: string
  className?: string
  tabClassName?: string
  activeTabClassName?: string
  contentClassName?: string
  variant?: 'default' | 'pills' | 'underline'
  size?: 'sm' | 'md' | 'lg'
  onChange?: (activeTabId: string) => void
}

const UiTabs: React.FC<TabsProps> = ({
  tabs,
  defaultActiveTab,
  className = '',
  tabClassName = '',
  activeTabClassName = '',
  contentClassName = '',
  variant = 'default',
  size = 'md',
  onChange,
}) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab || tabs[0]?.id)

  const handleTabClick = (tabId: string) => {
    const tab = tabs.find((t) => t.id === tabId)
    if (tab && !tab.disabled) {
      setActiveTab(tabId)
      onChange?.(tabId)
    }
  }

  const getTabSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm'
      case 'lg':
        return 'px-6 py-3 text-lg'
      default:
        return 'px-4 py-2 text-base'
    }
  }

  const getVariantClasses = (isActive: boolean, isDisabled: boolean) => {
    const baseClasses = `${getTabSizeClasses()} font-medium transition-all duration-200 cursor-pointer`

    if (isDisabled) {
      return `${baseClasses} opacity-50 cursor-not-allowed text-gray-400`
    }

    switch (variant) {
      case 'pills':
        return isActive
          ? `${baseClasses} bg-blue-600 text-white rounded-lg shadow-sm`
          : `${baseClasses} text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg`

      case 'underline':
        return isActive
          ? `${baseClasses} text-blue-600 border-b-2 border-blue-600`
          : `${baseClasses} text-gray-600 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-300`

      default:
        return isActive
          ? `${baseClasses} bg-white text-blue-600 border border-gray-200 border-b-white rounded-t-lg shadow-sm`
          : `${baseClasses} text-gray-600 hover:text-blue-600 hover:bg-gray-50 border border-transparent rounded-t-lg`
    }
  }

  const getTabListClasses = () => {
    const baseClasses = 'flex'

    switch (variant) {
      case 'pills':
        return `${baseClasses} gap-1 p-1 bg-gray-100 rounded-lg`
      case 'underline':
        return `${baseClasses} border-b border-gray-200`
      default:
        return `${baseClasses} border-b border-gray-200`
    }
  }

  const activeTabContent = tabs.find((tab) => tab.id === activeTab)

  return (
    <div className={`w-full ${className}`}>
      {/* Tab List */}
      <div className={getTabListClasses()} role="tablist">
        {tabs.map((tab) => {
          const isActive = tab.id === activeTab
          const isDisabled = tab.disabled || false

          return (
            <button
              key={tab.id}
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
              aria-disabled={isDisabled}
              tabIndex={isActive ? 0 : -1}
              className={`${getVariantClasses(
                isActive,
                isDisabled
              )} ${tabClassName} ${isActive ? activeTabClassName : ''}`}
              onClick={() => handleTabClick(tab.id)}
              disabled={isDisabled}
            >
              {tab.label}
            </button>
          )
        })}
      </div>

      {/* Tab Content */}
      <div
        id={`tabpanel-${activeTab}`}
        role="tabpanel"
        aria-labelledby={`tab-${activeTab}`}
        className={`mt-4 ${contentClassName}`}
      >
        {activeTabContent?.content}
      </div>
    </div>
  )
}

export default React.memo(UiTabs)
