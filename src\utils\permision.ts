import { ADMIN_ROLE, APPROVAL_STATUS } from '@/constants'
import { getCookie } from './handleCookie'
import { STORAGEKEY } from '@/services/cookies'
import { Admin } from '@/interfaces'

export const getPermission = ({
  approvalStatus,
  creatorId,
  admin,
  deleteApproved = false,
}: {
  approvalStatus?: APPROVAL_STATUS
  creatorId?: string
  admin?: Admin
  deleteApproved?: boolean
}): {
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canApprove: boolean
  canSetPriority: boolean
} => {
  let currentAdmin: Admin | undefined
  try {
    currentAdmin =
      admin ||
      (JSON.parse(getCookie(STORAGEKEY.ADMIN) || '') as unknown as Admin)
  } catch (err) {
    currentAdmin = admin
  }
  const canCreate = [ADMIN_ROLE.systemAdmin, ADMIN_ROLE.operator].includes(
    currentAdmin?.role as ADMIN_ROLE
  )
  let canEdit = false
  let canDelete = false
  let canApprove = false
  let canSetPriority = false
  const isSystemAdmin = currentAdmin?.role === ADMIN_ROLE.systemAdmin
  const isApprover = currentAdmin?.role === ADMIN_ROLE.approver
  const isCreator = (currentAdmin?._id || currentAdmin?.id) === creatorId

  if (
    [APPROVAL_STATUS.draft, APPROVAL_STATUS.rejected].includes(
      approvalStatus as APPROVAL_STATUS
    ) &&
    (isCreator || isSystemAdmin)
  ) {
    canEdit = true
    canDelete = true
  }

  if (
    approvalStatus === APPROVAL_STATUS.waiting &&
    (isApprover || isSystemAdmin)
  ) {
    canApprove = true
  }

  if (
    approvalStatus === APPROVAL_STATUS.approved &&
    (isCreator || isSystemAdmin) &&
    deleteApproved
  ) {
    canDelete = true
  }

  if (
    approvalStatus === APPROVAL_STATUS.approved &&
    (isSystemAdmin || isApprover)
  ) {
    canSetPriority = true
  }

  return { canCreate, canEdit, canDelete, canApprove, canSetPriority }
}
