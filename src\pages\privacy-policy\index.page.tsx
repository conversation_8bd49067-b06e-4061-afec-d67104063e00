import { StaticPageCard } from '@/components/card'
import { StaticPageLayout } from '@/components/layouts/staticPageLayout'
import { getCurrentLanguage } from '@/utils'
import { IMAGE } from '@/utils/string'
import { PrivacyAndPolicyEn } from './PrivacyAndPolicyEn'
import { PrivacyAndPolicyJa } from './PrivacyAndPolicyJa'

function PrivacyAndPolicy() {
  const lang = getCurrentLanguage()
  const title =
    lang === 'en' ? 'Glitters Privacy Policy' : 'Glitters プライバシーポリシー'

  return (
    <StaticPageLayout title={title}>
      <StaticPageCard
        title={title}
        imageUrl={IMAGE.whatIsMinting}
        createdAt="2025-08-29"
        classNames={'max-w-full privacy-policy'}
      >
        {lang === 'en' ? <PrivacyAndPolicyEn /> : <PrivacyAndPolicyJa />}
      </StaticPageCard>
    </StaticPageLayout>
  )
}

export default PrivacyAndPolicy
