# Transaction Modals

This document describes two modal dialogs used to inform users about the result of a purchase transaction: a success modal and a failure modal.

---

## ✅ Purchase Success Modal

### Purpose

Displayed when a user completes a purchase successfully. It confirms the action and guides the user on what to do next.

### Components

#### 1. Icon

- **Type:** Shopping cart icon
- **Meaning:** Symbolizes successful purchase
- **Position:** Centered at the top

#### 2. Message

- **Text:**
  ```
  Purchase completed. You can check it from My Inventory!
  ```
- **Purpose:** Confirms success and provides instructions

#### 3. Action Button

- **Label:** `OK`
- **Action:** Closes the modal
- **Style:** Small square button, dark background, white uppercase text

#### 4. Background Overlay (assumed)

- Prevents interaction with the rest of the interface

---

## ❌ Transaction Failure Modal

### Purpose

Displayed when a transaction fails. It informs the user of the failure and provides a space to show the cause.

### Components

#### 1. Icon

- **Type:** Sad face icon
- **Meaning:** Symbolizes a failed or unsuccessful action
- **Position:** Centered at the top

#### 2. Message

- **Text:**
  ```
  Transaction unsuccessful. Cause:
  ```
- **Purpose:** Informs the user of the failure and leads into an error message (may be dynamically filled)

#### 3. Action Button

- **Label:** `OK`
- **Action:** Closes the modal
- **Style:** Same as in the success modal

#### 4. Background Overlay (assumed)

- Keeps focus on the modal

---

## 💡 Usage Tips

- Both modals should appear as part of a consistent design system.
- The failure modal should dynamically append or display the actual cause of the error.
- Keep the user informed but not overwhelmed — one clear action is enough (`OK`).
