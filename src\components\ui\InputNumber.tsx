import React, {
  forwardRef,
  useState,
  useCallback,
  useEffect,
  ReactNode,
} from 'react'
import { UseFormRegisterReturn } from 'react-hook-form'

interface NumberInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    'size' | 'onChange' | 'type' | 'value'
  > {
  // Value and change handling
  value?: number
  onChange?: (value: number | undefined) => void

  // Number constraints
  min?: number
  max?: number
  step?: number
  precision?: number // Number of decimal places

  // Visual customization
  variant?: 'default' | 'search' | 'outline' | 'filled'
  size?: 'sm' | 'md' | 'lg'

  // Content
  label?: string
  helperText?: string
  error?: string

  // Icons and addons (only for when controls are hidden)
  leftIcon?: ReactNode
  rightIcon?: ReactNode

  // Styling
  className?: string
  inputClassName?: string
  labelClassName?: string
  containerClassName?: string
  buttonClassName?: string

  // Features
  allowMouseWheel?: boolean
  allowKeyboard?: boolean
  hideControls?: boolean
  keepWithinRange?: boolean // Auto-correct values outside min/max
  allowEmpty?: boolean // Allow empty/undefined values

  // Formatting
  formatValue?: (value: number) => string
  parseValue?: (value: string) => number | undefined
  prefix?: string // e.g., "$", "€"
  suffix?: string // e.g., "px", "%", "kg"

  // States
  isLoading?: boolean
  isDisabled?: boolean
  isRequired?: boolean

  // React Hook Form integration
  registration?: UseFormRegisterReturn
}

const UiNumberInput = forwardRef<HTMLInputElement, NumberInputProps>(
  (
    {
      // Value and constraints
      value,
      onChange,
      min,
      max,
      step = 1,
      precision = 0,

      // Variants and sizing
      variant = 'default',
      size = 'lg',

      // Content
      label,
      helperText,
      error,
      placeholder = '0',

      // Icons (only when controls are hidden)
      leftIcon,
      rightIcon,

      // Styling
      className = '',
      inputClassName = '',
      labelClassName = '',
      containerClassName = '',
      buttonClassName = '',

      // Features
      allowMouseWheel = false,
      allowKeyboard = true,
      hideControls = false,
      keepWithinRange = true,
      allowEmpty = false,

      // Formatting
      formatValue,
      parseValue,
      prefix = '',
      suffix = '',

      // States
      isLoading = false,
      isDisabled = false,
      isRequired = false,

      // React Hook Form
      registration,

      ...rest
    },
    ref
  ) => {
    const [displayValue, setDisplayValue] = useState('')
    const [isFocused, setIsFocused] = useState(false)

    // Format number for display
    const formatNumber = useCallback(
      (num: number | undefined): string => {
        if (num === undefined || num === null) return ''

        if (formatValue) {
          return formatValue(num)
        }

        const formatted =
          precision > 0 ? num.toFixed(precision) : num.toString()
        return `${prefix}${formatted}${suffix}`
      },
      [formatValue, precision, prefix, suffix]
    )

    // Parse string to number
    const parseNumber = useCallback(
      (str: string): number | undefined => {
        if (!str.trim()) return allowEmpty ? undefined : 0

        if (parseValue) {
          return parseValue(str)
        }

        // Remove prefix/suffix for parsing
        let cleanStr = str
        if (prefix) cleanStr = cleanStr.replace(new RegExp(`^\\${prefix}`), '')
        if (suffix) cleanStr = cleanStr.replace(new RegExp(`\\${suffix}$`), '')

        const parsed = parseFloat(cleanStr)
        return isNaN(parsed) ? undefined : parsed
      },
      [parseValue, prefix, suffix, allowEmpty]
    )

    // Clamp value within min/max range
    const clampValue = useCallback(
      (val: number | undefined): number | undefined => {
        if (val === undefined) return val
        if (!keepWithinRange) return val

        let clamped = val
        if (min !== undefined) clamped = Math.max(min, clamped)
        if (max !== undefined) clamped = Math.min(max, clamped)

        return clamped
      },
      [min, max, keepWithinRange]
    )

    // Update display value when value prop changes
    useEffect(() => {
      if (!isFocused) {
        setDisplayValue(formatNumber(value))
      }
    }, [value, formatNumber, isFocused])

    // Handle input value change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      setDisplayValue(inputValue)

      const parsed = parseNumber(inputValue)
      const clamped = clampValue(parsed)
      onChange?.(clamped)
    }

    // Handle increment/decrement
    const adjustValue = useCallback(
      (delta: number) => {
        const currentValue = value ?? 0
        const newValue = currentValue + delta
        const clamped = clampValue(newValue)

        if (clamped !== undefined) {
          const rounded =
            precision > 0
              ? Math.round(clamped * Math.pow(10, precision)) /
                Math.pow(10, precision)
              : Math.round(clamped)

          onChange?.(rounded)
        }
      },
      [value, clampValue, precision, onChange]
    )

    // Handle focus
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true)
      // Show raw number without formatting when focused
      if (value !== undefined) {
        const rawValue =
          precision > 0 ? value.toFixed(precision) : value.toString()
        setDisplayValue(rawValue)
      }
      rest.onFocus?.(e)
    }

    // Handle blur
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false)

      // Parse and validate final value
      const parsed = parseNumber(displayValue)
      const clamped = clampValue(parsed)

      if (clamped !== value) {
        onChange?.(clamped)
      }

      // Update display with formatted value
      setDisplayValue(formatNumber(clamped))
      rest.onBlur?.(e)
    }

    // Handle keyboard events
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!allowKeyboard) return

      if (e.key === 'ArrowUp') {
        e.preventDefault()
        adjustValue(step)
      } else if (e.key === 'ArrowDown') {
        e.preventDefault()
        adjustValue(-step)
      }

      rest.onKeyDown?.(e)
    }

    // Handle mouse wheel
    const handleWheel = (e: React.WheelEvent<HTMLInputElement>) => {
      if (!allowMouseWheel || !isFocused) return

      e.preventDefault()
      const delta = e.deltaY > 0 ? -step : step
      adjustValue(delta)
    }

    const getSizeClasses = () => {
      switch (size) {
        case 'sm':
          return 'h-[2.5rem] w-[6rem] text-sm'
        case 'lg':
          return 'h-[2.75rem] w-[7.5rem] text-lg'
        default:
          return 'h-[2rem] w-[6rem] text-base'
      }
    }

    const getButtonSizeClasses = () => {
      switch (size) {
        case 'sm':
          return 'w-[2.5rem] h-[2.5rem] text-xs'
        case 'lg':
          return 'w-[2.75rem] h-[2.75rem] text-lg'
        default:
          return 'w-[2rem] h-[2rem] text-sm'
      }
    }

    const getVariantClasses = () => {
      switch (variant) {
        case 'search':
          return 'text-white'
        case 'outline':
          return 'text-gray-900'
        case 'filled':
          return 'border-transparent text-gray-900'
        default:
          return 'text-white'
      }
    }

    const getButtonVariantClasses = () => {
      switch (variant) {
        case 'search':
        case 'default':
          return 'text-gray-300 hover:text-white'
        case 'outline':
        case 'filled':
          return 'text-gray-600 hover:text-gray-800'
      }
    }

    const getErrorClasses = () => {
      if (error) {
        return 'border-red-500'
      }
      return ''
    }

    const getDisabledClasses = () => {
      if (isDisabled) {
        return 'opacity-50 cursor-not-allowed'
      }
      return ''
    }

    // Base container classes
    const containerClasses = `
      flex items-center border transition-all duration-200
      ${getSizeClasses()}
      ${getVariantClasses()}
      ${getErrorClasses()}
      ${getDisabledClasses()}
      ${hideControls ? 'focus-within:ring-2 focus-within:ring-blue-500/20' : ''}
    `.trim()

    const inputClasses = `
      bg-transparent border-0 outline-none text-center w-[3.875rem]
      placeholder-gray-400 focus:outline-none
      ${
        variant === 'outline' || variant === 'filled'
          ? 'placeholder-gray-500'
          : ''
      }
      ${inputClassName}
    `.trim()

    const buttonClasses = `
      ${getButtonSizeClasses()}
      ${getButtonVariantClasses()}
      flex items-center justify-center border-0 font-mono font-bold
      ${buttonClassName}
      ${
        isDisabled
          ? 'cursor-not-allowed opacity-50'
          : 'cursor-pointer hover:scale-105'
      }
    `.trim()

    // Icons
    const MinusIcon = () => (
      <svg
        className="w-3 h-3"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M20 12H4"
        />
      </svg>
    )

    const PlusIcon = () => (
      <svg
        className="w-3 h-3"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 4v16m8-8H4"
        />
      </svg>
    )

    if (hideControls) {
      // Render as regular input when controls are hidden
      const regularInputClasses = `
        w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2
        ${getSizeClasses()}
        ${getVariantClasses()}
        ${getErrorClasses()}
        ${getDisabledClasses()}
        ${leftIcon ? 'pl-10' : 'px-3'}
        ${rightIcon || isLoading ? 'pr-10' : 'px-3'}
        focus:border-blue-500 focus:ring-blue-500/20
      `.trim()

      return (
        <div className={`w-full ${containerClassName}`}>
          {/* Label */}
          {label && (
            <label
              className={`block text-sm font-medium mb-2 ${
                variant === 'outline' || variant === 'filled'
                  ? 'text-gray-700'
                  : 'text-gray-300'
              } ${labelClassName}`}
            >
              {label}
              {isRequired && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}

          {/* Input Container */}
          <div className={`relative ${className}`}>
            {/* Left Icon */}
            {leftIcon && (
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                {leftIcon}
              </div>
            )}

            {/* Input Field */}
            <input
              ref={ref}
              type="text"
              inputMode="decimal"
              value={displayValue}
              onChange={handleInputChange}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              onWheel={handleWheel}
              placeholder={placeholder}
              disabled={isDisabled}
              className={regularInputClasses}
              {...registration}
              {...rest}
            />

            {/* Right Icon or Loading */}
            {(rightIcon || isLoading) && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
                ) : (
                  <div className="text-gray-400">{rightIcon}</div>
                )}
              </div>
            )}
          </div>

          {/* Helper Text or Error */}
          {(helperText || error) && (
            <div
              className={`mt-1 text-sm ${
                error ? 'text-red-500' : 'text-gray-500'
              }`}
            >
              {error || helperText}
            </div>
          )}
        </div>
      )
    }

    return (
      <div className={`${containerClassName}`}>
        {/* Label */}
        {label && (
          <label
            className={`block text-sm font-medium mb-2 ${
              variant === 'outline' || variant === 'filled'
                ? 'text-gray-700'
                : 'text-gray-300'
            } ${labelClassName}`}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Number Input Container */}
        <div className={`${containerClasses} ${className}`}>
          {/* Minus Button */}
          <button
            type="button"
            className={`
              ${buttonClasses}
              ${
                min !== undefined && value !== undefined && value <= min
                  ? 'opacity-50 cursor-not-allowed'
                  : ''
              }
            `}
            onClick={() => !isDisabled && adjustValue(-step)}
            disabled={
              isDisabled ||
              (min !== undefined && value !== undefined && value <= min)
            }
            tabIndex={-1}
          >
            <MinusIcon />
          </button>

          {/* Input Field */}
          <input
            ref={ref}
            type="number"
            inputMode="decimal"
            value={displayValue}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            onWheel={handleWheel}
            placeholder={placeholder}
            disabled={isDisabled}
            className={inputClasses}
            {...registration}
            {...rest}
          />

          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-inherit rounded-lg">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
            </div>
          )}

          {/* Plus Button */}
          <button
            type="button"
            className={`
              ${buttonClasses}
              ${
                max !== undefined && value !== undefined && value >= max
                  ? 'opacity-50 cursor-not-allowed'
                  : ''
              }
            `}
            onClick={() => !isDisabled && adjustValue(step)}
            disabled={
              isDisabled ||
              (max !== undefined && value !== undefined && value >= max)
            }
            tabIndex={-1}
          >
            <PlusIcon />
          </button>
        </div>

        {/* Helper Text or Error */}
        {(helperText || error) && (
          <div
            className={`mt-1 text-sm ${
              error ? 'text-red-500' : 'text-gray-500'
            }`}
          >
            {error || helperText}
          </div>
        )}
      </div>
    )
  }
)

UiNumberInput.displayName = 'NumberInput'

export default React.memo(UiNumberInput)
