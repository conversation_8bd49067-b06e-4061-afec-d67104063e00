import React, { ReactNode, useState } from 'react'
import dynamic from 'next/dynamic'
import {
  BUTTON_SIZES,
  CARD_RATIOS,
  CARD_SIZES,
  INPUT_SIZES,
  INPUT_VARIANTS,
  PAYMENT_STATUS,
} from '@/constants'
import { TabItem } from './Tabs'
import { useForm } from 'react-hook-form'
import { SelectOption } from './Select'
import { UseInfiniteQueryResult } from '@tanstack/react-query'
import { fetchPosts, fetchProducts, useAppInfiniteQuery } from './utils'
import { InfiniteScrollProps } from '@/components/ui/InfiniteScroll'
const UiButton = dynamic(() => import('@/components/ui/Button'))
const UiCard = dynamic(() => import('@/components/ui/Card'))
const UiTabs = dynamic(() => import('@/components/ui/Tabs'))
const UiInput = dynamic(() => import('@/components/ui/Input'))
const UiSelect = dynamic(() => import('@/components/ui/Select'))
const UiNumberInput = dynamic(() => import('@/components/ui/InputNumber'))
const UiModal = dynamic(() => import('@/components/ui/Modal'))
const UiStatusContent = dynamic(() => import('@/components/ui/StatusContent'))
const DynamicInfiniteScroll = dynamic(
  () => import('@/components/ui/InfiniteScroll')
)

function UiInfiniteScroll<T>(props: InfiniteScrollProps<T>) {
  const TypedComponent = DynamicInfiniteScroll as React.ComponentType<
    InfiniteScrollProps<T>
  >
  return <TypedComponent {...props} />
}

const ExamplePage = () => {
  const RenderOptions = (options: Record<string, any>) => {
    return (
      <span className="text-xs text-white font-semibold">
        {JSON.stringify(options, null, 2)}
      </span>
    )
  }

  const RenderButton = () => {
    return (
      <div className="flex flex-col flex-1 gap-4">
        <div className="flex flex-col flex-1 gap-2">
          <div className="text-xl text-white font-semibold">
            Button Component
          </div>
          <div className="flex flex-col flex-wrap flex-1 gap-2">
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.SM,
                  isGradient: true,
                  isDisabled: false,
                  isLoading: false,
                }}
              />
              <UiButton title="Connect Wallet" />
            </div>
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.NORMAL,
                  isGradient: true,
                  isDisabled: true,
                  isLoading: false,
                }}
              />
              <UiButton title="Connect Wallet" isDisabled={true} />
            </div>
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.NORMAL,
                  isGradient: true,
                  isDisabled: true,
                  isLoading: true,
                }}
              />
              <UiButton title="Connect Wallet" isLoading={true} />
            </div>
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.NORMAL,
                  isGradient: true,
                  isDisabled: false,
                  isLoading: false,
                }}
              />
              <UiButton title="Search" size={BUTTON_SIZES.NORMAL} />
            </div>
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.MD,
                  isGradient: true,
                  isDisabled: false,
                  isLoading: false,
                }}
              />
              <UiButton title="Connect" size={BUTTON_SIZES.MD} />
            </div>
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.MD,
                  isGradient: false,
                  isDisabled: false,
                  isLoading: false,
                }}
              />
              <UiButton
                title="Cancel"
                size={BUTTON_SIZES.MD}
                isGradient={false}
              />
            </div>
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.XL,
                  isGradient: true,
                  isDisabled: false,
                  isLoading: false,
                }}
              />
              <UiButton title="BUY" size={BUTTON_SIZES.XL} />
            </div>
            <div className="flex flex-col gap-2">
              <RenderOptions
                options={{
                  size: BUTTON_SIZES.LG,
                  isGradient: true,
                  isDisabled: false,
                  isLoading: false,
                }}
              />
              <UiButton title="Process to payment" size={BUTTON_SIZES.LG} />
            </div>
          </div>
        </div>
      </div>
    )
  }

  const CardShowcase = () => {
    const nftCards = [
      {
        title: 'CryptoDunk',
        image: 'https://picsum.photos/300/300?random=1',
        size: CARD_SIZES.NORMAL,
        price: 'Floor Price 1050.00 OFT',
        variant: 'default' as const,
        aspectRatio: CARD_RATIOS.square,
      },
      {
        title: 'Metaverse Land Sales Soar: A New Digital Gold Rush?',
        image: 'https://picsum.photos/300/300?random=1',
        size: CARD_SIZES.LG,
        variant: 'default' as const,
        aspectRatio: CARD_RATIOS.landscape,
        description:
          'The virtual real estate market is experiencing unprecedented growth, with digital land plots fetching millions. Experts weigh in on whether this is a sustainable',
        price: '2024-07-14',
      },
      {
        title: 'CryptoDunk',
        image: 'https://picsum.photos/300/300?random=1',
        size: CARD_SIZES.MD,
        price: 'Floor Price 1050.00 OFT',
        variant: 'default' as const,
        aspectRatio: CARD_RATIOS.square,
      },
    ]

    const basicCards = [
      {
        title: 'What is an NFT',
        image: 'https://picsum.photos/200/150?random=10',
      },
      {
        title: 'What is minting',
        image: 'https://picsum.photos/200/150?random=11',
      },
      {
        title: 'How to stay protected in Web3',
        image: 'https://picsum.photos/200/150?random=12',
      },
      {
        title: 'What is a DeFi Wallet',
        image: 'https://picsum.photos/200/150?random=13',
      },
    ]

    return (
      <div className="min-h-screen flex flex-col flex-1 bg-black p-6">
        <div className="mx-auto flex flex-col flex-1">
          {/* Main NFT Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-8 gap-20 mb-12">
            {nftCards.map((cardData, i) => {
              return (
                <UiCard
                  key={i}
                  {...cardData}
                  isSelected={i === 0} // First card selected for demo
                  imagePriority={true} // Prioritize first 4 images for faster loading
                  imageSizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
                />
              )
            })}
          </div>

          {/* Basic NFT Section */}
          <div className="space-y-4 flex flex-col flex-1">
            <h2 className="text-white text-xl font-semibold">Basic NFT</h2>
            <p className="text-gray-400 text-sm">
              Let&apos;s learn about NFT and Web3
            </p>

            <div className="flex flex-1 flex-wrap gap-4 max-w-full">
              {basicCards.map((card, i) => (
                <UiCard
                  key={i}
                  title={card.title}
                  image={card.image}
                  variant=""
                  size={CARD_SIZES.SM}
                  aspectRatio={CARD_RATIOS.landscape}
                  onClick={() =>
                    console.log(`Clicked basic card: ${card.title}`)
                  }
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const RenderInput = () => {
    // Example usage with react-hook-form
    const InputExamples: React.FC = () => {
      const SearchIcon = () => (
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      )

      const EmailIcon = () => (
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
          />
        </svg>
      )

      const EyeIcon = () => (
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
          />
        </svg>
      )

      return (
        <div className="min-h-screen bg-black p-8">
          <div className="max-w-2xl mx-auto space-y-8">
            <h1 className="text-white text-2xl font-bold mb-8">
              Input Component Examples
            </h1>

            {/* Search Input (matches the image) */}
            <div className="space-y-4">
              <h2 className="text-white text-lg font-semibold">Search Input</h2>
              <UiInput
                variant={INPUT_VARIANTS.SEARCH}
                placeholder="Filter by keyword, tag..."
                leftIcon={<SearchIcon />}
                className="max-w-md"
              />
            </div>

            {/* Form Inputs */}
            <div className="space-y-4">
              <h2 className="text-white text-lg font-semibold">Form Inputs</h2>

              <UiInput
                label="Email Address"
                type="email"
                placeholder="Enter your email"
                leftIcon={<EmailIcon />}
                helperText="We'll never share your email with anyone else."
                isRequired
              />

              <UiInput
                label="Password"
                type="password"
                placeholder="Enter your password"
                rightIcon={<EyeIcon />}
                error="Password must be at least 8 characters"
              />

              <UiInput
                label="Search Users"
                variant={INPUT_VARIANTS.SEARCH}
                placeholder="Type to search..."
                leftIcon={<SearchIcon />}
                isLoading
              />
            </div>

            {/* Different Variants */}
            <div className="space-y-4">
              <h2 className="text-white text-lg font-semibold">Variants</h2>
              <UiInput
                variant={INPUT_VARIANTS.DEFAULT}
                placeholder="Default variant"
              />
              <UiInput
                variant={INPUT_VARIANTS.OUTLINE}
                placeholder="Outline variant"
              />
              <UiInput
                variant={INPUT_VARIANTS.FILLED}
                placeholder="Filled variant"
              />
            </div>

            {/* Different Sizes */}
            <div className="space-y-4">
              <h2 className="text-white text-lg font-semibold">Sizes</h2>

              <UiInput
                size={INPUT_SIZES.SM}
                placeholder="Small size"
                leftIcon={<SearchIcon />}
              />

              <UiInput
                size={INPUT_SIZES.MD}
                placeholder="Medium size (default)"
                leftIcon={<SearchIcon />}
              />

              <UiInput
                size={INPUT_SIZES.LG}
                placeholder="Large size"
                leftIcon={<SearchIcon />}
              />
            </div>

            {/* With Addons */}
            <div className="space-y-4">
              <h2 className="text-white text-lg font-semibold">With Addons</h2>
              <UiInput
                label="Price"
                placeholder="0.00"
                leftAddon={<span className="text-gray-400">$</span>}
                rightAddon={<span className="text-gray-400 text-sm">USD</span>}
              />
            </div>

            {/* States */}
            <div className="space-y-4">
              <h2 className="text-white text-lg font-semibold">States</h2>
              <UiInput placeholder="Loading state" isLoading />
              <UiInput placeholder="Disabled state" isDisabled />
              <UiInput
                placeholder="Error state"
                error="This field is required"
              />
            </div>
          </div>
        </div>
      )
    }

    // React Hook Form integration example
    const ReactHookFormExample: React.FC = () => {
      const {
        register,
        formState: { errors },
      } = useForm()

      return (
        <div className="bg-black p-8">
          <div className="max-w-md mx-auto">
            <h2 className="text-white text-xl font-bold mb-6">
              React Hook Form Integration
            </h2>

            {/* Example usage with react-hook-form */}
            <div className="space-y-4">
              <UiInput
                label="Email"
                type="email"
                placeholder="Enter your email"
                registration={register('email', {
                  required: 'Email is required',
                })}
                error={errors.email?.message as string}
              />

              <UiInput
                label="Search"
                variant={INPUT_VARIANTS.SEARCH}
                placeholder="Filter by keyword, tag..."
                // registration={register('search')}
              />
            </div>

            <div className="mt-6 p-4 bg-gray-800 rounded-lg">
              <h3 className="text-white font-semibold mb-2">
                Usage with react-hook-form:
              </h3>
              <pre className="text-gray-300 text-sm overflow-x-auto">
                {`const { register, formState: { errors } } = useForm();

                  <Input
                    label="Email"
                    registration={register('email', {
                      required: 'Email is required'
                    })}
                    error={errors.email?.message}
                  />`}
              </pre>
            </div>
          </div>
        </div>
      )
    }

    return (
      <>
        <InputExamples />
        <ReactHookFormExample />
      </>
    )
  }

  const RenderSelect = () => {
    const [singleValue, setSingleValue] = useState<string | number>('')
    const [multiValue, setMultiValue] = useState<(string | number)[]>([])
    const [searchValue, setSearchValue] = useState<string | number>('')

    const basicOptions: SelectOption[] = [
      { value: 'react', label: 'React' },
      { value: 'vue', label: 'Vue.js' },
      { value: 'angular', label: 'Angular' },
      { value: 'svelte', label: 'Svelte', disabled: true },
    ]

    const categoryOptions: SelectOption[] = [
      {
        value: 'frontend',
        label: 'Frontend Development',
        description: 'User interface and client-side development',
        icon: <div className="w-4 h-4 bg-blue-500 rounded"></div>,
      },
      {
        value: 'backend',
        label: 'Backend Development',
        description: 'Server-side and database development',
        icon: <div className="w-4 h-4 bg-green-500 rounded"></div>,
      },
      {
        value: 'mobile',
        label: 'Mobile Development',
        description: 'iOS and Android applications',
        icon: <div className="w-4 h-4 bg-purple-500 rounded"></div>,
      },
      {
        value: 'devops',
        label: 'DevOps',
        description: 'Deployment and infrastructure',
        icon: <div className="w-4 h-4 bg-orange-500 rounded"></div>,
      },
    ]

    const UserIcon = () => (
      <svg
        className="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
        />
      </svg>
    )

    return (
      <div className="min-h-screen bg-black p-8">
        <div className="max-w-2xl mx-auto space-y-8">
          <h1 className="text-white text-2xl font-bold mb-8">
            Select Component Examples
          </h1>

          {/* Basic Select */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">Basic Select</h2>
            <UiSelect
              label="Choose Framework"
              options={basicOptions}
              value={singleValue}
              onChange={(value) => setSingleValue(value as string)}
              placeholder="Select a framework..."
              helperText="Choose your preferred frontend framework"
            />
          </div>

          {/* Searchable Select */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">
              Searchable Select
            </h2>
            <UiSelect
              label="Development Category"
              options={categoryOptions}
              value={searchValue}
              onChange={(value) => setSearchValue(value as string)}
              isSearchable
              placeholder="Search categories..."
              leftIcon={<UserIcon />}
            />
          </div>

          {/* Multi-Select */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">Multi-Select</h2>
            <UiSelect
              label="Skills"
              options={categoryOptions}
              value={multiValue}
              onChange={(value) => setMultiValue(value as (string | number)[])}
              isMultiple
              isSearchable
              isClearable
              placeholder="Select multiple skills..."
            />
          </div>

          {/* Different Variants */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">Variants</h2>

            <UiSelect
              variant={INPUT_VARIANTS.SEARCH}
              options={basicOptions}
              placeholder="Search variant"
              isSearchable
            />

            <UiSelect
              variant={INPUT_VARIANTS.OUTLINE}
              options={basicOptions}
              placeholder="Outline variant"
            />

            <UiSelect
              variant={INPUT_VARIANTS.FILLED}
              options={basicOptions}
              placeholder="Filled variant"
            />
          </div>

          {/* Different Sizes */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">Sizes</h2>

            <UiSelect
              size={INPUT_SIZES.SM}
              options={basicOptions}
              placeholder="Small size"
            />

            <UiSelect
              size={INPUT_SIZES.MD}
              options={basicOptions}
              placeholder="Medium size (default)"
            />

            <UiSelect
              size={INPUT_SIZES.LG}
              options={basicOptions}
              placeholder="Large size"
            />
          </div>

          {/* States */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">States</h2>

            <UiSelect
              options={basicOptions}
              placeholder="Loading state"
              isLoading
            />

            <UiSelect
              options={basicOptions}
              placeholder="Disabled state"
              isDisabled
            />

            <UiSelect
              options={basicOptions}
              placeholder="Error state"
              error="This field is required"
            />
          </div>

          {/* React Hook Form Integration */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">
              React Hook Form Integration
            </h2>
            <div className="p-4 bg-gray-800 rounded-lg">
              <h3 className="text-white font-semibold mb-2">
                Usage with react-hook-form:
              </h3>
              <pre className="text-gray-300 text-sm overflow-x-auto">
                {`const { register, formState: { errors } } = useForm();
                  <Select
                    label="Framework"
                    options={options}
                    registration={register('framework', {
                      required: 'Please select a framework'
                    })}
                    error={errors.framework?.message}
                  />`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const RenderInputNumber = () => {
    const [quantity, setQuantity] = useState<number | undefined>(1)
    const [price, setPrice] = useState<number | undefined>(99.99)
    // const [percentage, setPercentage] = useState<number | undefined>(50)

    return (
      <div className="min-h-screen bg-black p-8">
        <div className="max-w-2xl mx-auto space-y-8">
          <h1 className="text-white text-2xl font-bold mb-8">
            Horizontal Number Input Examples
          </h1>

          {/* Basic Horizontal Layout */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">
              Basic Quantity Selector
            </h2>
            <UiNumberInput
              label="Quantity"
              value={quantity}
              onChange={setQuantity}
              min={0}
              max={10}
              step={1}
            />
            <p className="text-gray-400 text-sm">
              Current quantity: {quantity}
            </p>
          </div>

          {/* Price Input */}
          <div className="space-y-4">
            <h2 className="text-white text-lg font-semibold">Price Input</h2>
            <UiNumberInput
              label="Price"
              value={price}
              onChange={setPrice}
              min={0}
              step={0.01}
              precision={2}
              prefix="$"
              className="max-w-xs"
            />
            <p className="text-gray-400 text-sm">
              Current price: ${price?.toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    )
  }

  const RenderModal = () => {
    const [isOpen, setIsOpen] = useState<boolean>(false)
    const [isOpenModal, setIsOpenModal] = useState<boolean>(false)

    return (
      <>
        <div className="flex flex-1 flex-col gap-4">
          <UiButton
            title={'Open Modal failed'}
            handleClick={() => {
              setIsOpen(true)
            }}
          />
          <UiButton
            title={'Open Modal success'}
            handleClick={() => {
              setIsOpenModal(true)
            }}
          />
        </div>
        <UiModal
          isOpen={isOpen}
          header={'Payment confirmation'}
          onClose={() => setIsOpen(false)}
        >
          <UiStatusContent status={PAYMENT_STATUS.FAILED} />
        </UiModal>
        <UiModal
          isOpen={isOpenModal}
          header={'Payment confirmation'}
          onClose={() => setIsOpenModal(false)}
        >
          <UiStatusContent status={PAYMENT_STATUS.SUCCESS} />
        </UiModal>
      </>
    )
  }

  const RenderScroll = () => {
    interface Post {
      id: number
      title: string
      content: string
      author: string
      createdAt: string
    }

    interface Product {
      id: number
      name: string
      price: number
      category: string
      rating: number
      image: string
    }

    const postsQuery = useAppInfiniteQuery({
      queryKey: ['posts'],
      queryFn: fetchPosts,
      getNextPageParam: (lastPage: any) => lastPage.nextCursor,
    })

    const productsQuery = useAppInfiniteQuery({
      queryKey: ['products'],
      queryFn: fetchProducts,
      getNextPageParam: (lastPage: any) => lastPage.nextCursor,
    })

    const emptyQuery = useAppInfiniteQuery({
      queryKey: ['empty'],
      queryFn: async () => ({ data: [], hasMore: false }),
      getNextPageParam: () => undefined,
    })

    // Error state query
    const errorQuery = {
      ...postsQuery,
      isError: true,
      isLoading: false,
      data: null,
    }

    const renderPost = (item: Post, index: number): ReactNode => (
      <div
        key={`post-${index}`}
        className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors"
      >
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-lg font-semibold text-white">{item.title}</h3>
          <span className="text-xs text-gray-500 whitespace-nowrap ml-4">
            {new Date(item.createdAt).toLocaleDateString()}
          </span>
        </div>
        <p className="text-gray-300 mb-4 leading-relaxed">{item.content}</p>
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-semibold">
              {item.author.charAt(item.author.length - 1)}
            </span>
          </div>
          <span className="text-sm text-gray-400">{item.author}</span>
        </div>
      </div>
    )

    const renderProduct = (product: Product, index: number) => (
      <div
        key={`product-${index}`}
        className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700 hover:border-gray-600 transition-colors group"
      >
        <div className="aspect-square bg-gray-700 relative overflow-hidden">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-2 right-2 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
            <div className="flex items-center space-x-1">
              <svg
                className="w-3 h-3 text-yellow-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span className="text-white text-xs">{product.rating}</span>
            </div>
          </div>
        </div>
        <div className="p-4">
          <h3 className="text-white font-semibold mb-1">{product.name}</h3>
          <p className="text-gray-400 text-sm mb-2">{product.category}</p>
          <div className="flex items-center justify-between">
            <span className="text-green-400 font-bold">${product.price}</span>
            <button className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
              Add to Cart
            </button>
          </div>
        </div>
      </div>
    )

    const CustomSkeleton = () => (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 animate-pulse">
        <div className="flex justify-between mb-3">
          <div className="bg-gray-600 h-5 rounded w-2/3"></div>
          <div className="bg-gray-600 h-4 rounded w-20"></div>
        </div>
        <div className="space-y-2 mb-4">
          <div className="bg-gray-600 h-4 rounded w-full"></div>
          <div className="bg-gray-600 h-4 rounded w-4/5"></div>
        </div>
        <div className="flex items-center space-x-3">
          <div className="bg-gray-600 w-8 h-8 rounded-full"></div>
          <div className="bg-gray-600 h-4 rounded w-20"></div>
        </div>
      </div>
    )

    const ProductSkeleton = () => (
      <div className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700 animate-pulse">
        <div className="aspect-square bg-gray-600"></div>
        <div className="p-4">
          <div className="bg-gray-600 h-5 rounded w-3/4 mb-2"></div>
          <div className="bg-gray-600 h-4 rounded w-1/2 mb-3"></div>
          <div className="flex items-center justify-between">
            <div className="bg-gray-600 h-5 rounded w-16"></div>
            <div className="bg-gray-600 h-8 rounded w-20"></div>
          </div>
        </div>
      </div>
    )
    return (
      <div className="min-h-screen bg-black p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-white mb-8">
            Infinite Scroll Examples
          </h1>

          {/* Basic List */}
          <div className="mb-12">
            <h2 className="text-xl font-semibold text-white mb-4">
              Basic List
            </h2>
            <div className="h-96 border border-gray-700 rounded-lg overflow-hidden">
              <UiInfiniteScroll
                query={postsQuery}
                renderItem={renderPost}
                keyExtractor={(post: Post) => post.id.toString()}
                skeletonComponent={<CustomSkeleton />}
                className="h-full"
                containerClassName="p-4 space-y-4"
                enablePullToRefresh
                onRefresh={() => console.log('Refreshing posts...')}
              />
            </div>
          </div>

          {/* Grid Layout */}
          <div className="mb-12">
            <h2 className="text-xl font-semibold text-white mb-4">
              Grid Layout - Products
            </h2>
            <div className="h-96 border border-gray-700 rounded-lg overflow-hidden">
              <UiInfiniteScroll
                query={productsQuery}
                renderItem={renderProduct}
                keyExtractor={(product: Product) => product.id.toString()}
                cols={3}
                gap="gap-4"
                className="h-full"
                containerClassName="p-4"
                skeletonComponent={<ProductSkeleton />}
              />
            </div>
          </div>

          {/* Empty State Example */}
          <div className="mb-12">
            <h2 className="text-xl font-semibold text-white mb-4">
              Empty State
            </h2>
            <div className="h-64 border border-gray-700 rounded-lg">
              <UiInfiniteScroll
                query={emptyQuery}
                renderItem={renderPost}
                keyExtractor={(post: Post) => post.id.toString()}
                emptyTitle="No posts found"
                emptyDescription="There are no posts to display at the moment."
              />
            </div>
          </div>

          {/* Error State Example */}
          <div className="mb-12">
            <h2 className="text-xl font-semibold text-white mb-4">
              Error State
            </h2>
            <div className="h-64 border border-gray-700 rounded-lg">
              <UiInfiniteScroll
                query={errorQuery as UseInfiniteQueryResult<any, Error>}
                renderItem={renderPost}
                keyExtractor={(post: Post) => post.id.toString()}
                errorTitle="Failed to load posts"
                errorDescription="Something went wrong while fetching the posts."
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  const RenderTabs = () => {
    const exampleTabs: TabItem[] = [
      {
        id: 'scroll',
        label: 'InfiniteScroll',
        content: <RenderScroll />,
      },
      {
        id: 'modal',
        label: 'Modal',
        content: <RenderModal />,
      },
      {
        id: 'input_number',
        label: 'Input Number',
        content: <RenderInputNumber />,
      },
      {
        id: 'select',
        label: 'Select',
        content: <RenderSelect />,
      },
      {
        id: 'input',
        label: 'Input',
        content: <RenderInput />,
      },
      {
        id: 'card',
        label: 'Cards',
        content: <CardShowcase />,
      },
      {
        id: 'button',
        label: 'Buttons',
        content: <RenderButton />,
      },
      // {
      //   id: 'settings',
      //   label: 'Settings',
      //   content: (
      //     <div className="p-4 bg-gray-50 rounded-lg">
      //       <h3 className="text-lg font-semibold mb-2">Settings Content</h3>
      //       <p className="text-gray-600">Configure your preferences here.</p>
      //     </div>
      //   ),
      // },
      // {
      //   id: 'disabled',
      //   label: 'Disabled',
      //   content: <div>This content should not be visible</div>,
      //   disabled: true,
      // },
    ]
    return (
      <div className="text-white">
        <div className="flex">
          <UiTabs
            tabs={exampleTabs}
            contentClassName={'px-4'}
            variant="underline"
          />
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col flex-1 gap-4">
      <RenderTabs />
    </div>
  )
}

export default React.memo(ExamplePage)
