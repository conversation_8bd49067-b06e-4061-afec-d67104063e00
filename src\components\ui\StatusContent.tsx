import { PAYMENT_STATUS } from '@/constants'
import React, { useMemo } from 'react'
import { MdOutlineCheckCircle } from 'react-icons/md'
import { CgDanger } from 'react-icons/cg'
import { useTranslate } from '@/hooks'

interface IStatusContent {
  status: PAYMENT_STATUS
  errorMessage?: string
}
interface IStatusItem {
  status: PAYMENT_STATUS
  icon: React.ReactNode
  content: string
}
const UiStatusContent = ({ status, errorMessage }: IStatusContent) => {
  const trans = useTranslate()
  const currentStatus = useMemo<IStatusItem>(() => {
    const statuses: IStatusItem[] = [
      {
        status: PAYMENT_STATUS.SUCCESS,
        icon: <MdOutlineCheckCircle color={'#00FF00'} size={'9.375rem'} />,
        content: trans.payment_success,
      },
      {
        status: PAYMENT_STATUS.FAILED,
        icon: <CgDanger color={'#F2C157'} size={'9.375rem'} />,
        content: trans.transaction_fail,
      },
      {
        status: PAYMENT_STATUS.CANCEL,
        icon: <CgDanger color={'#F2C157'} size={'9.375rem'} />,
        content: trans.transaction_fail,
      },
    ]
    return statuses.find((s: IStatusItem) => s.status === status) as IStatusItem
  }, [status, trans])

  return (
    <div className="flex flex-col flex-1 gap-12">
      <div className="flex justify-center items-center">
        {currentStatus.icon}
      </div>
      <div className="flex flex-col flex-1 font-normal text-[14px] leading-[170%] text-center tracking-[0.02em] font-montserrat">
        <div className="text-white">{currentStatus.content}</div>
        {errorMessage && <div className="text-red-400">{errorMessage}</div>}
      </div>
    </div>
  )
}

export default React.memo(UiStatusContent)
