import Link from 'next/link'
import { memo } from 'react'
import Image from 'next/image'
import { useTranslate } from '@/hooks/useTranslate'
import { IMAGE } from '@/utils/string'

const Footer: React.FC = (): JSX.Element => {
  const trans = useTranslate()

  const navItems = [
    { title: trans.footer_termsOfUse, url: '/terms-of-use' },
    { title: trans.footer_privacyPolicy, url: '/privacy-policy' },
    { title: trans.footer_contact, url: '/contact' },
  ]

  return (
    <footer className="mt-[80px] flex sm:flex-row flex-col justify-between items-center bg-getCardBg py-6 lg:px-24 md:px-12 sm:px-6 px-3 font-montserrat font-normal text-[12px] leading-[140%] tracking-[0.02em] sm:h-[182px] w-full">
      <p className="max-lg:text-lg max-lg:justify-start text-base max-sm:text-sm text-neutral-400 sm:mb-0 mb-4 text-white/50">
        {trans.footer_copyright}
      </p>
      <div className="flex sm:flex-row flex-col justify-center items-center sm:space-x-12 sm:space-y-0 space-y-4 text-white">
        {navItems.map((item) => (
          <Link href={item.url} key={item.title}>
            {item.title}
          </Link>
        ))}
        <Link href={process.env.NEXT_PUBLIC_GET_X as string} target="_blank">
          <Image
            src={IMAGE.logoX}
            alt="logo"
            width={22}
            height={22}
            className=""
          />
        </Link>
      </div>
    </footer>
  )
}

export default memo(Footer)
