import { useRouter } from 'next/router'
import React from 'react'
import { HiOutlineArrowLeft } from 'react-icons/hi'

import ButtonComponent from './Button'
import clsx from 'clsx'
import { BUTTON } from '@/utils/string'

function ButtonBack({
  className,
  onClick,
}: {
  className?: string
  onClick?: () => void
}) {
  const router = useRouter()
  const handleBack = () => {
    onClick ? onClick() : router.back()
  }
  return (
    <ButtonComponent
      title={BUTTON.back}
      type="default"
      beforeIcon={<HiOutlineArrowLeft className="h-5 w-4" />}
      className={clsx(
        'flex gap-3 items-center min-w-fit px-6 !font-bold hover:opacity-70 rounded text-white !border-[#FFFFFF26] h-8',
        className && className
      )}
      onClick={handleBack}
    />
  )
}

export default ButtonBack
