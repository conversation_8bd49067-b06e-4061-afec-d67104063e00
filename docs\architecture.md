# Frontend Architecture Documentation

## Overview

This project is built using Next.js 13 with TypeScript, following a modular and scalable architecture. The application is organized into clear layers with separation of concerns, making it maintainable and extensible.

## Technology Stack

- Node.js >= 18.14.0
- TypeScript
- Framework: Next.js 13
- UI Library: Tailwind CSS
- State Management: Zustand
- Routing: Next.js 13 Page Router
- Internationalization: Custom hooks and static JSON (Japanese supported)
- Data Fetching: React Query
- Form Handling: React Hook Form
- API requests: Axios
- Form Validation: Zod

## Project Structure

```
fe/
├── src/
│   ├── components/         # Reusable UI and feature components
│   ├── constants/          # App-wide constants
│   ├── helpers/            # Utility functions
│   ├── hooks/              # Custom React hooks
│   ├── icons/              # Icon components
│   ├── interfaces/         # TypeScript interfaces
│   ├── pages/              # Next.js 13 pages (legacy, see below)
│   ├── services/           # API services, cookies, and HTTP client
│   ├── store/              # Zustand state management
│   ├── styles/             # SCSS and Tailwind CSS
│   ├── utils/              # Utility functions
│   └── ...
├── public/                 # Static assets (images, locales, etc.)
├── docs/                   # Project documentation
├── package.json            # Project dependencies and scripts
├── README.md               # Project documentation
└── ...
```

### Key Differences from Next.js 15+ Example

- **App Directory**: This project uses the `pages/` directory (Next.js 13 Pages Router), not the new `app/` directory (App Router).
- **State Management**: Uses Zustand instead of React Context API.
- **Internationalization**: Uses custom hooks and static JSON for Japanese, not Next.js i18n or next-intl.
- **No shadcn/ui or Lucide Icons**: Uses custom and Ant Design components, and local icon files.
- **No React Server Components**: All components are client-side.

## Components Directory Structure

The `src/components/` folder is the central place for all reusable and feature-specific React components in the project. It is organized to promote modularity, reusability, and clear separation of concerns. Each subfolder groups related components by their function or domain.

**Main subfolders:**

- `ui/` – Atomic, reusable UI building blocks (buttons, cards, modals, tabs, etc.).
- `buttons/` – Button-related components and variants.
- `carousel/` – Carousel and slider components.
- `common/` – Shared utility components (e.g., loading indicators, overlays, pagination).
- `formItem/` – Form item wrappers and helpers.
- `inputs/` – Input field components (text, upload, custom inputs).
- `layouts/` – Layout and navigation components (headers, footers, sidebars, nav bars).
- `modal/` – Modal dialog components for various use cases.
- `nftComponents/` – Components specific to NFT display and interaction.
- `rootModal/` – Root-level modal management and modal layout components.
- `tag/` – Tag and tag input components.
- `texts/` – Text display components (titles, errors, information, etc.).

This structure makes it easy to locate, maintain, and extend components as the application grows. For details on each type of component, see the following sections on UI and feature components.

---

### 1. UI Components (`src/components/ui/`)

The `src/components/ui/` folder contains the project's atomic, reusable UI building blocks. These components are designed for flexibility, accessibility, and composability, and are used throughout the application and in feature components.

#### Main Components Overview

| Component        | Description                                                                         |
| ---------------- | ----------------------------------------------------------------------------------- |
| `Button`         | Flexible button with gradient, size, loading, and disabled states.                  |
| `Card`           | Display container for content, images, and actions, with variants and size options. |
| `Modal`          | Accessible modal dialog with customizable header, actions, and content.             |
| `Tabs`           | Tabbed navigation with variants (default, pills, underline) and size options.       |
| `Select`         | Custom select/dropdown supporting search, multi-select, clearable, and variants.    |
| `Input`          | Text input with variants, sizes, icons, addons, and form integration.               |
| `InputNumber`    | Numeric input with increment/decrement controls, formatting, and validation.        |
| `Spinner`        | Animated loading spinner with gradient support.                                     |
| `InfiniteScroll` | Virtualized infinite scroll list/grid with loading, error, and empty states.        |
| `StatusContent`  | Status display for payment or transaction results, with icons and messages.         |

## Constants Directory Structure

The `src/constants/` folder contains all application-wide constants, enums, configuration values, and static data used throughout the project. Centralizing these values helps ensure consistency and makes it easy to update shared settings.

**Main files:**

- `admin.ts` – Admin-related constants.
- `apiUrls.ts` – API endpoint URLs.
- `auth.ts` – Authentication-related constants.
- `common.ts` – General-purpose constants used across the app.
- `enum.ts` – Enumerations for various domains.
- `errors.ts` – Error code and message constants.
- `header.ts` – Header and navigation constants.
- `httpRequest.ts` – HTTP request configuration constants.
- `index.ts` – Central export for constants.
- `routes.ts` – Route path constants.
- `socket.ts` – WebSocket event/channel constants.
- `status.ts` – Status and state constants.
- `storage.ts` – Local storage and session storage keys.
- `type.ts` – Type definitions for constants.

## Interfaces Directory Structure

The `src/interfaces/` folder contains all TypeScript type definitions and interfaces used throughout the application. Centralizing interfaces ensures type safety, consistency, and easier refactoring across the codebase.

**Main files:**

- `common.ts` – Shared/common types (e.g., modal state, icon props, search types).
- `user.ts` – User-related interfaces (user details, user lists).
- `auth.ts` – Authentication and role types, error response interfaces.
- `nftComponents.ts` – NFT-related component props and types.
- `connectItems.ts` – Types for wallet/connect UI items.
- `banner.ts` – Banner/NFT banner types (creation, listing, detail).
- `news.ts` – News and news list types.
- `socket.ts` – Types for WebSocket events and payloads.

**Usage:**

- Import interfaces to type component props, API responses, and state slices.
- Promotes strong typing and maintainability across the app.

**Example:**

```ts
import { UserDetail } from '@/interfaces'

const user: UserDetail = { ... }
```

---

## Pages Directory Structure

The `src/pages/` folder contains all route-based React components, following the Next.js 13 Pages Router convention. Each file or subfolder corresponds to a route in the application.

**Main structure:**

- Each route is a `.page.tsx` file or a folder with an `index.page.tsx` file.
- Supports nested routes (e.g., `/admin/users/[id]/index.page.tsx`).
- Special files: `_app.page.tsx` (custom App), `_document.tsx` (custom Document), error pages (`404/`, `405/`).

**Usage:**

- Each page exports a React component for that route.
- Pages can import and compose feature/UI components, hooks, and state.
- Dynamic imports are used for code splitting on heavy/conditional components.

**Example:**

```tsx
// src/pages/my-profile/index.page.tsx
import MyProfile from './MyProfile'
import MyNFTs from './MyNFTs'
import MyTransactionHistory from './MyTransactionHistory'

export default function MyProfilePage() {
  return (
    <div>
      <MyProfile />
      <MyNFTs />
      <MyTransactionHistory />
    </div>
  )
}
```

---

## Services Directory Structure

The `src/services/` folder contains all logic for API communication, cookies, and HTTP client configuration. It is organized by domain and responsibility.

**Main subfolders:**

- `apiCall/` – API service modules, organized by domain (e.g., `auth.ts`, `users.ts`, `banner.ts`, `nft.ts`). Each file exports async functions for API endpoints.
- `cookies/` – Cookie management utilities (set, get, remove cookies, storage keys).

**HTTP Client:**

- `apiCall/baseApi.ts` defines a custom Axios instance with a global signature header, token injection, and error handling (including auto-logout on 401).
- All API calls use this client for consistency and centralized error/auth handling.

**Usage:**

- Import API functions in components, hooks, or state slices to fetch or mutate data.
- Use cookie utilities for authentication/session management.

**Example:**

```ts
import { getUserInfo } from '@/services/apiCall/users'
const user = await getUserInfo()

import { setCookie } from '@/services/cookies'
setCookie('token', 'abc123')
```

## Hooks Directory Structure

The `src/hooks/` folder contains all custom React hooks used throughout the application. These hooks encapsulate reusable logic for state, effects, and utilities, promoting DRY principles and improving code maintainability.

**Main files:**

- `index.ts` – Central export for all hooks in the directory.
- `useClickOutSide.ts` – Detects clicks outside a specified element, commonly used for closing dropdowns or modals.
- `useConnectWallet.ts` – Handles logic for connecting to a crypto wallet (e.g., MetaMask), including connection state and events.
- `useMetaMaskError.ts` – Provides error handling and user-friendly messages for MetaMask-related issues.
- `useNetworkData.ts` – Retrieves and manages blockchain network data, such as network ID and status.
- `useSocketUserBuyByCredit.ts` – Manages WebSocket events related to user purchases by credit.
- `useTableHeight.ts` – Dynamically calculates and manages table height for responsive layouts.
- `useTranslate.ts` – Returns translation functions or objects for internationalization, using static JSON files.
- `useViewport.ts` – Detects and provides viewport size and breakpoints for responsive design.

#### Design Patterns & Best Practices

- **Single Responsibility:** Each hook should encapsulate a single piece of logic or concern.
- **Reusability:** Hooks are designed to be reused across multiple components and features.
- **Composability:** Hooks can be composed together to build more complex logic.
- **Naming Convention:** All custom hooks are prefixed with `use` and named according to their purpose.
- **Type Safety:** Hooks leverage TypeScript for strong typing of inputs and outputs.

This structure ensures that shared logic is easy to find, test, and maintain, and that components remain focused on rendering and UI concerns.

## Helpers Directory Structure

The `src/helpers/` folder contains utility functions and helper modules that provide reusable logic for the application. These helpers are designed to be stateless and can be imported wherever needed.

**Main files:**

- `index.ts` – Central export for helpers.
- `wagmiClient.ts` – Helper for configuring and managing the Wagmi client (Web3/Wallet integration).

#### Design Patterns

- **Props-driven**: All components accept props for customization (size, variant, icons, etc.).
- **Variants & Sizes**: Most components support multiple visual variants and sizes for design consistency.
- **Composition**: Components are composable and can be nested or used as building blocks in feature components.
- **Accessibility**: Keyboard navigation and ARIA attributes are considered in interactive components.
- **Form Integration**: Inputs and selects support React Hook Form integration via `registration` prop.

#### Example: Button Component

```tsx
// Usage of Button
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES } from '@/constants'
;<UiButton
  title="Connect Wallet"
  size={BUTTON_SIZES.NORMAL}
  isGradient={true}
  isDisabled={false}
  isLoading={false}
  handleClick={() => alert('Clicked!')}
/>
```

**Button Props:**

- `title`: string (button label)
- `size`: BUTTON_SIZES (SM, NORMAL, MD, LG, XL)
- `isGradient`: boolean (gradient background)
- `isDisabled`: boolean
- `isLoading`: boolean (shows spinner)
- `handleClick`: function

#### Example: Select Component

```tsx
import UiSelect, { SelectOption } from '@/components/ui/Select'

const options: SelectOption[] = [
  { value: 'react', label: 'React' },
  { value: 'vue', label: 'Vue.js' },
  { value: 'angular', label: 'Angular' },
]

<UiSelect
  label="Choose Framework"
  options={options}
  value={selectedValue}
  onChange={setSelectedValue}
  isSearchable
  isClearable
  error={errorMsg}
/>
```

**Select Props:**

- `options`: array of `{ value, label, ... }`
- `value`, `onChange`: controlled value and handler
- `isSearchable`, `isMultiple`, `isClearable`: feature flags
- `variant`, `size`: visual style and size
- `label`, `helperText`, `error`: form support
- `leftIcon`, `rightIcon`: icon slots

#### Other Notable Components

**Card**

```tsx
import UiCard from '@/components/ui/Card'
;<UiCard
  title="NFT Title"
  image="/path/to/image.jpg"
  description="NFT description"
  price="$100"
  variant="default"
  size="MD"
  onClick={() => alert('Card clicked!')}
/>
```

**Modal**

```tsx
import UiModal from '@/components/ui/Modal'
;<UiModal
  isOpen={isModalOpen}
  header="Confirm Action"
  onClose={() => setIsModalOpen(false)}
  confirmButton={{ isShow: true, title: 'Confirm', action: handleConfirm }}
  cancelButton={{ isShow: true, title: 'Cancel', action: handleCancel }}
>
  <div>Modal content goes here</div>
</UiModal>
```

**Tabs**

```tsx
import UiTabs, { TabItem } from '@/components/ui/Tabs'

const tabs: TabItem[] = [
  { id: 'tab1', label: 'Tab 1', content: <div>Content 1</div> },
  { id: 'tab2', label: 'Tab 2', content: <div>Content 2</div> },
]

<UiTabs tabs={tabs} variant="underline" size="md" />
```

**Input**

```tsx
import UiInput from '@/components/ui/Input'
;<UiInput
  label="Email"
  placeholder="Enter your email"
  leftIcon={<EmailIcon />}
  error={errorMsg}
  isRequired
/>
```

**InputNumber**

```tsx
import UiNumberInput from '@/components/ui/InputNumber'
;<UiNumberInput
  label="Quantity"
  value={quantity}
  onChange={setQuantity}
  min={1}
  max={10}
  step={1}
/>
```

**Spinner**

```tsx
import UiSpinner from '@/components/ui/Spinner'
;<UiSpinner size="h-6 w-6" strokeColorFrom="#F2AF36" strokeColorTo="#FAE475" />
```

**InfiniteScroll**

```tsx
import UiInfiniteScroll from '@/components/ui/InfiniteScroll'
;<UiInfiniteScroll
  query={postsQuery}
  renderItem={renderPost}
  keyExtractor={(item) => item.id}
  skeletonComponent={<CustomSkeleton />}
  emptyTitle="No posts found"
/>
```

**StatusContent**

```tsx
import UiStatusContent from '@/components/ui/StatusContent'
import { PAYMENT_STATUS } from '@/constants'
;<UiStatusContent status={PAYMENT_STATUS.SUCCESS} />
```

#### UI Showcase

A comprehensive UI showcase and documentation page is available in `src/components/ui/page.tsx`, demonstrating all UI components, their props, and usage patterns. This file serves as a living style guide and reference for developers.

---

### 2. Feature Components

Feature-specific components are organized by domain or feature area, e.g. `nftComponents/`, `modal/`, `layouts/`.

#### Example: NFT Information Component

```tsx
// src/components/nftComponents/NftInformation.tsx
import React from 'react'

interface NftInformationProps {
  name: string
  description: string
}

const NftInformation: React.FC<NftInformationProps> = ({
  name,
  description,
}) => (
  <div>
    <h2>{name}</h2>
    <p>{description}</p>
  </div>
)

export default NftInformation
```

## Utils Directory Structure

The `src/utils/` folder contains general-purpose utility functions and helpers used throughout the application. These utilities promote code reuse, simplify common operations, and help keep components and services focused on business logic.

**Main files:**

- `handleCookie.ts` – Functions for setting, getting, and deleting browser cookies (client-side only).
- `format.ts` – String, number, and data formatting utilities (e.g., shorten strings, format currency, sanitize HTML, Excel parsing, etc.).
- `regex.ts` – Common regular expressions and validation schemas (email, URL, numbers, contract addresses, etc.).
- `auth.ts` – Authentication helpers (logout, JWT parsing, role checks, URL validation by role).
- `date.ts` – Date/time formatting helpers using Moment.js.
- `countString.ts` – Utility for counting string length, including emoji-aware counting.
- `string.ts` – Static string constants, image paths, button labels, modal messages, and form messages.
- `type.ts` – Utility for static type-based constants (e.g., route pathnames for banners, news, NFTs).

**Usage:**

- Import utility functions to handle formatting, validation, cookies, and other cross-cutting concerns in components, hooks, and services.
- Keeps business logic clean and DRY by centralizing common helpers.

**Example:**

```ts
import { setCookie, getCookie } from '@/utils/handleCookie'
setCookie('token', 'abc123', 7)

import { shortenString, formatCurrency } from '@/utils/format'
const short = shortenString('0x1234567890abcdef')
const price = formatCurrency('10000')

import { schemaEmail } from '@/utils/regex'
const isValid = schemaEmail.test('<EMAIL>')

import { logout } from '@/utils/auth'
logout('user')

import { convertTimestampToDate } from '@/utils/date'
const date = convertTimestampToDate(Date.now())
```

## Page Architecture

Pages are organized in the `src/pages/` directory, following the Next.js 13 Pages Router convention. Each page is a React component exported from a `.page.tsx` file.

#### Example: User Profile Page

```tsx
// src/pages/my-profile/index.page.tsx
import React from 'react'
import MyProfile from './MyProfile'
import MyNFTs from './MyNFTs'
import MyTransactionHistory from './MyTransactionHistory'

export default function MyProfilePage() {
  // Tabs and logic omitted for brevity
  return (
    <div>
      <MyProfile />
      <MyNFTs />
      <MyTransactionHistory />
    </div>
  )
}
```

## State Management

This project uses Zustand for global state management. Slices are used to organize state by domain.

#### Example: Common State Slice

```ts
// src/store/slices/common.ts
import { StateCreator } from 'zustand'
import { ICommonState, ModalState } from '@/interfaces'

export const commonSlice: StateCreator<ICommonState> = (set) => ({
  role: null,
  modalType: null,
  modalTitle: null,
  message: null,
  status: null,
  isLoading: false,
  setRole(role) {
    set(() => ({ role }))
  },
  setLoading(type) {
    set(() => ({ isLoading: type }))
  },
  setModalType(data: ModalState) {
    set(() => ({
      modalType: data.type,
      modalTitle: data.title,
      message: data?.message,
      status: data?.status,
      event: data?.event,
    }))
  },
})
```

## API Implementation

API services are organized by domain in `src/services/apiCall/`. The HTTP client is a custom Axios instance with interceptors for authentication and error handling.

#### Example: User API Service

```ts
// src/services/apiCall/users.ts
import appRequest from './baseApi'

export const getUserInfo = async () => {
  return await appRequest.get('/user/info')
}
```

## Internationalization

- Japanese translations are stored in `public/locales/ja/` as JSON files.
- Use the `useTranslate` hook to access translations.

#### Example: Using Translations

```ts
// src/hooks/useTranslate.ts
import ja from 'public/locales/ja'
export const useTranslate = () => ja
```

## Styling

- Tailwind CSS is used for utility-first styling.
- Custom SCSS is used for global styles and variables (`src/styles/main.scss`).

## Best Practices

- Use TypeScript for type safety.
- Organize code by feature/domain.
- Use Zustand for global state, React hooks for local state.
- Use Tailwind CSS for styling.
- Keep components small and focused.
- Use aliases for imports (see `tsconfig.json`).
- Write meaningful commit messages (see `README.md`).

## Creating New Pages and Components

1. **Page**: Add a new file in `src/pages/[route]/index.page.tsx`.
2. **Component**: Add to `src/components/[feature]/[ComponentName].tsx`.
3. **State**: Add a new slice in `src/store/slices/` if needed.
4. **API**: Add to `src/services/apiCall/`.
5. **Hook**: Add new hook to `src/hooks`
6. **Types**: Add to `src/interfaces/` if needed
7. **Translations**: Add to `public/locales/ja/` and `public/locales/ja/` if needed.

This architecture ensures maintainability, scalability, and developer productivity for Next.js 13 projects using Zustand and Tailwind CSS.

## Guidelines for Importing and Using UI Components

The UI components in this project are designed for flexibility, reusability, and performance. Below are best practices and examples for importing and using these components, based on real usage in `src/components/ui/page.tsx`.

### Importing UI Components

#### Static Imports

Use static imports for components that are lightweight or used frequently throughout your app:

```tsx
import UiButton from '@/components/ui/Button'
import UiCard from '@/components/ui/Card'
```

#### Dynamic Imports

**Recommend**
For heavier components or those used conditionally (e.g., modals, infinite scroll), use Next.js dynamic imports to enable code splitting and improve performance:

```tsx
import dynamic from 'next/dynamic'
const UiModal = dynamic(() => import('@/components/ui/Modal'))
const UiInfiniteScroll = dynamic(() => import('@/components/ui/InfiniteScroll'))
```

### Using UI Components

- **Props-driven:** All UI components accept props for customization (size, variant, icons, etc.).
- **Composition:** Components can be composed together to build complex UIs. For example, you can use `UiButton` inside a modal, or pass custom icons to inputs and selects.
- **Type Safety:** Props are strongly typed with TypeScript for better developer experience.

#### Example Usage

```tsx
<UiButton title="Connect Wallet" size={BUTTON_SIZES.NORMAL} isGradient handleClick={onConnect} />

<UiCard title="NFT Title" image="/path/to/image.jpg" description="NFT description" price="$100" />

<UiModal isOpen={isOpen} header="Confirm Action" onClose={closeModal}>
  <div>Modal content goes here</div>
</UiModal>
```

#### Composing Components

You can nest and compose UI components to create rich interfaces:

```tsx
<UiModal isOpen={isOpen} onClose={closeModal}>
  <UiStatusContent status={PAYMENT_STATUS.SUCCESS} />
</UiModal>

<UiTabs tabs={tabItems} variant="underline" size="md" />
```

#### Dynamic Component Typing

When using dynamic imports with generic components (like InfiniteScroll), you may need to cast the component for proper typing:

```tsx
const DynamicInfiniteScroll = dynamic(
  () => import('@/components/ui/InfiniteScroll')
)
function UiInfiniteScroll<T>(props: InfiniteScrollProps<T>) {
  const TypedComponent = DynamicInfiniteScroll as React.ComponentType<
    InfiniteScrollProps<T>
  >
  return <TypedComponent {...props} />
}
```

### Best Practices

- Prefer static imports for commonly used, lightweight components.
- Use dynamic imports for large or rarely used components to optimize bundle size.
- Always pass required props and use TypeScript for type checking.
- Refer to `src/components/ui/page.tsx` for comprehensive usage examples and patterns.

This approach ensures your UI remains modular, performant, and easy to maintain as your application grows.
