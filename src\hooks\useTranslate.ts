import { useEffect, useState } from 'react'
import ja from 'public/locales/ja'
import en from 'public/locales/en'
import { getCurrentLanguage } from '@/utils/language'

export const useTranslate = () => {
  const [locale, setLocale] = useState('en')

  useEffect(() => {
    // Get language from localStorage or default to English
    const storedLang = getCurrentLanguage()
    setLocale(storedLang)
  }, [])

  return locale === 'en' ? en : ja
}
