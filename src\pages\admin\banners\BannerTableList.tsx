import React, { useEffect, useMemo, useState } from 'react'
import { Empty, Input, RowProps, Table, Tooltip } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import type { TableRowSelection } from 'antd/es/table/interface'
import { HiOutlineTrash } from 'react-icons/hi'
import { useRouter } from 'next/router'
import Image from 'next/image'
import { toast } from 'react-toastify'
import { useMutation } from '@tanstack/react-query'

import { IconPen, IconSearch, IconSortDropDrag } from '@/icons'
import UiModal from '@/components/ui/Modal'
import {
  APPROVAL_STATUS,
  BUTTON_SIZES,
  pageConfig,
  ROLE,
  ROUTES,
} from '@/constants'
import {
  TBanner,
  TBannerActionTable,
  TDeleteType,
  TNewsTableListProps,
} from '@/interfaces'
import { deleteBanner, updateBannerIndex } from '@/services/apiCall/banner'
import { IMAGE } from '@/utils/string'
import InfiniteScroll from 'react-infinite-scroll-component'

import type { DragEndEvent } from '@dnd-kit/core'
import { DndContext } from '@dnd-kit/core'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { useTableHeight } from '@/hooks'
import moment from 'moment'
import UiButton from '@/components/ui/Button'
import { useStore } from '@/store'
import { getPermission } from '@/utils'
import ApprovalStatusBadge from '@/components/badge/ApprovalStatusBadge'
import { getCookie, STORAGEKEY } from '@/services/cookies'

const getIndexMoveUpdate = (banners: TBanner[]) => {
  const indexSortDESC = banners.map((item) => item.index).sort((a, b) => b - a)

  const indexMoveUpdate = banners.map((banner, index) => {
    return { id: banner._id, index: indexSortDESC[index] }
  })

  return indexMoveUpdate
}
const isValidUrl = (url: string) => {
  try {
    const parsed = new URL(url)
    return ['http:', 'https:'].includes(parsed.protocol)
  } catch {
    return false
  }
}

const TEXT_LABELS = {
  DELETES: '選択したアイテムを削除',
  CREATE: '作成',
  BANNER: 'バナー',
  EXTERNAL_LINK: '外部リンク',
  COLLECTION: 'コレクション',
  STATUS: '状態',
  CREATOR: '作成者',
  CREATED_DATE: '作成日',
  ACTION: 'アクション',
  APPROVER: '承認者',
  APPROVE_DATE: '承認日',
  VIEW: '詳細',
}

const Row = ({ children, ...props }: RowProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  })

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 }),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 10 } : {}),
  }
  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if ((child as React.ReactElement).key === '_id') {
          return React.cloneElement(child as React.ReactElement, {
            children: (
              <div
                ref={setActivatorNodeRef}
                className="flex justify-center items-center w-9 h-9 touch-none cursor-move"
                {...listeners}
              >
                {/* <HiMenu
                  style={{
                    touchAction: 'none',
                    cursor: 'move',
                    width: 24,
                    height: 24,
                  }}
                  {...listeners}
                /> */}
                <IconSortDropDrag />
              </div>
            ),
          })
        }
        return child
      })}
    </tr>
  )
}

function Action({ data }: TBannerActionTable) {
  const { canEdit } = getPermission({
    approvalStatus: data?.approvalStatus as APPROVAL_STATUS,
    creatorId: data.createdBy?._id || '',
  })
  const { push } = useRouter()
  return (
    <div className="flex items-center gap-6">
      <UiButton
        className="w-full flex !text-primary"
        title={<IconPen />}
        isGradient={false}
        isDisabled={!canEdit}
        handleClick={() => push(`${ROUTES.adminBanners}/edit/${data?._id}`)}
      />
      <UiButton
        className="w-full !text-primary"
        title={<span className="mr-1">{TEXT_LABELS.VIEW}</span>}
        handleClick={() => push(`${ROUTES.adminBanners}/view/${data?._id}`)}
        isGradient={false}
      />
    </div>
  )
}

function BannerTableList(props: TNewsTableListProps) {
  const {
    bannersData,
    refetchBanner,
    banners,
    pageIndex,
    setPageIndex,
    setBanners,
    isLoading,
    pageSize,
    searchWord,
    setSearchWord,
  } = props
  const [openModalDelete, setOpenModalDelete] = useState<boolean>(false)
  const [deleteBannerType, setDeleteBannerType] =
    useState<TDeleteType>('deleteBanner')

  const [bannerSelect, setBannerSelect] = useState<string[]>([])
  const [bannerDeleteId, setBannerDeleteId] = useState<string>('')
  const [isLoadingUpdateIndex, setLoadingUpdateIndex] = useState<boolean>(false)
  const [isSelectAllBanner] = useState<boolean>(false)
  const totalHeightMinus = 300
  const { tableHeight } = useTableHeight(totalHeightMinus)
  const { role } = useStore()

  const { push } = useRouter()

  const columns: ColumnsType<TBanner> = [
    {
      title: ' ',
      dataIndex: '_id',
      key: '_id',
      render: () => <Row />,
    },
    {
      title: TEXT_LABELS.BANNER,
      dataIndex: 'image',
      key: 'image',
      render: (_, record) => (
        <div className="w-[96px] h-[56px] rounded-md ">
          {record?.image && (
            <Image
              width={96}
              height={56}
              unoptimized
              className="h-full w-full object-contain rounded-lg"
              src={record?.image || IMAGE.defaultImage}
              alt="banner image"
            />
          )}
        </div>
      ),
    },
    {
      title: TEXT_LABELS.EXTERNAL_LINK,
      dataIndex: 'externalLink',
      key: 'externalLink',
      width: 180,
      render: (externalLink: string) => {
        if (!isValidUrl(externalLink)) {
          return <span>-</span>
        }

        return (
          <Tooltip title={externalLink}>
            <a
              href={externalLink}
              target="_blank"
              rel="noopener noreferrer"
              className="line-clamp-2 break-words text-blue-500 hover:underline block max-w-[180px]"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                wordBreak: 'break-word',
              }}
            >
              {externalLink}
            </a>
          </Tooltip>
        )
      },
    },
    {
      title: TEXT_LABELS.COLLECTION,
      dataIndex: 'collection',
      key: 'collection',
      render: (collection) => {
        return <div>{collection?.name || '-'}</div>
      },
    },
    {
      title: TEXT_LABELS.STATUS,
      dataIndex: 'approvalStatus',
      key: 'approvalStatus',
      render: (approvalStatus) => (
        <ApprovalStatusBadge status={approvalStatus} />
      ),
    },
    {
      title: TEXT_LABELS.CREATOR,
      dataIndex: 'createdBy',
      key: 'createdBy',
      render: (createdBy) => {
        return <span>{createdBy?.email || '-'}</span>
      },
    },
    {
      title: TEXT_LABELS.CREATED_DATE,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time: string) => {
        const formatted = moment(time).isValid()
          ? moment(time).format('YYYY-MM-DD')
          : '-'

        return <span>{formatted}</span>
      },
    },
    {
      title: TEXT_LABELS.APPROVER,
      dataIndex: 'approvedBy',
      key: 'approvedBy',
      render: (approvedBy) => <span>{approvedBy?.email ?? '-'}</span>,
    },
    {
      title: TEXT_LABELS.ACTION,
      dataIndex: 'Approve Date',
      key: 'approveDate',
      render: (time: string) => {
        const formatted = moment(time).isValid()
          ? moment(time).format('YYYY-MM-DD')
          : '-'

        return <span>{formatted}</span>
      },
    },
    {
      title: TEXT_LABELS.ACTION,
      dataIndex: 'Action',
      key: 'Action',
      render: (_, record) => <Action data={record} />,
    },
  ]

  const handlePageChange = () => {
    if (
      bannersData?.totalItems &&
      pageIndex < bannersData?.totalItems / pageSize
    ) {
      setPageIndex(pageIndex + 1)
    }
  }

  useEffect(() => {
    setBannerSelect([])
  }, [pageSize])

  useEffect(() => {
    if (isSelectAllBanner) {
      setBannerSelect(banners?.map((item) => item._id))
    }
  }, [banners, isSelectAllBanner])

  const rowSelection: TableRowSelection<TBanner> = {
    onChange: (_, selectedRows) => {
      setBannerSelect(selectedRows.map((item) => item._id))
    },
    selectedRowKeys: bannerSelect,
    onSelectAll: (selected) => {
      if (adminData?.role === ROLE.operator) {
        const validRows = banners.filter(
          (item) =>
            typeof item.createdBy !== 'string' &&
            item?.createdBy?.email === adminData.email
        )
        setBannerSelect(selected ? validRows.map((r) => r._id) : [])
      } else {
        setBannerSelect(selected ? banners.map((r) => r._id) : [])
      }
    },
    getCheckboxProps: (record: TBanner) => {
      if (adminData?.role === ROLE.operator) {
        const isOwner =
          typeof record.createdBy !== 'string' &&
          record?.createdBy?.email === adminData.email

        if (!isOwner) {
          return { style: { display: 'none' }, disabled: true }
        }
      }
      return {}
    },
  }
  const { mutate: mutateDeleteBanner, isPending: isDeleteBanner } = useMutation(
    {
      mutationFn: (bannerIds: string[]) => {
        const type = isSelectAllBanner ? 'deleteAll' : 'delete'
        return deleteBanner(
          bannerIds,
          type,
          (role as ROLE.systemAdmin) || ROLE.operator
        )
      },
      onSuccess: () => {
        setOpenModalDelete(false)
        refetchBanner()
        setBannerDeleteId('')
        setBannerSelect([])
        toast.success('バナーの削除が成功しました。')
      },
    }
  )
  const handleDeleteBanner = (type: TDeleteType) => {
    const bannerIds = {
      deleteBanner: [bannerDeleteId],
      deleteBanners: bannerSelect,
    }
    mutateDeleteBanner(bannerIds[type])
  }
  const handleOpenModalDeleteBanners = () => {
    setOpenModalDelete(true)
    setDeleteBannerType('deleteBanners')
  }

  const onDragEnd = async ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const bannersBefore = [...banners]
      const activeIndex = banners.findIndex((i) => i._id === active.id)
      const overIndex = banners.findIndex((i) => i._id === over?.id)
      const bannersMove = arrayMove(bannersBefore, activeIndex, overIndex)

      setLoadingUpdateIndex(true)
      setBanners(bannersMove)

      try {
        await updateBannerIndex(getIndexMoveUpdate(bannersMove))
        toast.success('バナーの順番が更新されました。')
      } catch (error) {
        toast.error('バナーの順番が更新されませんでした')
        setBanners(bannersBefore)
      }
      setLoadingUpdateIndex(false)
    }
  }

  const isApproverRole = useMemo(() => role === ROLE.approver, [role])
  const adminData = useMemo(() => getCookie(STORAGEKEY.ADMIN), [role])

  return (
    <>
      <div className="mt-7">
        <div className="mb-6 flex-wrap md:flex justify-between">
          <Input
            size="large"
            className="w-full md:w-[60%] lg:w-1/3"
            placeholder={`名前でバナーを検索`}
            suffix={<IconSearch height={20} width={20} />}
            value={searchWord}
            onChange={(e) => setSearchWord(e.target.value)}
            onPressEnter={() => setSearchWord(searchWord)}
            allowClear
          />
          {!isApproverRole ? (
            <UiButton
              className="flex justify-center items-center rounded-lg w-full md:w-[35%] lg:w-[214px] !h-11 mt-2 md:mt-0"
              isGradient={true}
              title={TEXT_LABELS.CREATE}
              handleClick={() => push(ROUTES.adminCreateBanner)}
            />
          ) : (
            <div></div>
          )}
        </div>
        <div className="flex items-center justify-between">
          {!isApproverRole ? (
            <div className="flex justify-between w-full mb-6">
              <UiButton
                isDisabled={bannerSelect?.length === 0 || isDeleteBanner}
                size={BUTTON_SIZES.MD}
                className={
                  'flex justify-center items-center gap-2 rounded !text-primary w-24 h-9'
                }
                isGradient={false}
                title={
                  <>
                    <HiOutlineTrash className="font-montserrat font-normal w-4 h-4 !text-primary inline -ml-9" />
                    {TEXT_LABELS.DELETES}
                  </>
                }
                handleClick={handleOpenModalDeleteBanners}
                isBorder={false}
              />
            </div>
          ) : (
            <div></div>
          )}
        </div>
        <InfiniteScroll
          dataLength={bannersData?.totalItems || 1}
          next={handlePageChange}
          hasMore={true}
          loader={''}
          height={tableHeight}
        >
          <DndContext
            modifiers={[restrictToVerticalAxis]}
            onDragEnd={onDragEnd}
          >
            <SortableContext
              items={banners.map((i) => i._id)}
              strategy={verticalListSortingStrategy}
            >
              <Table
                pagination={false}
                className="mx-2 custom-select custom-table"
                columns={columns}
                rowSelection={!isApproverRole ? rowSelection : undefined}
                dataSource={banners}
                loading={isLoading || isLoadingUpdateIndex}
                rowKey="_id"
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={pageConfig.noData}
                    />
                  ),
                }}
                components={{
                  body: {
                    row: Row,
                  },
                }}
              />
            </SortableContext>
          </DndContext>
        </InfiniteScroll>
      </div>

      <UiModal
        header={''}
        isOpen={openModalDelete}
        onClose={() => {
          setOpenModalDelete(false)
          setBannerDeleteId('')
          setBannerSelect([])
        }}
        cancelButton={{
          isShow: true,
          title: 'キャンセル',
          action: () => {
            setOpenModalDelete(false)
            setBannerDeleteId('')
            setBannerSelect([])
          },
        }}
        confirmButton={{
          isShow: true,
          title: 'はい',
          action: () => handleDeleteBanner(deleteBannerType),
        }}
      >
        <p className="text-center text-sm font-semibold">
          {deleteBannerType === 'deleteBanners'
            ? '選択したバナーを削除しますか？'
            : 'このバナーを削除してもよろしいですか。'}
        </p>
      </UiModal>
    </>
  )
}

export default BannerTableList
