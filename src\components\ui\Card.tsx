import React from 'react'
import Image from 'next/image'
import { CARD_RATIOS, CARD_SIZES } from '@/constants'
import Link from 'next/link'
import { formatWithCurrency } from '@/utils'
import { Tooltip } from 'antd'
import { VideoPreview } from '../nfts'

export interface CardProps {
  title: string
  titleToolTip?: boolean
  image?: string
  video?: string
  description?: string
  // Pricing info
  price?: string
  priceLabel?: string

  // Visual customization
  variant?: 'default' | 'featured' | 'info' | 'basic' | ''
  size?: CARD_SIZES
  aspectRatio?: CARD_RATIOS

  // Styling
  className?: string
  imageClassName?: string

  // Interactive
  onClick?: () => void
  href?: string
  isSelected?: boolean

  // Additional content
  badge?: string
  footer?: React.ReactNode
  children?: React.ReactNode

  // Next.js Image specific
  imagePriority?: boolean
  imageSizes?: string
}

const UiCard: React.FC<CardProps> = ({
  title,
  titleToolTip,
  image,
  video,
  description,
  price,
  variant = '',
  size = CARD_SIZES.MD,
  aspectRatio = CARD_RATIOS.square,
  className = '',
  priceLabel,
  imageClassName = '',
  onClick,
  href,
  isSelected = false,
  badge,
  footer,
  children,
  imagePriority = false,
  imageSizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case CARD_SIZES.SM:
        return 'h-[14rem] px-0'
      case CARD_SIZES.NORMAL:
        return 'h-[20rem]'
      case CARD_SIZES.MD:
        return 'h-[21.875rem]'
      case CARD_SIZES.LG:
        return 'h-[25rem]'
      default:
        return ''
    }
  }

  const getAspectRatioClasses = () => {
    switch (aspectRatio) {
      case CARD_RATIOS.portrait:
        return 'aspect-[3/4]'
      case CARD_RATIOS.landscape:
        return 'aspect-[4/3] min-h-[9.375rem] max-h-[11.25rem] w-full'
      default:
        return 'aspect-square'
    }
  }

  const getVariantClasses = () => {
    const baseClasses = 'rounded-xl overflow-hidden transition-all duration-300'

    switch (variant) {
      case 'featured':
        return `${baseClasses} bg-gradient-to-br from-purple-900/20 to-blue-900/20 border-2 border-purple-500/30 shadow-lg shadow-purple-500/20`
      case 'info':
        return `${baseClasses} bg-gray-800/50 border border-gray-700/50 p-4`
      case 'basic':
        return `${baseClasses} bg-gray-800/30 border border-gray-700/30`
      case 'default':
        return `${baseClasses} bg-card hover:border-blue-500/30 hover:shadow-lg hover:shadow-blue-500/10 p-4`
      default:
        ''
    }
  }

  const getBorderClasses = () => {
    if (isSelected) {
      return 'border-2 border-blue-500'
    }
    return ''
  }

  const titleClasses = ` font-montserrat font-semibold leading-[140%] tracking-[0.02em] text-white mb-2 text-sm line-clamp-1`

  const CardContent = () => (
    <div
      className={`
        relative w-full group cursor-pointer p-[1.25rem]
        ${getVariantClasses()}
        ${getBorderClasses()}
        ${getSizeClasses()}
        ${className}
        flex flex-col gap-4
        bg-getCardBg
      `}
      onClick={onClick}
    >
      {/* Badge */}
      {badge && (
        <div className="absolute top-2 right-2 z-10 px-2 py-1 bg-purple-600 text-white text-xs font-medium rounded-md">
          {badge}
        </div>
      )}

      {/* Image Section */}
      {image && variant !== 'info' && (
        <div className={`relative ${getAspectRatioClasses()} overflow-hidden`}>
          <Image
            src={image}
            alt={title}
            fill
            sizes={imageSizes}
            className={`object-cover group-hover:scale-105 transition-transform duration-300 ${imageClassName}`}
            priority={imagePriority}
          />
          {!!variant && (
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          )}
        </div>
      )}

      {/* Video Section */}
      {video && variant !== 'info' && (
        <div className={`relative ${getAspectRatioClasses()} overflow-hidden`}>
          <VideoPreview src={video} className="object-cover w-full h-full" />
        </div>
      )}

      {/* Content Section */}
      <div
        className={`flex flex-col flex-1 ${variant === 'info' ? 'h-full' : ''}`}
      >
        {/* Title */}
        {titleToolTip ? (
          <Tooltip title={title}>
            <h3 className={titleClasses} style={{ textOverflow: 'ellipsis' }}>
              {title}
            </h3>
          </Tooltip>
        ) : (
          <h3 className={titleClasses}>{title}</h3>
        )}

        {/* Description */}
        {description && (
          <p className={`text-white mt-2 text-xs font-[200]`}>{description}</p>
        )}

        {/* Custom Children */}
        {children}

        {/* Price Section */}
        {price && (
          <div className="mt-auto">
            <div className="font-montserrat font-normal text-[12px] leading-[140%] tracking-[0.02em] text-white">{`${priceLabel}: ${formatWithCurrency(
              +price
            )}`}</div>
          </div>
        )}

        {/* Footer */}
        {footer && (
          <div className="mt-3 pt-3 border-t border-gray-700/50">{footer}</div>
        )}
      </div>

      {/* Hover overlay for clickable cards */}
      {(onClick || href) && (
        <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      )}
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="block">
        <CardContent />
      </Link>
    )
  }

  return <CardContent />
}

UiCard.displayName = 'Card'
export default React.memo(UiCard)
