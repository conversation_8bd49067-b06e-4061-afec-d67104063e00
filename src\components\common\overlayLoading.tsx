import clsx from 'clsx'
import { memo } from 'react'

const OverlayLoading: React.FC = (): JSX.Element => {
  return (
    <div
      className={clsx(
        'h-screen w-screen fixed left-0 top-0 z-50 bg-gray-900 flex items-center justify-center opacity-80 transition-all'
      )}
    >
      <div className="loader-container">
        <div className="loader"></div>
        <div className="loader-text">処理中...</div>
      </div>
    </div>
  )
}

export default memo(OverlayLoading)
