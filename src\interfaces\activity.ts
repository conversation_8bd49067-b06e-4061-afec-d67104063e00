import { Paginated } from '.'
import { ACTION_TYPE } from '../constants'
import { Admin } from './admin'

export type Activity = {
  _id: string
  targetId: Record<string, string>
  adminId: Partial<Admin>
  action: ACTION_TYPE
  createdAt: Date | string
  updatedAt: Date | string
}

export type ActivitySearch = {
  pageSize: number
  pageIndex?: number
}

export type PaginatedActivity = Paginated<Activity>
