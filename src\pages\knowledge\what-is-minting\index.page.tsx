import { StaticPageCard } from '@/components/card'
import { StaticPageLayout } from '@/components/layouts/staticPageLayout'
import { getCurrentLanguage } from '@/utils'
import { IMAGE } from '@/utils/string'

const EnglishContent = () => (
  <div className="static-page">
    <p>
      Minting an NFT refers to the process of recording a digital item on the
      blockchain and issuing it as a unique asset.
    </p>
    <p>
      This process gives the work its verifiable origin, ensures authenticity,
      and stores the ownership history on an immutable ledger.
    </p>

    <h3>What Does It Mean to Mint an NFT?</h3>
    <p>To mint is to officially give birth to a digital asset.</p>
    <p>
      At the time of minting, metadata such as the creator’s name, description,
      and files (like images, music, or videos) are attached to the NFT, which
      is then recorded permanently on the blockchain.
    </p>
    <p>
      As a result, an NFT becomes a certified, traceable, and unique digital
      item that stands apart from any copy.
    </p>

    <h3>Why Minting Matters</h3>
    <p>Some people ask, “Can’t I just screenshot an NFT?”</p>
    <p>
      But a screenshot has no proof of ownership, no history, and no traceable
      origin.
    </p>
    <p>
      Minted NFTs, on the other hand, are verifiably original, with clear
      records of creation and ownership—making them the real thing in the
      digital space.
    </p>

    <h3>Minting for Creators</h3>
    <ul>
      <li>Officially and securely register your work on the blockchain</li>
      <li>Build communities with limited-edition or reward-based NFTs</li>
      <li>
        Design royalty systems that generate income from both primary and
        secondary sales
      </li>
    </ul>

    <h3>Minting for Collectors</h3>
    <ul>
      <li>
        Become the first verified owner of an NFT by participating in the
        initial mint
      </li>
      <li>
        Enjoy the excitement of projects that offer randomized or surprise NFT
        reveals, similar to mystery packs
      </li>
    </ul>

    <p>
      At Glitters, minting is more than just a technical process—it&apos;s a way
      to mark your connection to a creative work and preserve that connection
      forever.
    </p>
    <p>
      Step into the origin story of digital culture, and leave your mark with
      every NFT you mint.
    </p>
  </div>
)

const JapaneseContent = () => (
  <div className="static-page">
    <p>
      NFTを「ミントする」とは、デジタルコンテンツをブロックチェーンに記録し、唯一無二の存在として発行することを意味します。
    </p>
    <p>
      この行為によって、作品の真正性や所有者が明確になり、ブロックチェーン上に永続的な履歴として残ります。
    </p>

    <h3>NFTをミントするとは？</h3>
    <p>
      「ミント」とは、デジタルアイテムに対して公式な“誕生日”を与える行為とも言えます。
    </p>
    <p>
      ミントの際には、作成者名、説明、画像や音声・映像ファイルなどのメタデータがNFTに紐付けられ、その由来と所有履歴がブロックチェーンに記録されます。
    </p>
    <p>
      これにより、NFTは「本物であること」と「誰が最初に所有したか」を証明できる唯一性のあるデジタル資産となります。
    </p>

    <h3>なぜミントが必要なのか？</h3>
    <p>
      「NFTはスクリーンショットで複製できるのでは？」という疑問をよく耳にします。
    </p>
    <p>確かに見た目は似ていても、スクショには証明も履歴もありません。</p>
    <p>
      一方、ミントされたNFTは、誰が作り、誰が所有し、どのように移転されたかがすべて記録されており、偽物との違いが明確に可視化された“証明された本物”です。
    </p>

    <h3>クリエイターにとってのミント</h3>
    <ul>
      <li>自身の作品を改ざん不可能な形で公式に登録できる</li>
      <li>限定版NFTや特典付きNFTを通じてファンコミュニティの形成が可能</li>
      <li>二次流通時にもロイヤリティを受け取る仕組みを構築できる</li>
    </ul>

    <h3>コレクターにとってのミント</h3>
    <ul>
      <li>
        ミント段階でNFTを入手すれば、オリジナルオーナーとしての証明を得られる
      </li>
      <li>
        ミント参加時にどの作品が出るか分からない「ミステリーパック」的要素を楽しめるプロジェクトも多い
      </li>
    </ul>

    <p>
      Glittersでは、NFTのミントを通じて、作品やプロジェクトへの関与が「証明」として可視化され、未来へと残る価値となります
    </p>
    <p>
      デジタル作品の「始まり」に立ち会い、その一部になる体験を、Glittersで。
    </p>
  </div>
)

function WhatIsMinting() {
  const lang = getCurrentLanguage()
  const title =
    lang === 'en'
      ? 'What is Minting? – Giving Digital Works a Proven Beginning'
      : 'ミントとは？ – デジタル作品に「証明」と「始まり」を与える行為'

  return (
    <StaticPageLayout title={title}>
      <StaticPageCard
        title={title}
        imageUrl={IMAGE.whatIsMinting}
        createdAt="2025-08-01"
      >
        {lang === 'en' ? <EnglishContent /> : <JapaneseContent />}
      </StaticPageCard>
    </StaticPageLayout>
  )
}

export default WhatIsMinting
