import { ButtonHTMLAttributes, ReactElement } from 'react'

type TabButtonProp = {
  isActive: boolean
  className?: string
  title: ReactElement | string
} & Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'children'>

export const TabButton = ({
  isActive,
  className,
  title,
  ...props
}: TabButtonProp) => {
  const commonClasses =
    'font-montserrat font-normal text-[14px] leading-[140%] tracking-[0.02em] text-white rounded-lg px-[15px] py-[10px] border-solid'
  const activeClasses = isActive
    ? 'bg-[#2d2d2d] border border-[#2d2d2d]'
    : 'hover:bg-[#3e3e3e] transition-all duration-300 border border-[#3e3e3e]'
  return (
    <button
      className={`${commonClasses} ${activeClasses} ${className}`}
      {...props}
    >
      {title}
    </button>
  )
}
