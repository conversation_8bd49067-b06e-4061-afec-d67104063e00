import { ACTION_TYPE, ADMIN_ROLE_MAP } from '@/constants'
import { Activity } from '@/interfaces'

export const getActivityContent = (item: Activity) => {
  if (item.action === ACTION_TYPE.CREATED) {
    return `${item.adminId?.email}は${
      item.targetId.email
    }の${ADMIN_ROLE_MAP.get(item.targetId.role)}アカウントを作成しました。`
  }
  if (item.action === ACTION_TYPE.UPDATED) {
    return `${item.adminId?.email}は${
      item.targetId.email
    }の役割を${ADMIN_ROLE_MAP.get(item.targetId.role)}に変更しました。`
  }
  if (item.action === ACTION_TYPE.UNBLOCKED) {
    return `${item.adminId?.email}は${
      item.targetId.email
    }の${ADMIN_ROLE_MAP.get(item.targetId.role)}アカウントを有効にしました。`
  }
  if (item.action === ACTION_TYPE.BLOCKED) {
    return `${item.adminId?.email}は${
      item.targetId.email
    }の${ADMIN_ROLE_MAP.get(item.targetId.role)}アカウントを無効にしました。`
  }
}
