import React from 'react'
import { TEducationalItem } from '@/interfaces'
import KnowledgeCard from '@/components/homepage/KnowledgeCard'
import { useTranslate } from '@/hooks'
import { IMAGE } from '@/utils/string'

interface EducationalSectionProps {
  items?: TEducationalItem[]
  className?: string
}

const EducationalSection: React.FC<EducationalSectionProps> = ({
  className = '',
}) => {
  const t = useTranslate()

  const educationalContents = [
    {
      id: '1',
      title: t.educational_what_is_an_nft,
      image: IMAGE.whatIsAnNft,
      url: '/knowledge/what-is-an-nft',
      order: 1,
    },
    {
      id: '2',
      title: t.educational_what_is_minting,
      image: IMAGE.whatIsMinting,
      url: '/knowledge/what-is-minting',
      order: 2,
    },
    {
      id: '3',
      title: t.educational_how_to_stay_protected_in_web3,
      image: IMAGE.howToStayProtectedInWeb3,
      url: '/knowledge/how-to-stay-protected-in-web3',
      order: 3,
    },
    {
      id: '4',
      title: t.educational_what_is_get_wallet,
      image: IMAGE.whatIsAGetWallet,
      url: '/knowledge/what-is-a-get-wallet',
      order: 4,
    },
  ]

  return (
    <section className={`py-12 ${className}`}>
      <div className=" mx-auto px-4 lg:px-8 py-6 bg-getCardBg rounded-md">
        {/* Section Header */}
        <div className="mb-8 tracking-[0.04em]">
          <h2 className="text-lg font-montserrat font-semibold text-[18px] leading-[170%] tracking-[0.02em] text-white mb-4">
            {t.educational_title}
          </h2>
          <p className="font-montserrat font-normal text-[13px] leading-[140%] tracking-[0.02em] text-white max-w-2xl">
            {t.educational_subtitle}
          </p>
        </div>

        {/* Horizontal Card List */}
        <div className="relative">
          <div className="flex gap-6 overflow-x-auto pb-4 scrollbar-hide">
            {educationalContents.map((item) => (
              <KnowledgeCard key={item.id} item={item} />
            ))}
          </div>

          {/* Gradient Overlay for Scroll Indication bg-gradient-to-l from-neutral-900 to-transparent*/}
          <div className="absolute right-0 top-0 bottom-4 w-8 pointer-events-none" />
        </div>
      </div>
    </section>
  )
}

export default EducationalSection
