import { useQuery } from '@tanstack/react-query'
import { getNftApprovalStats } from '@/services/apiCall/nft'
import { NftApprovalStats } from '@/interfaces'
import { StatisticsCard } from '@/components/card/StatisticsCard'
import { TextAdminTitle } from '@/components/texts/TextAdminTitle'
import UiButton from '@/components/ui/Button'
import { getCookie } from '@/utils'
import { NFT_FILTER_DATA } from '@/utils/type'
import { IconDownload } from '@/components/icons'
import { BUTTON_SIZES } from '@/constants'

export const TEXT_NFT_STATS = {
  CSV_EXPORT: 'CSV出力',
} as const

type NftStatisticsProps = {
  filterData: NFT_FILTER_DATA | null
}

const exportCsv = async (filterData: NFT_FILTER_DATA | null) => {
  const token = getCookie('admin_access_token') || ''

  // build query
  const queryParams = new URLSearchParams()
  if (filterData) {
    Object.entries(filterData).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, String(value))
      }
    })
  }

  const url = `${
    process.env.NEXT_PUBLIC_API_BASE_URL
  }/v1/nfts/metadata/export-csv/list-nfts?${queryParams.toString()}`

  const res = await fetch(url, {
    headers: {
      Authorization: token,
      'x-signature': process.env.NEXT_PUBLIC_GLOBAL_API_SIGNATURE || '',
    },
  })
  const csvText = await res.text()

  const bom = '\uFEFF'
  const blob = new Blob([bom + csvText], { type: 'text/csv;charset=utf-8;' })
  const blobUrl = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = blobUrl
  link.setAttribute('download', 'NFTs_Management.csv')
  document.body.appendChild(link)
  link.click()
  link.parentNode?.removeChild(link)
}

export default function NftStatistics({ filterData }: NftStatisticsProps) {
  const { data, isLoading } = useQuery<NftApprovalStats>({
    queryKey: ['nft-approval-stats'],
    queryFn: getNftApprovalStats,
  })

  const statTitles = [
    { key: 'totalNft', label: 'NFT数' },
    { key: 'totalWaitingForApproval', label: '承認待ち' },
    { key: 'totalRejected', label: '却下済み' },
    { key: 'totalApproved', label: '承認済' },
    { key: 'totalDraft', label: 'ドラフト' },
  ]

  return (
    <div>
      <div className="flex justify-between items-center">
        <TextAdminTitle title="オーバービュー" />
        <UiButton
          size={BUTTON_SIZES.FIT}
          title={<span>{TEXT_NFT_STATS.CSV_EXPORT}</span>}
          isGradient={false}
          handleClick={() => exportCsv(filterData)}
          className="!text-primary"
          icon={<IconDownload />}
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2  [@media(min-width:1000px)]:grid-cols-3  [@media(min-width:1175px)]:grid-cols-5 gap-4">
        {statTitles.map(({ key, label }) => (
          <StatisticsCard
            key={key}
            title={label}
            stat={isLoading ? '...' : data?.[key] ?? '-'}
          />
        ))}
      </div>
    </div>
  )
}
