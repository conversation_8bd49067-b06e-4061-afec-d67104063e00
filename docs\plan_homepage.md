# Homepage Feature Implementation Plan

## 1. Overview

This plan outlines the implementation of a modern, responsive homepage for the GLITTERS NFT marketplace. The homepage will feature a hero section with carousel, search and filter functionality, NFT grid display, marketplace insights sidebar, educational content section, and a comprehensive footer. The design follows a dark theme with neon blue and bright yellow accents, supporting both desktop (1366x768px minimum) and mobile (390x845px minimum) responsive layouts.

**Key Features:**

- Hero section with carousel and CTA
- Search and filter functionality using Ant Design components
- NFT grid with load more functionality
- Marketplace insights sidebar
- Educational NFT learning section
- Responsive design for all screen sizes
- Language switching (JA/EN)
- Wallet connection integration

---

## 2. Example Layout

```tsx
// Example homepage structure
import HomePage from '@/pages/home/<USER>/HomePage'
import Header from '@/components/layouts/Header'
import Footer from '@/components/layouts/Footer'

export default function HomePageLayout() {
  return (
    <div className="min-h-screen bg-dark">
      <Header />
      <main className="flex-1">
        <HomePage />
      </main>
      <Footer />
    </div>
  )
}
```

**UI Components Breakdown:**

- **Header**: Logo, language switcher, connect wallet button
- **Hero Section**: Carousel with navigation arrows, pagination dots, CTA button
- **Search & Filter**: Input field, category dropdown, search button
- **Main Content**: NFT grid (left) + marketplace insights (right)
- **Educational Section**: Horizontal card list for NFT learning
- **Footer**: Logo, links, copyright

---

## 3. Flow

### User Flow:

1. **Landing**: User arrives at homepage, sees hero carousel
2. **Navigation**: Can switch language, connect wallet, or explore NFTs
3. **Search**: User can search/filter NFTs by keyword, category
4. **Browse**: View NFT grid with floor prices, load more functionality
5. **Learn**: Access educational content about NFTs and Web3
6. **Read**: View latest marketplace insights and news

### Data Flow:

1. **Banner Data**: Fetch hero carousel banners via `getBanners` API
2. **NFT Data**: Fetch featured NFTs via `getNftsTag` API with pagination
3. **News Data**: Fetch marketplace insights via new news API
4. **Categories**: Fetch NFT categories for filter dropdown
5. **State Management**: Use Zustand for global state (user, wallet connection)
6. **Local State**: React hooks for search filters, pagination, loading states

### Component Composition:

- **HomePage**: Main container component
- **HeroSection**: Carousel with navigation and CTA
- **SearchFilterBar**: Ant Design Input and Select components
- **NFTGrid**: Grid layout with NFT cards and load more
- **MarketplaceInsights**: Sidebar with news/blog posts
- **EducationalSection**: Horizontal card list
- **ResponsiveLayout**: Flexbox grid system for desktop/mobile

---

## 4. Version Note

**Plan Action:** Creating a new comprehensive homepage feature with modern UI/UX design, replacing the current basic homepage implementation.

**Technology Stack:**

- Next.js 13 (Pages Router)
- TypeScript
- Tailwind CSS for styling
- Ant Design for form components (Input, Select)
- Custom UI components from `@/components/ui`
- Zustand for state management
- React Query for data fetching

**Breaking Changes:** None - this is a new feature implementation that enhances the existing homepage.

---

## 5. API Spec

### Existing APIs to Use:

```ts
// Banner API (existing)
GET /api/banners
Response: {
  items: TBanner[]
  pageIndex: number
  pageSize: number
  totalItems: number
}

// NFT API (existing)
GET /api/market/collections
Query: {
  pageIndex: number; // Default: 1
  pageSize: number;  // Default: 12
  searchWord?: string;
  category?: string;  // Filter by collection category
}
Response: {
  items: Collection[]
  pageIndex: number
  pageSize: number
  totalItems: number
}
```

### New APIs Required:

```ts
// News/Insights API (new)
GET /api/news/user
Query: {
  pageSize: number
  pageIndex: number
}
Response: {
  items: TNewsItem[]
  pageIndex: number
  pageSize: number
  totalItems: number
}


// Categories API (old)
src\services\apiCall\category.ts\getListCategories

// Educational Content (new)
Static page so no api needed
```

### TypeScript Interfaces:

```ts
// New interfaces to add
enum NewStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
}

interface TNewsItem: {
  id?: string;
  title: string;
  imageId?: string;
  context: string;
  imageUrl?: string;
  status: NewStatus
}
```

---

## 6. Summary List of Files

### Files to Create

- [ ] `src/pages/home/<USER>/HomePage.tsx` – Main homepage component
- [ ] `src/pages/home/<USER>/HeroSection.tsx` – Hero carousel section
- [ ] `src/pages/home/<USER>/SearchFilterBar.tsx` – Search and filter bar
- [ ] `src/pages/home/<USER>/NFTGrid.tsx` – NFT grid with load more
- [ ] `src/pages/home/<USER>/MarketplaceInsights.tsx` – News/insights sidebar
- [ ] `src/pages/home/<USER>/EducationalSection.tsx` – Educational content
- [ ] `src/components/homepage/NFTCard.tsx` – Individual NFT card component
- [ ] `src/components/homepage/NewsCard.tsx` – Individual news card component
- [ ] `src/components/homepage/KnowledgeCard.tsx` – Educational card component
- [ ] `src/services/apiCall/news.ts` – News API service
- [ ] `src/services/apiCall/categories.ts` – Categories API service
- [ ] `src/services/apiCall/educational.ts` – Educational content API service
- [ ] `src/interfaces/news.ts` – News-related interfaces
- [ ] `src/interfaces/categories.ts` – Category interfaces
- [ ] `src/interfaces/educational.ts` – Educational content interfaces
- [ ] `src/constants/homepage.ts` – Homepage-specific constants
- [ ] `public/locales/ja/homepage.json` – Japanese translations for homepage
- [ ] `public/locales/en/homepage.json` – English translations for homepage

### Files to Modify

- [ ] `src/pages/home/<USER>/home.tsx` – Update to use new HomePage component
- [ ] `src/pages/index.page.tsx` – Update metadata and imports
- [ ] `src/components/layouts/header.tsx` – Add language switcher
- [ ] `src/components/layouts/footer.tsx` – Update with new footer content
- [ ] `src/constants/routes.ts` – Add any new routes if needed
- [ ] `src/constants/common.ts` – Add homepage-specific constants
- [ ] `src/services/apiCall/index.ts` – Export new API services
- [ ] `src/interfaces/index.ts` – Export new interfaces
- [ ] `src/styles/main.scss` – Add homepage-specific styles
- [ ] `tailwind.config.js` – Add custom colors for dark theme

---

## Implementation Steps

### Phase 1: Foundation & Structure

1. **Create homepage components structure** in `src/pages/home/<USER>/`
2. **Create reusable homepage components** in `src/components/homepage/`
3. **Add new interfaces** for news, categories, and educational content
4. **Create API services** for new endpoints
5. **Add homepage constants** and configuration

### Phase 2: Core Components

6. **Implement HeroSection** with carousel and navigation
7. **Build SearchFilterBar** using Ant Design components
8. **Create NFTGrid** with responsive layout and load more
9. **Develop MarketplaceInsights** sidebar component
10. **Build EducationalSection** with horizontal card layout

### Phase 3: Integration & Styling

11. **Integrate all components** into main HomePage component
12. **Implement responsive design** for all screen sizes
13. **Add dark theme styling** with custom colors
14. **Integrate with existing header/footer** components
15. **Add loading states and error handling**

### Phase 4: Polish & Optimization

16. **Add animations and transitions** for better UX
17. **Implement proper SEO** with meta tags
18. **Add accessibility features** (ARIA, keyboard navigation)
19. **Optimize performance** with lazy loading and code splitting
20. **Add comprehensive translations** for JA/EN

### Phase 5: Testing & Documentation

21. **Test responsive design** on various screen sizes
22. **Test wallet connection** integration
23. **Test search and filter** functionality
24. **Update documentation** in architecture.md
25. **Final review and testing**

---

## 7. Other Notes

### Internationalization:

- Add homepage-specific translations to `public/locales/ja/homepage.json` and `public/locales/en/homepage.json`
- Use `useTranslate` hook for all text content
- Support dynamic language switching

### Styling:

- Use Tailwind CSS for utility-first styling
- Implement custom dark theme colors in `tailwind.config.js`
- Use SCSS for complex animations and custom styles
- Ensure consistent spacing and typography

### Accessibility:

- Add proper ARIA labels and roles
- Ensure keyboard navigation works for all interactive elements
- Provide alt text for all images
- Maintain proper color contrast ratios
- Support screen readers

### Performance:

- Use Next.js dynamic imports for heavy components
- Implement lazy loading for images
- Optimize bundle size with code splitting
- Use React Query for efficient data fetching and caching

### SEO:

- Add proper meta tags for social sharing
- Implement structured data for NFTs and news
- Ensure proper heading hierarchy
- Add Open Graph and Twitter Card meta tags

---

## 8. Checklist

- [ ] Plan reviewed and approved
- [ ] All new/modified files listed
- [ ] API contracts confirmed
- [ ] UI/UX design reviewed
- [ ] Responsive design requirements confirmed
- [ ] Ant Design components identified
- [ ] Custom UI components identified
- [ ] State management approach defined
- [ ] Internationalization strategy planned
- [ ] Accessibility requirements documented
- [ ] Performance optimization strategy planned
- [ ] Code follows project conventions
- [ ] Testing strategy defined
