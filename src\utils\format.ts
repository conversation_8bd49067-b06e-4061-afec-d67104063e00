import DOMPurify from 'dompurify'
import moment from 'moment'
import axios, { AxiosRequestConfig, ResponseType } from 'axios'
import * as XLSX from 'xlsx'

import { schemaNumber } from './regex'
import { TYPE_NFT, TYPE_STATUS_NFT } from '@/constants/type'
// import { ENFT_TYPE, METADATA_STATUS } from '@/constants'
import { TIME_UTC } from './string'
import { NftDetail, TBanner } from '@/interfaces'
import { pathnameScreen } from './type'
import { Url } from 'next/dist/shared/lib/router/router'
import numeral from 'numeral'
export const shortenString = (
  str: string | undefined | null,
  firstLength = 6,
  lastLength = 4
) => {
  return str?.length
    ? `${str.substring(0, firstLength)}...${str?.substring(
        str?.length - lastLength,
        str?.length
      )}`
    : ''
}

export const formatTransaction = (transaction: string) => {
  if (!transaction) return ''
  const first = transaction.substring(0, 5)
  const last = transaction.substring(transaction.length - 5)

  return `${first}....${last}`
}

export const onRegexNumber = (input: string) => {
  if (schemaNumber.test(input)) {
    return input
  }
}

export const isValueTable = (
  value: string | number | undefined,
  symbol = ''
) => {
  if (value) {
    return value + symbol
  }
  return '----'
}

export const getUniqueElementIfAllEqual = (arr: NftDetail[]) => {
  // const firstElement = arr[0].itemType
  // for (let i = 1; i < arr.length; i++) {
  //   if (arr[i].itemType !== firstElement) {
  //     return false
  //   }
  // }

  // return firstElement
  return arr[0]
}

export const getItemType = (arr: NftDetail[]) => {
  return arr.length > 0
}

export const getStatusSaleItem = (arr: NftDetail[]) => {
  // const firstElement = arr[0].status
  // const allStatusMatch = arr.every((item) => item.status === firstElement)
  // const hasCancelSale = arr.some(
  //   (item) => item.status === METADATA_STATUS.notSell
  // )

  // return !allStatusMatch || hasCancelSale ? false : arr
  return arr.length > 0
}

export const getStatusNotSaleItem = (arr: NftDetail[]) => {
  // const allStatusNotSale = arr.every(
  //   (item) => item.status === METADATA_STATUS.notSell
  // )
  // return allStatusNotSale
  return arr.length > 0
}

export const getTypeSelectedNFT = (type: TYPE_STATUS_NFT | TYPE_NFT) => {
  if (type == 0) {
    return ''
  }
  return type
}

export const formatObjValueEmpty = (obj: object | undefined) => {
  for (const key in obj) {
    if (obj[key] === '') {
      delete obj[key]
    }
  }
  return obj
}
export function removeValueFromObject({
  obj,
  valueRemove = null,
}: {
  obj: object
  valueRemove?: string | null
}) {
  const filteredObj = Object.fromEntries(
    Object.entries(obj).filter(([, value]) => value !== valueRemove)
  )
  return filteredObj
}

export const formatCurrency = (value: string, maximumFractionDigits = 20) => {
  if (!value) return
  const formatter = new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY',
    minimumFractionDigits: 0,
    useGrouping: true,
    maximumFractionDigits: maximumFractionDigits,
  })
  return formatter.format(Number(value))
}

//sanitized HTML use dompurify
const setTargetAttribute = (
  node: Element,
  callBack: (node: Element) => void
) => {
  if ('target' in node) {
    node.setAttribute('target', '_blank')
  }
  callBack(node)
}
const setTargetAttributeLink = (node: Element) => {
  if (
    !node.hasAttribute('target') &&
    (node.hasAttribute('xlink:href') || node.hasAttribute('href'))
  ) {
    node.setAttribute('xlink:show', 'new')
  }
}

export const sanitizedHTML = (value: string) => {
  if (!value) return ''

  const descriptionWithStylesAndRel: string = value.replace(
    /<a/g,
    '<a style="color: blue;" rel="noopener noreferrer" '
  )

  DOMPurify.addHook('afterSanitizeAttributes', (node: Element) => {
    setTargetAttribute(node, setTargetAttributeLink)
  })

  const sanitizedHTMLConvert = DOMPurify.sanitize(descriptionWithStylesAndRel)

  return sanitizedHTMLConvert
}

export const formatNumberWithComma = (value: number | undefined) => {
  const numberConverted = value
    ? new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 4,
        maximumFractionDigits: 4,
        ...(value >= Math.pow(10, 9) && {
          notation: 'compact',
        }),
      }).format(value)
    : null
  return numberConverted?.replace('.0000', '')
}
export const formatTimeToUTC = (time: string | number) => {
  const utcTime = time ? moment(time).utc().format(TIME_UTC) : ''
  return utcTime
}

/**
 * Validates if a string is a valid URL
 * @param url - The URL string to validate
 * @returns boolean indicating if the URL is valid
 */
const isValidUrl = (url: string): boolean => {
  try {
    // Handle empty or undefined URLs
    if (!url || url.trim() === '') {
      return false
    }

    // Create URL object to validate
    new URL(url)

    // Additional checks for valid protocol
    const validProtocols = ['http:', 'https:']
    const urlObj = new URL(url)
    if (!validProtocols.includes(urlObj.protocol)) {
      return false
    }

    return true
  } catch (error) {
    return false
  }
}

/**
 * Gets the redirect link for a banner based on its tags or default URL
 * @param banner - The banner object containing ID and tags
 * @returns The redirect URL for the banner - either from tags or default path
 */
export const getBannerRedirectLink = (banner: TBanner) => {
  let link = `${pathnameScreen.banner.url}/${banner._id}`
  if (banner?.tags[0]?.includes('redirectLink')) {
    const getLink = banner?.tags[0]?.split('redirectLink:')?.[1]
    link = isValidUrl(getLink) ? getLink : ''
  }
  return link as Url
}

export const fetchDataFromXlsx = async (url: string): Promise<number> => {
  // eslint-disable-next-line no-useless-catch
  try {
    const data = await fetchExcelData(url)
    const filteredData = filterNonEmptyRecords(data)
    return calculateTotalByColumn(filteredData, 'purchased_price')
  } catch (error) {
    throw error
  }
}

const fetchExcelData = async (
  url: string
): Promise<Record<string, unknown>[]> => {
  const options: AxiosRequestConfig = {
    url,
    responseType: 'arraybuffer' as ResponseType,
  }
  const axiosResponse = await axios(options)
  const workbook = XLSX.read(axiosResponse.data)
  const sheetName = workbook.SheetNames[0]
  const worksheet = workbook.Sheets[sheetName]
  return XLSX.utils.sheet_to_json(worksheet, { blankrows: true, defval: '' })
}

/**
 * Filters an array of data records, removing rows that are empty except for the first column.
 * Stops processing if two consecutive empty rows are encountered.
 */
const filterNonEmptyRecords = (
  data: Record<string, unknown>[]
): Record<string, unknown>[] => {
  const filteredData: Record<string, unknown>[] = []
  let emptyRowCount = 0

  for (const row of data) {
    const isEmpty = Object.values(row)
      .slice(1)
      .every((value) => value === '')

    if (isEmpty) {
      emptyRowCount++
      if (emptyRowCount === 2) break
    } else {
      emptyRowCount = 0
    }

    filteredData.push(row)
  }

  return filteredData
}

/**
 * Calculates the total sum of a column in the dataset.
 */
const calculateTotalByColumn = (
  data: Record<string, unknown>[],
  columnName: string
): number => {
  if (data.length === 0) return 0

  const header = data[0] || {}
  const columnKey =
    Object.keys(header).find((key) => header[key] === columnName) || ''

  return data
    .map((row) => parseFloat(row[columnKey] as string) || 0)
    .reduce((sum, value) => sum + value, 0)
}

export const CURRENCY = 'GET PAY'
export const formatWithCurrency = (price: number) => {
  return `${formatPriceNoRound(price, 4)} ${CURRENCY}`
}

export const formatWithCurrencyRound = (price: number) => {
  return `${formatPrice(price, 4)} ${CURRENCY}`
}

export const formatPriceNoRound = (price: number, s: number) => {
  return price % 1 !== 0 ? price.toFixed(s) : price.toString()
}

export const formatPrice = (price: number, s: number) => {
  if (price > 1000) {
    const short = numeral(price).format('0.0a')
    return short.includes('.0')
      ? short.replace('.0', '').replace(/([a-z])/g, (m) => m.toUpperCase())
      : short.replace(/([a-z])/g, (m) => m.toUpperCase())
  }
  return price % 1 !== 0 ? price.toFixed(s) : price
}

export const getFontSizeByNumberOfCharacters = (
  text: string,
  maxCharacters: number,
  fontSize: number
) => {
  const numberOfCharacters = text.length
  if (numberOfCharacters > maxCharacters) {
    return (fontSize * (maxCharacters / numberOfCharacters)).toFixed(0)
  }
  return fontSize
}
