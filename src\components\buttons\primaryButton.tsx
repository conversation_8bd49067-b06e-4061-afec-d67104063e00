import React, { memo } from 'react'
import clsx from 'clsx'

interface IProps {
  children?: React.ReactNode
  title?: string
  type?: 'button' | 'submit' | 'reset'
  bordered?: boolean
  disabled?: boolean
  small?: boolean
  width?: string
  height?: string
  color?: string
  textTransform?: 'capitalize' | 'uppercase' | 'normal-case'
  bgColor?: string | undefined
  onClick?: () => void
  className?: string
}

const PrimaryButton: React.FC<IProps> = ({
  children,
  title,
  type = 'button',
  bordered = false,
  disabled = false,
  small = false,
  width = small ? '152px' : '180px',
  height = small ? '32px' : '48px',
  color,
  textTransform = 'capitalize',
  bgColor,
  onClick,
  className,
}) => {
  const styles = {
    width,
    height,
    ...(color ? { color } : {}),
    ...(bgColor ? { backgroundColor: bgColor } : {}),
  }

  return (
    <button
      type={type}
      style={styles}
      onClick={onClick}
      disabled={disabled}
      className={clsx(
        'flex justify-center items-center rounded-md transition-all',
        className ?? '',
        textTransform,
        small ? 'text-sm font-semibold' : 'text-lg font-bold',
        {
          'bg-tailwindBrand1 hover:opacity-80': !bordered && !bgColor,
          [`border-2 border-tailwindBrand1 ${
            !disabled ? 'hover-bg-active' : ''
          }`]: bordered,
          'text-white': !color,
          'cursor-not-allowed hover:!opacity-50 !opacity-50': disabled,
          'hover:opacity-80': bgColor,
        }
      )}
    >
      {title ?? ''}
      {children ?? ''}
    </button>
  )
}

export default memo(PrimaryButton)
