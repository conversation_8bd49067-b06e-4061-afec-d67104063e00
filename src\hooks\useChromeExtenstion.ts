import { useState, useEffect, useCallback, useMemo } from 'react'

// Types for extension communication
type ExtensionAction = string
type ExtensionPayload = any
type ExtensionResponse = any

export interface ExtensionMessage {
  type: string
  payload: ExtensionResponse
}

export interface UseExtensionOptions {
  extensionId?: string
  onMessage?: (message: ExtensionMessage) => void
  onError?: (error: Error) => void
}

/**
 * Hook for communicating with Chrome extensions
 */
export const useChromeExtension = (options: UseExtensionOptions = {}) => {
  const [lastResponse, setLastResponse] = useState<ExtensionResponse | null>(
    null
  )
  const isAvailable = useMemo<boolean>(() => {
    return (
      typeof window !== 'undefined' &&
      typeof (window as any).chrome !== 'undefined' &&
      !!(window as any).chrome.runtime &&
      !!(window as any).chrome.runtime.sendMessage
    )
  }, [])
  const [error, setError] = useState<Error | null>(null)

  // Get extension ID from options, env, or localStorage
  const getExtensionId = useCallback((): string => {
    if (options.extensionId) return options.extensionId

    if (typeof localStorage !== 'undefined') {
      if (localStorage.getItem('EXTENSION_ID')) {
        return localStorage.getItem('EXTENSION_ID') || ''
      }
    }

    if (
      typeof process !== 'undefined' &&
      process.env.NEXT_PUBLIC_EXTENSION_ID
    ) {
      return process.env.NEXT_PUBLIC_EXTENSION_ID as string
    }
    return ''
  }, [options.extensionId])

  // Listen for messages from the extension
  useEffect(() => {
    if (!isAvailable) return
    const messageListener = async (event: any) => {
      if (event.source !== window) return
      const message: ExtensionMessage = event.data
      options.onMessage?.(message)
    }
    window.addEventListener('message', messageListener)
    return () => window.removeEventListener('message', messageListener)
  }, [isAvailable, options])

  if (!isAvailable) {
    const error = new Error('Chrome Extensions API is not available')
    options.onError?.(error)
  }
  // Function to send message to extension
  const sendToExtension = useCallback(
    async (
      action: ExtensionAction,
      payload: ExtensionPayload,
      customExtensionId?: string
    ): Promise<ExtensionResponse> => {
      const extensionId = customExtensionId || getExtensionId()
      if (!extensionId) {
        const error = new Error('Extension ID is not defined')
        setError(error)
        options.onError?.(error)
        throw error
      }

      return new Promise((resolve, reject) => {
        try {
          ;(window as any).chrome.runtime.sendMessage(
            extensionId,
            { action, payload },
            (response: any) => {
              if ((window as any).chrome.runtime.lastError) {
                const error = new Error(
                  `Extension communication failed: ${
                    (window as any).chrome.runtime.lastError.message
                  }`
                )
                setError(error)
                options.onError?.(error)
                reject(error)
              } else {
                setLastResponse(response)
                resolve(response)
              }
            }
          )
        } catch (error) {
          const err =
            error instanceof Error ? error : new Error('Unknown error occurred')
          setError(err)
          options.onError?.(err)
          reject(err)
        }
      })
    },
    []
  )

  return {
    isAvailable,
    lastResponse,
    error,
    sendToExtension,
  }
}
