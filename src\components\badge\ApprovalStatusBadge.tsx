import { APPROVAL_STATUS, renderApprovalStatus } from '@/constants'
import { Badge } from 'antd'
import { PresetStatusColorType } from 'antd/es/_util/colors'

const badgeStatusMap = new Map<string, PresetStatusColorType>([
  [APPROVAL_STATUS.approved, 'success'],
  [APPROVAL_STATUS.rejected, 'error'],
  [APPROVAL_STATUS.draft, 'default'],
  [APPROVAL_STATUS.waiting, 'warning'],
])

export default function ApprovalStatusBadge({
  className,
  status,
}: {
  className?: string
  status: APPROVAL_STATUS
}) {
  return (
    <Badge
      status={badgeStatusMap.get(status) || 'default'}
      text={renderApprovalStatus[status]}
      className={className}
    />
  )
}
