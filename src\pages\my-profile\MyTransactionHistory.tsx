import React from 'react'
import { RightOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'

import { getUserTransactions } from '@/services/apiCall/transaction'
import { formatWithCurrency, shortenString } from '@/utils'
import { useTranslate } from '@/hooks'
import moment from 'moment'
import Link from 'next/link'
import { SCAN_URL } from '@/constants'
import AdminTable from '@/components/table/AdminTable'

interface MyTransactionHistoryProps {
  walletAddress: string
}

function MyTransactionHistory({ walletAddress }: MyTransactionHistoryProps) {
  const defaultPageSize = 4
  const trans = useTranslate()

  const transactionColumns = [
    {
      title: trans.timestamp,
      dataIndex: 'boughtAt',
      key: 'timestamp',
      className: 'text-white',
      render: (boughtAt: number) =>
        moment(new Date(boughtAt)).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: trans.transaction_hash,
      dataIndex: 'transactionHash',
      key: 'transactionHash',
      className: 'text-white',
      render: (transactionHash: string) => {
        return (
          <a
            className="hover:text-primaryHover"
            href={SCAN_URL.URL.replace('{tx}', transactionHash)}
            target="_blank"
            rel="noreferrer"
          >
            {transactionHash ? shortenString(transactionHash, 10, 8) : '-'}
          </a>
        )
      },
    },
    {
      title: trans.token_id,
      dataIndex: 'tokenId',
      key: 'tokenId',
      className: 'text-white',
      render: (tokenId: string) => (tokenId ? `#${tokenId}` : '-'),
    },
    {
      title: trans.buy_price,
      dataIndex: 'price',
      key: 'price',
      className: 'text-white',
      render: (price: number) => formatWithCurrency(price),
    },
  ]

  // Query for user transactions
  const {
    data: userTransactions,
    isLoading: transactionsLoading,
    error: transactionsError,
  } = useQuery({
    queryKey: ['userTransactions', walletAddress],
    queryFn: () =>
      getUserTransactions({
        pageIndex: 1,
        pageSize: defaultPageSize,
      }),
    enabled: !!walletAddress,
  })

  // Check if user has more than 4 transactions
  const hasMoreTransactions =
    userTransactions && userTransactions.totalItems > defaultPageSize

  if (transactionsLoading) {
    return (
      <div className="mb-12">
        <p className="font-montserrat font-semibold text-lg tracking-[0.02em] leading-[170%] text-white border-b border-default pb-2 mb-8">
          {trans.purchase_history}
        </p>
        <div className="text-white text-center">
          {trans.loading_transactions}
        </div>
      </div>
    )
  }

  if (transactionsError) {
    return (
      <div className="mb-12">
        <p className="font-montserrat font-semibold text-lg tracking-[0.02em] leading-[170%] text-white border-b border-default pb-2 mb-8">
          {trans.purchase_history}
        </p>
        <div className="text-white text-center">
          {trans.failed_to_load_transactions}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-12">
      <p className="font-montserrat font-semibold text-lg tracking-[0.02em] leading-[170%] text-white border-b border-default pb-2 mb-8">
        {trans.purchase_history}
      </p>
      <div className="mb-6 overflow-x-auto">
        <AdminTable
          columns={transactionColumns}
          dataSource={userTransactions?.items?.slice(0, 4) || []}
          pagination={false}
          className="custom-table"
          loading={transactionsLoading}
          rowKey="_id"
        />
      </div>

      {hasMoreTransactions && (
        <Link href={`/my-transactions`}>
          <button className="text-primary hover:text-primaryHover flex items-center gap-2 font-montserrat font-normal text-sm tracking-[0.02em] leading-[170%] mx-auto">
            <RightOutlined rev="icon" />
            <span>{trans.view_all_my_history}</span>
          </button>
        </Link>
      )}
    </div>
  )
}

export default MyTransactionHistory
