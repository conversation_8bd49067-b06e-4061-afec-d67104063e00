import Image from 'next/image'
import StandardBadge from '@/components/badge/StandardBadge'
import { NftMetadata } from '@/interfaces'
import { useState } from 'react'
import { useTranslate } from '@/hooks'
import { VideoPreview } from '../nfts'

const NftDetail = ({
  name,
  description,
  image,
  external_url,
  attributes,
  collection,
  mintedCount,
  canSetAsProfileImage
}: NftMetadata) => {
  const [activeTab, setActiveTab] = useState<'info' | 'attributes'>('info')
  const trans = useTranslate()
  const activeClass = (tab: string) => {
    return activeTab === tab
      ? 'bg-neutral-700 text-white'
      : 'bg-neutral-800 text-gray-400'
  }

  return (
    <div className="min-h-screen bg-getCardBg text-white p-8 flex flex-col md:flex-row gap-10">
      {/* Left - Image */}
      <div className="md:w-1/2 flex justify-center">
        <div className="p-4 rounded-md">
           { canSetAsProfileImage ? (
            <Image
              src={image}
              alt={name}
              width={400}
              height={400}
              className="rounded-md object-cover"
            />
          ) : (
            <div className="p-4 rounded-md">
              <div className="flex items-center justify-center w-full h-full bg-gray-200 rounded-md">
                <VideoPreview
                  src={image}
                  className="object-cover w-full h-full rounded-md"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right - Info */}
      <div className="md:w-1/2 space-y-6">
        <div className="flex gap-4">
          <button
            className={`px-4 py-2 rounded transition-colors ${activeClass(
              'info'
            )}`}
            onClick={() => setActiveTab('info')}
          >
            {trans.nft_info}
          </button>
          <button
            className={`px-4 py-2 rounded transition-colors ${activeClass(
              'attributes'
            )}`}
            onClick={() => setActiveTab('attributes')}
          >
            {trans.nft_attribute}
          </button>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-3xl font-semibold">{name}</span>
          <StandardBadge standard={collection?.standard} />
        </div>
        {activeTab === 'info' && (
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-400">
              {external_url && (
                <p className="text-xl text-white font-bold">{external_url}</p>
              )}
            </div>
          </div>
        )}
        {/* Attributes Tab */}
        {activeTab === 'attributes' && (
          <div className="space-y-4">
            <div className="text-sm text-gray-400">
              <h3 className="text-lg font-semibold text-white mb-4">
                {trans.nft_traits}
              </h3>
              {attributes && attributes.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {attributes.map((attribute, index) => (
                    <div
                      key={index}
                      className="bg-white bg-opacity-5 border border-gray-600 rounded-lg p-4 relative"
                    >
                      <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                        {attribute?.trait_type}
                      </div>
                      <div className="text-sm font-semibold text-white">
                        {attribute.value}
                      </div>
                      {/* Small triangle indicator in bottom right */}
                      <div className="absolute bottom-2 right-2 w-0 h-0 border-l-[6px] border-l-transparent border-b-[6px] border-b-gray-500"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <p>{trans.nft_no_attributes_available}</p>
                </div>
              )}
            </div>
          </div>
        )}
        {/* Amount selector */}
        {/* Introduction */}
        <div className="pt-6 border-t border-gray-600 mt-4">
          <h2 className="font-semibold text-lg mb-2">
            {trans.nft_introduction}
          </h2>
          <pre className="text-sm text-gray-300 mb-2">{description}</pre>
        </div>
        {collection.standard === 'ERC-1155' && (
          <div className="flex flex-col">
            {trans.nft_minted_quantity}: {mintedCount || 0}
          </div>
        )}
      </div>
    </div>
  )
}

export default NftDetail
