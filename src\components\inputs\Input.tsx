import React, { memo, useState } from 'react'
import { Controller } from 'react-hook-form'
import { HiOutlineEye, HiOutlineEyeOff } from 'react-icons/hi'
import Text from '../texts/Text'

interface PropsType extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string
  // eslint-disable-next-line
  control: any
  type?: string
  placeholder?: string
  classNameEye?: string
  className?: string
  classNameWarp?: string
  label?: string
  required?: boolean
}

function Input(props: PropsType): JSX.Element {
  const {
    name,
    control,
    type = 'text',
    placeholder,
    className = '',
    label,
    required,
    classNameEye = 'absolute top-[15px] right-[8px] h-5 w-5 cursor-pointer',
    classNameWarp = 'relative mt-5 w-full',
    ...rest
  } = props
  const [openEye, setOpenEye] = useState(false)
  const toggleEye = () => {
    setOpenEye((prev) => !prev)
  }
  const defaultClass =
    'h-10 text-black w-full rounded-md border border-gray-300  p-3 outline-none focus:border-blue-500'
  const disableClass = 'h-10 w-full rounded  border-none text-black '

  const newClassName = `${
    rest.disabled ? disableClass : defaultClass
  } ${className} `
  const handleType = () => {
    if (type === 'password') {
      return openEye ? 'text' : 'password'
    }
    return type
  }
  return (
    <Controller
      name={name}
      control={control}
      defaultValue=""
      render={({ fieldState: { error }, field: { onChange, value, ref } }) =>
        label ? (
          <div className={`relative ${classNameWarp}`}>
            <div className="mb-4">
              <label htmlFor={name}>
                <Text className=" font-bold">{label}</Text>
                {required && (
                  <Text className="ml-1  font-bold text-red-500">*</Text>
                )}
              </label>
            </div>
            <input
              id={name}
              value={value}
              onChange={onChange}
              placeholder={placeholder}
              ref={ref}
              type={handleType()}
              autoComplete="current-password"
              className={`${newClassName} ${error ? 'border-red-600' : ''}`}
              {...rest}
            />
            {error?.type === 'login' ? (
              <div className="mt-2 min-h-[1.25rem] rounded bg-[#efc5d9] p-3 text-sm text-[#d21e1e]">
                {error?.message}
              </div>
            ) : (
              <div className="mt-1 min-h-[1.25rem] text-[12px] text-red-600">
                {error?.message}
              </div>
            )}
            {type === 'password' && openEye && (
              <HiOutlineEye className={classNameEye} onClick={toggleEye} />
            )}
            {type === 'password' && !openEye && (
              <HiOutlineEyeOff className={classNameEye} onClick={toggleEye} />
            )}
          </div>
        ) : (
          <div className={classNameWarp}>
            <input
              id={name}
              value={value}
              onChange={onChange}
              placeholder={placeholder}
              type={handleType()}
              ref={ref}
              autoComplete="current-password"
              className={`${newClassName} ${error ? 'border-red-600' : ''}`}
              {...rest}
            />
            {error?.type === 'login' ? (
              <div className="mt-2 min-h-[1.25rem] rounded bg-[#efc5d9] p-3 text-sm text-[#d21e1e]">
                {error?.message}
              </div>
            ) : (
              <div className="mt-1 min-h-[1.25rem] text-[12px] text-red-600">
                {error?.message}
              </div>
            )}

            {type === 'password' && openEye && (
              <HiOutlineEye className={classNameEye} onClick={toggleEye} />
            )}
            {type === 'password' && !openEye && (
              <HiOutlineEyeOff className={classNameEye} onClick={toggleEye} />
            )}
          </div>
        )
      }
    />
  )
}

export default memo(Input)
