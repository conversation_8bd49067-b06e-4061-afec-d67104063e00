import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

import { checkValidUrlWithRole, getRoleParsed } from './utils/auth'
import { STORAGEKEY } from './services/cookies'

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const accessToken = request.cookies.get(STORAGEKEY.USER_ACCESS_TOKEN)?.value
  const accessTokenAdmin = request.cookies.get(
    STORAGEKEY.ADMIN_ACCESS_TOKEN
  )?.value
  const role = getRoleParsed(accessToken)
  const roleAdmin = getRoleParsed(accessTokenAdmin)
  const checkUrlData = checkValidUrlWithRole({ url: pathname, role, roleAdmin })
  if (!checkUrlData?.isValid) {
    return NextResponse.redirect(new URL(checkUrlData.redirectUrl, request.url))
  }
}

export const config = {
  // <PERSON>er ignoring `/_next/` and `/api/`
  matcher: ['/admin/:path*', '/my-info/:path*', '/', '/my-profile'],
}
