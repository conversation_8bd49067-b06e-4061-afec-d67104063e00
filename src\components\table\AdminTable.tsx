import { Table, Empty, TableProps } from 'antd'
import { MESSAGE_FORM } from '@/utils/string'
import React from 'react'
import {
  createItemRender,
  ItemsRenderConfig,
} from '@/components/ui/ItemsRender'

type CustomTableProps<RecordType extends object = any> =
  TableProps<RecordType> & {
    itemsRender?: () => ItemsRenderConfig
  }

export default function CustomTable<RecordType extends object = any>(
  props: CustomTableProps<RecordType>
) {
  const { pagination, locale, itemsRender, ...restProps } = props

  return (
    <Table<RecordType>
      {...restProps}
      className="custom-table"
      pagination={
        pagination
          ? {
              ...pagination,
              itemRender: createItemRender(itemsRender?.()),
            }
          : pagination
      }
      locale={{
        emptyText: (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={MESSAGE_FORM.noData}
          />
        ),
        ...locale,
      }}
    />
  )
}
