{"name": "get-marketplace-frontend", "version": "0.1.0", "private": true, "engines": {"node": ">=18.14.0", "yarn": ">=1.22.0", "npm": "please-use-yarn-instead"}, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --write .", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "5.2.6", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@hookform/resolvers": "^3.3.1", "@tanstack/react-query": "^5.0.5", "@tinymce/tinymce-react": "^4.3.0", "@types/dompurify": "^3.0.3", "@types/react-datepicker": "^4.10.0", "@types/uuid": "^9.0.1", "@walletconnect/sign-client": "^2.21.7", "antd": "5.12.2", "axios": "^1.5.1", "chart.js": "^4.4.0", "clsx": "^1.2.1", "dayjs": "^1.11.13", "dompurify": "^3.0.6", "dotenv": "^16.3.1", "ethers": "^5", "he": "^1.2.0", "markdown-table": "^3.0.3", "moment": "^2.29.4", "next": "13.2.3", "numeral": "^2.0.6", "react": "18.2.0", "react-chartjs-2": "^5.2.0", "react-cookie": "^6.1.1", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^4.10.0", "react-datetime": "^3.2.0", "react-device-detect": "^2.2.3", "react-dom": "18.2.0", "react-hook-form": "^7.43.1", "react-icons": "^4.11.0", "react-infinite-scroll-component": "^6.1.0", "react-paginate": "^8.1.4", "react-responsive-carousel": "^3.2.23", "react-toastify": "^9.1.1", "socket.io-client": "^4.7.2", "wagmi": "^1.4.12", "xlsx": "^0.18.5", "yup": "^1.0.0", "zustand": "^4.3.2"}, "devDependencies": {"@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "@types/node": "18.14.6", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "autoprefixer": "^10.4.13", "eslint": "8.33.0", "eslint-config-next": "13.1.6", "husky": "^8.0.3", "lint-staged": "^16.1.2", "postcss": "^8.4.21", "prettier": "^2.8.3", "sass": "^1.58.0", "tailwindcss": "^3.2.6", "typescript": "5.2.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}