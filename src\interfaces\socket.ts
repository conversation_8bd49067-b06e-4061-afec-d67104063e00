import { TTransferStatus } from '@/components/modal'

export type TUseSocketUserBuyByCredit = {
  setTransferStatus: React.Dispatch<
    React.SetStateAction<TTransferStatus | undefined>
  >
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>
  setTransactionHash: React.Dispatch<React.SetStateAction<string>>
  setErrStatus: React.Dispatch<React.SetStateAction<string>>
}

export type TSocketResponse = {
  buyerAddress: string
  fincodeOrderId: string
  itemId: number
  itemType: number
  nftName: string
  txHash?: string
  message?: string
}
export type TShowNotification = {
  data: TSocketResponse
  toastType?: 'error' | 'success'
  transferStatus: TTransferStatus
}

export type TUseSocketUserBuyByGetPlatform = {
  setCheckoutStatus: React.Dispatch<
    React.SetStateAction<TTransferStatus | undefined>
  >
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>
  setErrStatus: React.Dispatch<React.SetStateAction<string>>
}
