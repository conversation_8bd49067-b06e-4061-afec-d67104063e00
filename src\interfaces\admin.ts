import { Paginated } from '.'
import { ACCOUNT_STATUS, ADMIN_ROLE } from '../constants'

export type Admin = {
  id: string
  _id: string
  name: string
  email: string
  role: string
  status: string
  createdAt: string
}

export type AdminAccountStats = {
  totalAccounts: number
  totalActiveAccounts: number
  totalDisabledAccounts: number
  totalSystemAdminAccounts: number
  totalOperatorAccounts: number
  totalApproverAccounts: number
}

export type AdminSearch = {
  sortCondition?: string
  email?: string[] | string
  role?: ADMIN_ROLE
  status?: ACCOUNT_STATUS
  pageSize: number
  pageIndex?: number
}

export type CreateAdminBody = {
  email: string
  password: string
  role: ADMIN_ROLE
}

export type PaginatedAdmin = Paginated<Admin>
