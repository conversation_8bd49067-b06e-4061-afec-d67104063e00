import { useRouter } from 'next/router'
import { Col, Row, Checkbox, Form, Input, Typography } from 'antd'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import clsx from 'clsx'
import { CheckboxChangeEvent } from 'antd/es/checkbox'
import UiButton from '@/components/ui/Button'

import { BUTTON_SIZES } from '@/constants'
import {
  FORM_LOGIN_ADMIN,
  LOGIN_ERROR_RESPONSE,
  MIN_LENGTH_PASSWORD,
  ROUTES,
} from '@/constants'
import { IMAGE } from '@/utils/string'
import { IErrorResponse } from '@/interfaces'
import { STORAGEKEY, setCookie } from '@/services/cookies'
import { getAdminAccessToken } from '@/services/apiCall'

interface ValueFormProps {
  email: string
  password: string
  remember: boolean
}

const { Text } = Typography

const Login = () => {
  const [form] = Form.useForm()
  const { push, reload } = useRouter()
  const [accountErrorMessage, setAccountErrorMessage] = useState<string>('')
  const [formInfo, setFormInfo] = useState<{ email: string; password: string }>(
    {
      email: '',
      password: '',
    }
  )
  const rememberMe = localStorage.getItem('rememberMe')

  const handleChangeInput = (value: string, type: string) => {
    setAccountErrorMessage('')
    resetErrorField()

    if (type === FORM_LOGIN_ADMIN.EMAIL) {
      form.getFieldValue('remember') &&
        localStorage.setItem('rememberMe', value)
      setFormInfo((prev) => {
        const newDataForm = { ...prev, email: value }
        return newDataForm
      })
    } else {
      setFormInfo((prev) => {
        const newDataForm = { ...prev, password: value }
        return newDataForm
      })
    }
  }

  const showErrorMessage = (error: IErrorResponse) => {
    const errorMessage =
      error?.message && Array.isArray(error?.message)
        ? error?.message[error.message.length - 1]
        : error?.message
    switch (errorMessage) {
      case LOGIN_ERROR_RESPONSE.INVALID_EMAIL:
        form.setFields([
          {
            name: 'email',
            errors: ['データは不適切です。'],
          },
        ])
        break
      case LOGIN_ERROR_RESPONSE.WRONG_PASSWORD:
      case LOGIN_ERROR_RESPONSE.USER_NOT_FOUND:
      case LOGIN_ERROR_RESPONSE.ADMIN_NOT_FOUND:
        setAccountErrorMessage(
          'メールアドレスまたはパスワードが正しくありません。'
        )
        break
      case LOGIN_ERROR_RESPONSE.ADMIN_HAS_BEEN_BLOCKED:
        setAccountErrorMessage(
          'メールアドレスまたはパスワードが正しくありません。'
        )
        break
      default:
        setAccountErrorMessage('')
    }
  }

  const resetErrorField = () => {
    form.setFields([
      {
        name: 'email',
        errors: [],
      },
    ])
  }

  const onFinish = async (value: ValueFormProps) => {
    const data = await getAdminAccessToken({
      email: value.email.trim(),
      password: value.password,
    })
    if (!data.accessToken) {
      showErrorMessage(data)
    } else {
      setCookie(
        STORAGEKEY.ADMIN_ACCESS_TOKEN,
        data.accessToken,
        data.accessTokenExp
      )
      setCookie(
        STORAGEKEY.ADMIN,
        JSON.stringify(data?.user),
        data.accessTokenExp
      )
      push(ROUTES.adminDashboard)
      reload()
    }
  }

  const onCheckedRememberMeChange = (e: CheckboxChangeEvent) => {
    if (e.target.checked && formInfo?.email) {
      localStorage.setItem('rememberMe', formInfo.email)
    } else {
      localStorage.removeItem('rememberMe')
    }
  }

  useEffect(() => {
    if (rememberMe) {
      form.setFieldsValue({ email: rememberMe })
      form.setFieldsValue({ remember: !!rememberMe })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps

    setTimeout(() => {
      const pwd = form.getFieldValue('password')
      if (pwd) {
        setFormInfo((prev) => ({ ...prev, password: pwd }))
      }
    }, 100)
  }, [])

  const values = form.getFieldsValue()
  const isDisableLogin = !values?.email?.trim() || !values?.password

  return (
    <Row>
      <Col
        xxl={12}
        xl={12}
        className={`hidden lg:flex bg-getBg h-[100vh] w-full items-center justify-center`}
      >
        <Image
          alt="banner login"
          src={IMAGE.bannerLogin}
          width={1300}
          height={1045}
          className="object-cover h-[100vh] w-full"
        />
      </Col>
      <Col
        xxl={12}
        xl={12}
        className="bg-getBg w-full flex flex-col justify-center items-center"
      >
        <Image
          alt="logo"
          src={IMAGE.logoBrandSignin}
          width={271}
          height={60}
          className="mt-20"
        />
        <Form
          form={form}
          onFinish={onFinish}
          className="w-[400px]"
          layout="vertical"
          initialValues={{
            remember: false,
          }}
        >
          <div className="my-10 relative">
            <p className="font-montserrat text-sm leading-[1.8] tracking-[0.02em] max-w-[450px] text-center mx-auto">
              マーケットプレイス管理ダッシュボードにアクセスする.
            </p>
            <p
              className={clsx('text-tailwindFailed absolute', {
                '-translate-y-2': !accountErrorMessage,
                'transition duration-100': accountErrorMessage,
              })}
            ></p>
          </div>
          <Form.Item name="email">
            <Input
              placeholder="メールアドレス"
              className="!font-montserrat !text-sm"
              size="large"
              onChange={(e) =>
                handleChangeInput(e.target.value, FORM_LOGIN_ADMIN.EMAIL)
              }
            />
          </Form.Item>
          <Form.Item
            name="password"
            rules={[
              {
                min: MIN_LENGTH_PASSWORD,
                message: 'パスワードは8文字以上である必要があります',
              },
            ]}
          >
            <Input.Password
              placeholder="パスワード"
              size="large"
              className="!font-montserrat !text-sm"
              onChange={(e) =>
                handleChangeInput(e.target.value, FORM_LOGIN_ADMIN.PASSWORD)
              }
            />
          </Form.Item>
          <Text type="danger">{accountErrorMessage}</Text>
          <Row justify={'space-between'}>
            <Form.Item name="remember" valuePropName="checked">
              <Checkbox
                onChange={onCheckedRememberMeChange}
                className="custom-checkbox"
              >
                ログイン状態を保存する
              </Checkbox>
            </Form.Item>
          </Row>
          <UiButton
            title={<span>ログイン</span>}
            size={BUTTON_SIZES.NORMAL}
            isDisabled={isDisableLogin}
            handleClick={() => form.submit()}
            className="w-full mt-5 h-11 mb-8"
          />
        </Form>
      </Col>
    </Row>
  )
}

export default Login
