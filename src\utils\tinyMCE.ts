import he from 'he'

export const config = {
  height: 600,
  menubar: false,

  content_css: 'dark',
  plugins:
    'autolink emoticons image link lists searchreplace table code codesample',
  toolbar:
    'undo redo | bold italic underline strikethrough | fontsselect fontfamily fontsizeinput formatselect | ' +
    'alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | forecolor backcolor removeformat | ' +
    'pagebreak | charmap emoticons | fullscreen preview save print | template link image insertfile uploadButton | ltr rtl | code | codesample',
  branding: false,
  toolbar_mode: 'wrap',
  toc_depth: 3,
  statusbar: false,
  content_style: `
    body {
      background-color: #0A0A0A;
      color: #ffffff;
      font-family: Helvetica, Arial, sans-serif;
      font-size: 14px;
    }
    a {
      color: #4fc3f7;
    }
    table, th, td {
      border: 1px solid #ffff;
    }
    img {
      border: 1px solid #ffffff26;
    }
    h1, h2, h3, h4, h5, h6 {
      border-bottom: 1px solid #fff;
      padding-bottom: 4px;
      margin-bottom: 12px;
    }
  `,
  image_dimensions: true,
  image_class_list: [{ title: 'responsive', value: 'responsiveImage' }],
  language: 'ja',
  extended_valid_elements: 'a[href|target=_blank]',
  contextmenu_never_use_native: true,
  formats: {
    aligncenter: {
      selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img',
      classes: 'center',
      styles: { display: 'flex', justifyContent: 'center' },
    },
  },
}

export const formatContext = (html: string | undefined, maxLength = 150) => {
  if (!html) return ''
  const textOnly = html
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()
  const decoded = he.decode(textOnly)
  return decoded.length > maxLength
    ? decoded.slice(0, maxLength).trim() + '...'
    : decoded
}
