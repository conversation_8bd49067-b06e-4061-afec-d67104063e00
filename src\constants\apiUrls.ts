export const API_URLS = {
  //user
  userLogin: 'v1/auth/users/login',
  buyNft: 'v1/market/buy/checkout',
  giftCode: 'v1/gift',
  claimNft: 'v1/gift/claim',
  myNftList: 'v1/nfts/minted/users',
  userTransactions: 'v1/transactions/history/users',
  userDetailNft: 'nfts/minted',
  userNews: 'v1/news/user',
  userNew: 'v1/news',
  userNonce: 'v1/auth/nonce',
  checkout: 'v1/market/checkout',
  cancelPaymnet: 'v1/market/cancel-payment',
  contact: 'v1/contact',
  //admin
  users: 'v1/users',
  adminUsers: 'v1/users/admin-view',
  userProfile: 'v1/users/profile',
  adminLogin: 'v1/auth/admin/signin',
  adminCollections: 'v1/collections',
  adminCollectionStats: 'v1/collections/statistics',
  adminUploadCollectionImage: 'v1/files/collections/upload-image',
  adminCreatorsOfCollection: 'v1/admin/collections/creators',
  adminBanners: 'v1/banners',
  adminGetBanners: 'v1/banners/admin',
  adminMetadata: 'v1/nfts/metadata',
  adminUploadImage: 'v1/files/nfts/metadata/upload-image',
  adminImportCsv: 'v1/gift/import-csv',
  adminGetGift: 'v1/gift',
  adminDashboard: 'v1/admin/dashboard',
  adminCategories: 'v1/categories',
  collections: 'v1/market/collections',
  nfts: 'v1/market/nfts',
  collectionNfts: 'v1/market/collections/{id}/nfts',
  news: 'v1/news',
  adminNews: 'v1/news/admin',
  adminNewsImage: 'v1/files/news/upload-image',
  banners: 'v1/banners/user',
  adminFurusatoSetting: 'v1/nfts/furusato-setting',
  adminAccountStats: 'v1/admin/statistics',
  disableAdmin: 'v1/admin/{id}/block',
  enableAdmin: 'v1/admin/{id}/unblock',
  adminList: 'v1/admin',
  createAdmin: 'v1/admin',
  editAdmin: 'v1/admin/{id}',
  adminActivityList: 'v1/activities',
  transactionStatistics: 'v1/transactions/history/statistics',
  adminListTransaction: 'v1/transactions/history',
  adminNftApprovalStats: 'v1/nfts/metadata/statistics',
  adminConfig: 'v1/keys',
} as const
