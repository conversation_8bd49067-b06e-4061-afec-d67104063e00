import { NextApiRequest, NextApiResponse } from 'next'
import { getMarketCollections } from '@/services/apiCall'

const generateSitemapUrl = (url: string, lastmod?: string) => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com'
  return `
  <url>
    <loc>${baseUrl}${url}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const collectionsData = await getMarketCollections({
      pageIndex: 1,
      pageSize: 1000,
      searchWord: '',
      category: '',
    })

    const collectionUrls = collectionsData.items.map((collection: any) =>
      generateSitemapUrl(
        `/collections/${collection._id}`,
        collection.updatedAt ? new Date(collection.updatedAt).toISOString().split('T')[0] : undefined
      )
    )

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${collectionUrls.join('')}
</urlset>`

    res.setHeader('Content-Type', 'text/xml')
    res.setHeader('Cache-Control', 'public, s-maxage=3600, stale-while-revalidate') // Cache for 1 hour
    
    return res.status(200).send(sitemap)
  } catch (error) {
    console.error('Error generating collections sitemap:', error)
    return res.status(500).json({ message: 'Error generating sitemap' })
  }
}
