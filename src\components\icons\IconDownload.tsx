import React from 'react'

type IconProps = {
  size?: number | string
  color?: string
  className?: string
}

const IconUpload: React.FC<IconProps> = ({
  size = 18,
  color = '#F2C157',
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 14"
      fill="none"
      className={className}
    >
      <path
        d="M11 7.25L8 10.25M8 10.25L5 7.25M8 10.25L8 1.25M14.75 10.25V11.25C14.75 12.3546 13.8546 13.25 12.75 13.25H3.25C2.14543 13.25 1.25 12.3546 1.25 11.25L1.25 10.25"
        stroke={color}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default React.memo(IconUpload)
