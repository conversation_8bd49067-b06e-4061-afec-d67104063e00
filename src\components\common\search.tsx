import React, { Dispatch, SetStateAction, FC, KeyboardEvent } from 'react'
import clsx from 'clsx'

import { HiOutlineSearch, HiOutlineX } from 'react-icons/hi'
interface ISearchProps {
  placeholder: string
  handleSearch?: () => void
  handleClearAll?: () => void
  searchValue: string | undefined
  setSearchValue: Dispatch<SetStateAction<string>>
  className?: string
  width?: string
  inputClassName?: string
  onKeyDown?: (e: KeyboardEvent<HTMLInputElement>) => void
  form?: boolean
}

const Search: FC<ISearchProps> = ({
  placeholder = '',
  handleSearch,
  className = '',
  width = 'w-full',
  inputClassName = 'text-sm',
  setSearchValue,
  searchValue = '',
  handleClearAll,
  onKeyDown,
  form = true,
}) => {
  const handleChangeInput = (value: string) => {
    setSearchValue(value)
  }
  const onSubmit = (event) => {
    event.preventDefault()
    handleSearch && handleSearch()
  }

  const renderInputSearch = (
    <div className={clsx('relative', width)}>
      <div
        onClick={onSubmit}
        className="cursor-pointer inset-y-0 flex items-center pl-5 absolute top-0 left-0"
      >
        <HiOutlineSearch className="w-6 h-6 text-tailwindNeutral3" />
      </div>

      <input
        id="nft-search"
        value={searchValue}
        placeholder={placeholder}
        className={clsx(
          inputClassName,
          'bg-tailwindNeutral2 pl-12 h-10 pr-9 border rounded-md focus:border text-tailwindNeutral1 placeholder-white placeholder:text-tailwindNeutral3 font-normal tracking-wider outline-0  block w-full p-2.5 '
        )}
        autoComplete="off"
        onChange={(event) => handleChangeInput(event.target.value)}
        onKeyDown={(e) => onKeyDown && onKeyDown(e)}
      />

      {searchValue && (
        <HiOutlineX
          onClick={handleClearAll}
          className="cursor-pointer w-6 h-6 text-tailwindNeutral3 opacity-70 hover:opacity-100 absolute right-2 top-1/2 -translate-y-1/2"
        />
      )}
    </div>
  )
  return (
    <div className={`${className}`}>
      {form ? (
        <form className="flex items-center " onSubmit={onSubmit}>
          {renderInputSearch}
        </form>
      ) : (
        renderInputSearch
      )}
    </div>
  )
}

export default Search
