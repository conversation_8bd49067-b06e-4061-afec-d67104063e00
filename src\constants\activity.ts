export enum ACTION_TYPE {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  BLOCKED = 'blocked',
  UNBLOCKED = 'unblocked',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

// TODO add japanese
export const ACTION_MAP = new Map([
  [ACTION_TYPE.CREATED, 'CREATED'],
  [ACTION_TYPE.UPDATED, 'UPDATED'],
  [ACTION_TYPE.DELETED, 'DELETED'],
  [ACTION_TYPE.BLOCKED, 'BLOCKED'],
  [ACTION_TYPE.UNBLOCKED, 'UNBLOCKED'],
  [ACTION_TYPE.APPROVED, 'APPROVED'],
  [ACTION_TYPE.REJECTED, 'REJECTED'],
])
