import { UserDetail } from '@/interfaces'
import { IMAGE } from '@/utils/string'
import Link from 'next/link'
import Image from 'next/image'

export const SocialMediaButtons = ({
  className,
  user,
}: {
  className?: string
  user?: UserDetail
}) => {
  const socialMediaItems = [
    [IMAGE.logoX, user?.xUrl],
    [IMAGE.logoFb, user?.facebookUrl],
    [IMAGE.logoIg, user?.instagramUrl],
  ]

  return (
    <div className={`flex justify-center space-x-[10px] ${className || ''}`}>
      {socialMediaItems.map(([logoUrl, socialMediaUrl]) =>
        socialMediaUrl ? (
          <button
            key={logoUrl}
            className="bg-getCardBg rounded-[8px] h-[40px] w-[40px] flex justify-center items-center hover:bg-neutral-700 transition-all duration-300"
          >
            <Link href={socialMediaUrl} target="_blank">
              <Image src={logoUrl} alt="logo" width={20} height={20} />
            </Link>
          </button>
        ) : (
          <button
            key={logoUrl}
            className="bg-getCardBg rounded-[8px] h-[40px] w-[40px] flex justify-center items-center hover:bg-neutral-700 transition-all duration-300"
          >
            <Image src={logoUrl} alt="logo" width={20} height={20} />
          </button>
        )
      )}
    </div>
  )
}
