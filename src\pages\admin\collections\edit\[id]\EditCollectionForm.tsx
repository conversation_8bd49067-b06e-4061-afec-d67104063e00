import React, { useState, useEffect } from 'react'
import { Form, Radio, Input, Select, Row, Col } from 'antd'
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES } from '@/constants/enum'
import Text from '@/components/texts/Text'
import UploadImage from '@/components/uploadImages/UploadImages'
import { useRouter } from 'next/router'
import { useGet } from '@/hooks/useGet'
import { usePatch } from '@/hooks/usePatch'
import { getCollection, updateCollection } from '@/services/apiCall/collections'
import { getListCategories } from '@/services/apiCall/categories'
import { API_URLS } from '@/constants/apiUrls'
import { post } from '@/services/apiCall/baseApi'
import { toast } from 'react-toastify'
import { ROLE } from '@/constants/auth'
import { CollectionPayload } from '@/interfaces/collection'
import { APPROVAL_STATUS, CONTRACT_TYPES } from '@/constants/common'
import { getImageIdFromUrl } from '@/utils/uploadImages'
import { useStore } from '@/store'
import { getPermission } from '@/utils'

const { TextArea } = Input

const TEXT_LABELS = {
  CREATE_NFT_COLLECTION: 'NFTコレクションを作成',
  STANDARD: 'コレクションの標準をお選びください',
  NAME: 'コレクション名',
  CATEGORY: 'カテゴリー',
  DESCRIPTION: 'コレクション紹介',
  UPLOAD_LOGO: 'ロゴ画像をアップロード',
  UPLOAD_BACKGROUND: '背景画像をアップロード',
  SAVE_DRAFT: 'ドラフトを保存',
  UPDATE: 'アップデート',
  NAME_YOUR_NFT: 'NFTに名前を付けます',
  DESCRIPTION_PLACEHOLDER: '説明...',
  PLEASE_ENTER_NAME: 'コレクション名を入力してください',
  PLEASE_ENTER_DESCRIPTION: 'コレクションの説明を入力してください',
  PLEASE_SELECT_CATEGORY: 'カテゴリーを選択してください',
  PLEASE_SELECT_STANDARD: 'コレクションの標準を選択してください',
  PLEASE_UPLOAD_LOGO: 'ロゴ画像をアップロードしてください',
  PLEASE_UPLOAD_BACKGROUND: '背景画像をアップロードしてください',
  FAILED_TO_UPLOAD_LOGO: 'ロゴ画像のアップロードに失敗しました。',
  FAILED_TO_UPLOAD_BACKGROUND: '背景画像のアップロードに失敗しました。',
  CANNOT_EXCEED_1000: '説明は1000文字以内でなければなりません',
  CANNOT_EXCEED_100: '説明は100文字以内でなければなりません',
}

export default function EditCollectionForm() {
  const role = useStore((state) => state.role)
  const [form] = Form.useForm()
  const router = useRouter()
  const { id } = router.query
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [bgFile, setBgFile] = useState<File | null>(null)
  const [logoImageUrl, setLogoImageUrl] = useState<string>('')
  const [bgImageUrl, setBgImageUrl] = useState<string>('')

  // Ensure imageUrl is cleared when imageFile is removed
  const handleLogoFileChange = (file: File | null) => {
    setLogoFile(file)
    if (!file) {
      setLogoImageUrl('')
    }
  }
  const handleBgFileChange = (file: File | null) => {
    setBgFile(file)
    if (!file) {
      setBgImageUrl('')
    }
  }
  const [loading, setLoading] = useState(false)

  // Fetch collection data
  const { data: collection } = useGet({
    queryKey: ['collection', id],
    callback: () => {
      if (id) {
        return getCollection(id as string)
      }
      return Promise.resolve(null)
    },
  })

  // Fetch categories
  const { data: categoriesData, isLoading: loadingCategories } = useGet({
    queryKey: ['categories'],
    callback: () => getListCategories(),
  })
  const categories =
    categoriesData?.items?.map((cat: any) => ({
      value: cat._id,
      label: cat.name,
    })) || []

  // Patch hook for update
  const { mutate: patchCollection, isPending: updating } = usePatch({
    queryKey: ['collection', id],
    callback: async ({
      id,
      payload,
    }: {
      id: string
      payload: CollectionPayload
    }) => {
      return updateCollection(
        id,
        payload,
        (role as ROLE.systemAdmin) || (role as ROLE.operator)
      )
    },
  })

  const { canEdit } = getPermission({
    approvalStatus: collection?.approvalStatus,
    creatorId: collection?.createdBy._id,
  })

  // Populate form when collection is loaded
  useEffect(() => {
    if (collection) {
      form.setFieldsValue({
        contractType: collection?.standard,
        name: collection?.name,
        category: collection?.category,
        description: collection?.description,
      })
      setLogoImageUrl(collection?.logoImage)
      setBgImageUrl(collection?.backgroundImage)
    }
  }, [collection, form])

  const uploadImage = async (file: File, label: string) => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      const res = await post(
        API_URLS.adminUploadCollectionImage,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
        ROLE.systemAdmin
      )
      return res
    } catch (err) {
      toast.error(`${label} upload failed!`)
      return ''
    }
  }

  const handleLogoImage = async (): Promise<string | null> => {
    if (logoFile) {
      const uploadedId = await uploadImage(logoFile, 'Logo image')
      if (!uploadedId) {
        toast.error(TEXT_LABELS.FAILED_TO_UPLOAD_LOGO)
        setLogoFile(null)
        return null
      }
      return uploadedId
    }

    if (logoImageUrl) {
      return getImageIdFromUrl(logoImageUrl) || ''
    }

    toast.error(TEXT_LABELS.PLEASE_UPLOAD_LOGO)
    setLogoFile(null)
    return null
  }

  const handleBackgroundImage = async (): Promise<string | null> => {
    if (bgFile) {
      const uploadedId = await uploadImage(bgFile, 'Background image')
      if (!uploadedId) {
        toast.error(TEXT_LABELS.FAILED_TO_UPLOAD_BACKGROUND)
        setBgFile(null)
        return null
      }
      return uploadedId
    }

    if (bgImageUrl) {
      return getImageIdFromUrl(bgImageUrl) || ''
    }

    toast.error(TEXT_LABELS.PLEASE_UPLOAD_BACKGROUND)
    setBgFile(null)
    return null
  }

  // Form submit handler
  const handleSubmit = async (values: any, isDraft = false) => {
    setLoading(true)

    let logoImageId = ''
    let backgroundImageId = ''

    logoImageId = (await handleLogoImage()) || ''
    backgroundImageId = (await handleBackgroundImage()) || ''

    // Prepare payload
    const payload: CollectionPayload = {
      name: values.name,
      standard: values.contractType,
      description: values.description,
      category: values.category,
      logoImageId,
      backgroundImageId,
      isDraft,
      approvalStatus: isDraft ? APPROVAL_STATUS.draft : APPROVAL_STATUS.waiting,
    }
    // Submit collection
    patchCollection(
      { id: id as string, payload },
      {
        onSuccess: () => {
          form.resetFields()
          setLogoFile(null)
          setBgFile(null)
          setLoading(false)
          router.back()
        },
        onError: () => {
          setLogoFile(null)
          setBgFile(null)
          setLoading(false)
        },
      }
    )
  }

  const onFinish = async (values: any) => {
    // Default behavior - update collection (not draft)
    await handleSubmit(values, false)
  }

  const onSaveDraft = async () => {
    // Save as draft
    const values = await form.validateFields()
    await handleSubmit(values, true)
  }

  return (
    <Row>
      <Col xs={24} md={10}>
        <UploadImage
          width="w-[60%]"
          height="h-1/2"
          label={TEXT_LABELS.UPLOAD_LOGO}
          recommendedSize="350 x 350"
          required={true}
          value={logoFile}
          imageUrl={logoImageUrl}
          onChange={handleLogoFileChange}
          maxSize={2}
          setImageUrl={setLogoImageUrl}
          disabled={!canEdit}
        />
        <UploadImage
          height="h-1/2"
          label={TEXT_LABELS.UPLOAD_BACKGROUND}
          recommendedSize="1920 x 1080"
          required
          value={bgFile}
          imageUrl={bgImageUrl}
          onChange={handleBgFileChange}
          maxSize={5}
          setImageUrl={setBgImageUrl}
          disabled={!canEdit}
        />
      </Col>
      <Col xs={24} md={12}>
        <Form
          requiredMark={false}
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="flex flex-row gap-8 w-full text-white"
        >
          <Row>{loadingCategories && <span>Loading categories...</span>}</Row>
          <div className="flex flex-col flex-1 gap-6">
            <div>
              <Form.Item
                name="contractType"
                initialValue={CONTRACT_TYPES[0]}
                rules={[
                  { required: true, message: 'Choose your contract type' },
                ]}
                className="mb-2"
                label={
                  <Text require={true} className="text-xs font-montserrat">
                    {TEXT_LABELS.STANDARD}
                  </Text>
                }
              >
                <Radio.Group className="flex gap-8" disabled={!canEdit}>
                  {CONTRACT_TYPES.map((type) => (
                    <Radio key={type} value={type}>
                      {type}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </div>
            <Form.Item
              name="name"
              label={
                <Text require={true} className="text-xs font-montserrat">
                  {TEXT_LABELS.NAME}
                </Text>
              }
              rules={[
                { required: true, message: TEXT_LABELS.PLEASE_ENTER_NAME },
                { max: 100, message: TEXT_LABELS.CANNOT_EXCEED_100 },
                {
                  whitespace: true,
                  message: TEXT_LABELS.PLEASE_ENTER_NAME,
                },
              ]}
              className="mb-2"
            >
              <Input
                size="large"
                placeholder={TEXT_LABELS.NAME_YOUR_NFT}
                maxLength={100}
                showCount
                required
                disabled={!canEdit}
              />
            </Form.Item>
            <Form.Item
              name="category"
              label={
                <Text require={true} className="text-xs font-montserrat">
                  {TEXT_LABELS.CATEGORY}
                </Text>
              }
              rules={[
                { required: true, message: TEXT_LABELS.PLEASE_SELECT_CATEGORY },
              ]}
              className="mb-2"
            >
              <Select
                options={categories}
                placeholder="Category"
                size="large"
                loading={loadingCategories}
                disabled={loadingCategories || !canEdit}
              />
            </Form.Item>
            <Form.Item
              name="description"
              label={
                <Text require={true} className="text-xs font-montserrat">
                  {TEXT_LABELS.DESCRIPTION}
                </Text>
              }
              rules={[
                {
                  required: true,
                  message: TEXT_LABELS.PLEASE_ENTER_DESCRIPTION,
                },
                {
                  max: 1000,
                  message: TEXT_LABELS.CANNOT_EXCEED_1000,
                },
                {
                  whitespace: true,
                  message: TEXT_LABELS.PLEASE_ENTER_DESCRIPTION,
                },
              ]}
              className="mb-1"
            >
              <TextArea
                rows={4}
                placeholder={TEXT_LABELS.DESCRIPTION_PLACEHOLDER}
                className="w-full border border-[#333] p-4"
                maxLength={1000}
                showCount
                required
                disabled={!canEdit}
              />
            </Form.Item>
            <div className="flex flex-col gap-3 mt-6">
              <UiButton
                title={TEXT_LABELS.SAVE_DRAFT}
                size={BUTTON_SIZES.LG}
                isGradient={false}
                handleClick={onSaveDraft}
                className="w-full border border-[#333] !bg-transparent text-white"
                isDisabled={loading || updating || !canEdit}
              />
              <UiButton
                title={TEXT_LABELS.UPDATE}
                size={BUTTON_SIZES.LG}
                isGradient={true}
                handleClick={form.submit}
                className="w-full"
                isLoading={loading || updating}
                isDisabled={loading || updating || !canEdit}
              />
            </div>
          </div>
        </Form>
      </Col>
    </Row>
  )
}
