import { Button, Carousel, CarouselProps } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'
import { CarouselRef } from 'antd/es/carousel'
import clsx from 'clsx'
import { useQuery } from '@tanstack/react-query'

import { getBanners } from '@/services/apiCall'
import { TBanner } from '@/interfaces/banner'
import { DEFAULT_HOMEPAGE } from '@/constants'
import { useViewport } from '@/hooks'
import { getBannerRedirectLink } from '@/utils'

const CarouselComponent = (props: CarouselProps) => {
  const carouselRef = useRef<CarouselRef>(null)
  const [imageIndex, setImageIndex] = useState({
    next: DEFAULT_HOMEPAGE.defaultImageBannerIndex,
    prev: DEFAULT_HOMEPAGE.defaultImageBannerIndex,
  })
  const { windowWidth } = useViewport()
  const { data: bannerData, isLoading: isLoadingBanner } = useQuery({
    queryKey: ['get_banners'],
    queryFn: () => getBanners({ pageSize: DEFAULT_HOMEPAGE.bannerPageSize }),
  })

  const nextPage = () => {
    // If the length of the array is less than 2, clicks are not allowed
    if (bannerData?.items && bannerData?.items.length < 2) return
    carouselRef.current?.next()
  }

  const previousPage = () => {
    // If the length of the array is less than 2, clicks are not allowed
    if (bannerData?.items && bannerData?.items.length < 2) return
    carouselRef.current?.prev()
  }

  const calculatorIndexImage = (currentIndex: number, banners: TBanner[]) => {
    if (windowWidth < 1200) return //disable show left and right banner if width < 1200px
    switch (currentIndex) {
      // case: currentIndex is first
      case 0:
        setImageIndex({
          prev: banners.length - 1,
          next: currentIndex + 1,
        })
        break
      // case: currentIndex is last
      case banners.length - 1:
        setImageIndex({
          prev: currentIndex - 1,
          next: 0,
        })
        break
      default:
        setImageIndex({
          prev: currentIndex - 1,
          next: currentIndex + 1,
        })
    }
  }

  useEffect(() => {
    if (!carouselRef.current) return
  }, [])

  useEffect(() => {
    // If array length is greater than 1, set initial next is next index of first image and previous is last index,
    // Else next and previous are both the only index 0
    if (bannerData?.items && bannerData?.items?.length > 1) {
      setImageIndex({
        next: 1,
        prev: bannerData.items.length - 1,
      })
    }
  }, [bannerData])
  if (isLoadingBanner) return null
  return (
    <div className="flex max-h-[350px] gap-6 w-full h-full">
      {bannerData?.items && bannerData?.items?.length > 0 && (
        <>
          <div className="hidden min-[1200px]:block h-[350px] w-[20vw] opacity-50">
            <Image
              src={bannerData?.items[imageIndex.prev]?.image as string}
              height={350}
              width={600}
              alt="banner"
              className="w-full h-full object-cover object-right"
            />
          </div>
          <div className="relative min-[1200px]:w-[1200px] xl:h-[350px] w-full">
            <Carousel
              afterChange={(current: number) =>
                calculatorIndexImage(current, bannerData?.items as TBanner[])
              }
              ref={carouselRef}
              autoplay={bannerData.items.length > 1 && props.autoplay}
            >
              {bannerData?.items?.map((item) => (
                <Link
                  key={item.index}
                  href={getBannerRedirectLink(item)}
                  className="cursor-pointer lg:h-[350px] lg:w-full md:h-[300px] sm:h-[250px] bg-white"
                >
                  <Image
                    key={item.index}
                    src={item.image}
                    height={350}
                    width={1200}
                    alt="banner"
                    className="object-cover h-[140px] lg:h-[350px] lg:w-full md:h-[300px] sm:h-[250px] w-auto m-auto"
                  />
                </Link>
              ))}
            </Carousel>
            <Button
              className="hidden md:absolute md:block top-1/2 translate-y-[-50%] left-[60px] bg-white border-none"
              icon={
                <LeftOutlined rev={'left'} className="text-tailwindNeutral1" />
              }
              size="large"
              shape="circle"
              onClick={previousPage}
            />
            <Button
              className={clsx(
                'hidden md:absolute md:block top-1/2 translate-y-[-50%] right-[60px] bg-white border-none'
              )}
              icon={
                <RightOutlined
                  rev={'right'}
                  className="text-tailwindNeutral1"
                />
              }
              size="large"
              shape="circle"
              onClick={nextPage}
            />
          </div>
          <div className="hidden min-[1200px]:block h-[350px] w-[20vw] opacity-50 ">
            <Image
              src={bannerData?.items[imageIndex.next]?.image as string}
              height={350}
              width={600}
              alt="banner"
              className=" w-full h-full object-cover object-left"
            />
          </div>
        </>
      )}
    </div>
  )
}

export default CarouselComponent
