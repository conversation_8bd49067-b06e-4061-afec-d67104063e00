import Head from 'next/head'
import React, { useState } from 'react'

import { STORAGEKEY, getCookie } from '@/services/cookies'
import { useTranslate } from '@/hooks'
import MyProfile from './MyProfile'
import MyNFTs from './MyNFTs'
import MyTransactionHistory from './MyTransactionHistory'

function MyProfilePage() {
  const walletAddress = getCookie(STORAGEKEY.WALLET_ADDRESS)
  const trans = useTranslate()
  const [isUpdatingAva, setIsUpdating] = useState<boolean>(false)

  return (
    <>
      <Head>
        <title>{trans.my_profile_title}</title>
      </Head>

      {/* Main Content */}
      <div className="min-h-screen">
        {/* User Profile Section */}
        <MyProfile
          walletAddress={walletAddress}
          isUpdatingAva={isUpdatingAva}
          setIsUpdating={setIsUpdating}
        />

        {/* My NFTs Section */}
        <MyNFTs
          walletAddress={walletAddress}
          isUpdatingAva={isUpdatingAva}
          setIsUpdating={setIsUpdating}
        />

        {/* Purchase History Section */}
        <MyTransactionHistory walletAddress={walletAddress} />
      </div>
    </>
  )
}

export default MyProfilePage
