import { Input, Space, Select, Checkbox, Empty } from 'antd'
import UiButton from '@/components/ui/Button'
import { TextAdminTitle } from '../../../components/texts/TextAdminTitle'
import { IconPen, IconSearch } from '../../../icons'
import moment from 'moment'
import { useState, useEffect } from 'react'
import {
  getListCollections,
  getCreatorsOnCollection,
  updateCollectionPriority,
} from '@/services/apiCall/collections'
import { useQuery } from '@tanstack/react-query'
import { Collection, CollectionSearch } from '@/interfaces'
import { useDebounce } from '@/hooks'
import {
  APPROVAL_STATUS,
  DEBOUNCE_TIME,
  DEFAULT_PAGE_SIZE,
  ROUTES,
} from '@/constants'
import { STATUS_TEXT } from '@/constants/status'
import { BUTTON_SIZES } from '@/constants'
import router from 'next/router'
import { useDelete } from '@/hooks'
import { deleteCollection } from '@/services/apiCall/collections'
import UiModal from '@/components/ui/Modal'
import { OverlayLoading } from '@/components/common'
import { usePatch } from '@/hooks/usePatch'
import { useStore } from '@/store'
import { getPermission } from '@/utils'
import { ROLE } from '@/constants'
import { MESSAGE_FORM } from '@/utils/string'
import IconDelete from '@/components/icons/IconDelete'
import AdminTable from '@/components/table/AdminTable'
import ApprovalStatusBadge from '@/components/badge/ApprovalStatusBadge'

interface AdminCollection extends Collection {
  status?: string
  createdBy?: {
    _id: string
    email: string
  }
  isPriority?: boolean
  approvalStatus?: APPROVAL_STATUS
}

export default function ListCollections() {
  const collectionLabels = {
    collections: 'コレクション',
    createCollection: 'コレクション作成',
    searchCollectionsByName: 'コレクションを名前で検索',
    overview: 'オーバービュー',
    manageCollections: 'コレクション管理',
    name: '名前',
    description: '紹介',
    status: '状態',
    creator: '作成者',
    createdDate: '作成日',
    action: 'アクション',
    createCollectionGuide: 'NFTのコレクションを作成しましょう',
    cancel: 'キャンセル',
    yes: 'はい',
    doYouWantDelete: 'を削除しますか',
    priority: '優先度',
    nonPriority: '非優先度',
    all: 'すべて',
    approver: '承認者',
    approveDate: '承認日',
    view: '詳細',
  }
  const role = useStore((state) => state.role)
  const { canCreate } = getPermission({})

  const [searchName, setSearchName] = useState<string>('')
  const debouncedSearchName = useDebounce<string>(searchName, DEBOUNCE_TIME)
  const [collectionStatus, setCollectionStatus] =
    useState<APPROVAL_STATUS | null>(null)
  const [editableCollections, setEditableCollections] = useState<
    AdminCollection[]
  >([])
  const [collectionByCreator, setCollectionByCreator] = useState<string | null>(
    null
  )
  const [collectionByPriority, setCollectionByPriority] = useState<
    boolean | null
  >(null)
  const [collectionQueryParams, setCollectionQueryParams] =
    useState<CollectionSearch>({
      pageSize: DEFAULT_PAGE_SIZE,
      pageIndex: 1,
      searchWord: '',
    })
  const [creatorsOfCollectionsQueryParams] = useState<CollectionSearch>({
    pageSize: DEFAULT_PAGE_SIZE,
    pageIndex: 1,
  })

  // Modal state for success/error
  const [confirmVisible, setConfirmVisible] = useState(false)
  const [selectedCollection, setSelectedCollection] =
    useState<AdminCollection | null>(null)

  // Update query params when searchName changes
  useEffect(() => {
    setCollectionQueryParams((prev: CollectionSearch) => ({
      ...prev,
      searchWord: debouncedSearchName,
      approvalStatus: collectionStatus as unknown as APPROVAL_STATUS,
      createdBy: collectionByCreator,
      isPriority: collectionByPriority,
      pageSize: DEFAULT_PAGE_SIZE,
      pageIndex: 1,
    }))
  }, [
    debouncedSearchName,
    collectionStatus,
    collectionByCreator,
    collectionByPriority,
  ])

  const { data: listCollections = { items: [], totalItems: 0 }, isLoading } =
    useQuery({
      queryKey: ['list-collections', collectionQueryParams],
      queryFn: () => getListCollections(collectionQueryParams),
    })

  useEffect(() => {
    if (listCollections?.items) {
      setEditableCollections(listCollections.items)
    }
  }, [listCollections?.items])

  const { data: listCreatorsOfCollections = { items: [], totalItems: 0 } } =
    useQuery({
      queryKey: ['list-creators-of-collections', collectionQueryParams],
      queryFn: () => getCreatorsOnCollection(creatorsOfCollectionsQueryParams),
    })

  const { mutate, isPending } = useDelete({
    queryKey: ['list-collections'],
    callback: (ids: string | string[]) =>
      deleteCollection(ids, role as ROLE.systemAdmin | ROLE.operator),
  })

  // Handle pagination
  const handleTableChange = (pagination: any) => {
    setCollectionQueryParams((prev) => ({
      ...prev,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize,
    }))
  }

  const handleDelete = (collection: AdminCollection) => {
    setSelectedCollection(collection)
    setConfirmVisible(true)
  }

  const handleConfirmDelete = () => {
    if (!selectedCollection) return
    mutate(selectedCollection._id as string, {
      onSuccess: () => {
        setConfirmVisible(false)
        setSelectedCollection(null)
      },
      onError: () => {
        setConfirmVisible(false)
        setSelectedCollection(null)
      },
    })
  }

  const handleCancelDelete = () => {
    setConfirmVisible(false)
    setSelectedCollection(null)
  }

  // Patch hook for updating collection priority
  const { mutate: patchCollection } = usePatch({
    queryKey: ['collections'],
    callback: async ({
      _id,
      isPriority,
    }: {
      _id: string
      isPriority: boolean
    }) => {
      const response = await updateCollectionPriority(
        _id,
        { isPriority },
        role as ROLE.systemAdmin | ROLE.approver
      )
      return response
    },
  })

  const handleTogglePriority = (_id: string) => {
    setEditableCollections((prev) =>
      prev.map((collection) =>
        collection._id === _id
          ? { ...collection, isPriority: !collection.isPriority }
          : collection
      )
    )
    const toggled = editableCollections.find(
      (collection) => collection._id === _id
    )?.isPriority

    patchCollection({ _id, isPriority: !toggled })
  }

  const columns = [
    {
      title: collectionLabels.priority,
      dataIndex: 'isPriority',
      key: 'priority',
      width: '70px',
      render: (_: boolean, record: AdminCollection) => {
        const { canSetPriority } = getPermission({
          approvalStatus: record.approvalStatus,
        })
        if (canSetPriority) {
          return (
            <div className="flex items-center justify-center">
              <Checkbox
                checked={record.isPriority}
                onChange={() => handleTogglePriority(record._id as string)}
              />
            </div>
          )
        }
      },
    },
    {
      title: collectionLabels.name,
      dataIndex: 'name',
      key: 'name',
      width: '200px',
      render: (name: string) => (
        <span
          title={name}
          className="block max-w-xs truncate"
          style={{ maxWidth: 200 }}
        >
          {name}
        </span>
      ),
    },
    {
      title: collectionLabels.description,
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      width: '250px',
      render: (desc: string) => (
        <span
          title={desc}
          className="block max-w-xs truncate"
          style={{ maxWidth: 200 }}
        >
          {desc}
        </span>
      ),
    },
    {
      title: collectionLabels.status,
      dataIndex: 'approvalStatus',
      key: 'status',
      width: '150px',
      render: (approvalStatus: APPROVAL_STATUS) => (
        <ApprovalStatusBadge status={approvalStatus} />
      ),
    },
    {
      title: collectionLabels.creator,
      dataIndex: ['createdBy', 'email'],
      key: 'creator',
      width: '200px',
      render: (_: any, record: AdminCollection) =>
        record.createdBy?.email || '-',
    },
    {
      title: collectionLabels.createdDate,
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '100px',
      render: (createdAt: Date | string) =>
        moment(createdAt).format('YYYY-MM-DD'),
    },
    {
      title: collectionLabels.approver,
      dataIndex: 'approvedBy',
      key: 'approvedBy',
      width: '200px',
      render: (approvedBy: { email: string }) => approvedBy?.email || '-',
    },
    {
      title: collectionLabels.approveDate,
      dataIndex: 'approvedAt',
      key: 'approvedAt',
      width: '150px',
      render: (approvedAt: Date | string | null) =>
        approvedAt ? moment(approvedAt).format('YYYY-MM-DD') : '-',
    },
    {
      title: collectionLabels.action,
      dataIndex: 'approvedAt',
      width: '150px',
      render: (_: any, record: AdminCollection) => {
        const { canEdit, canDelete } = getPermission({
          approvalStatus: record.approvalStatus,
          creatorId: record.createdBy?._id || '',
        })
        return (
          <Space className="flex items-center justify-around gap-2">
            <UiButton
              className="w-full !text-primary"
              title={
                <span className="flex items-center gap-1">
                  <span className="mr-1">{<IconPen />}</span>
                </span>
              }
              handleClick={() =>
                router.push(`/admin/collections/edit/${record._id}`)
              }
              isGradient={false}
              isDisabled={!canEdit}
            />
            <UiButton
              className="w-full flex !text-primary"
              title={
                <span className="flex items-center gap-1">
                  <span className="mr-1">{<IconDelete />}</span>
                </span>
              }
              handleClick={() => handleDelete(record)}
              isGradient={false}
              isDisabled={!canDelete}
            />
            <UiButton
              className="w-full flex !text-primary"
              title={
                <span className="flex items-center gap-1">
                  <span className="mr-1">{collectionLabels.view}</span>
                </span>
              }
              handleClick={() => {
                if (record.approvalStatus === APPROVAL_STATUS.approved) {
                  router.push(`/admin/collections/detail/${record._id}`)
                } else {
                  router.push(`/admin/collections/view/${record._id}`)
                }
              }}
              isGradient={false}
            />
          </Space>
        )
      },
    },
  ]

  return (
    <div className="min-w-md">
      <TextAdminTitle title={collectionLabels.collections} className="mt-10" />
      <div className="mb-6 flex justify-between sm:gap-4 gap-2">
        <div className="flex flex-wrap gap-2 sm:gap-4 flex-1">
          <Input
            size="middle"
            placeholder={`${collectionLabels.searchCollectionsByName}...`}
            suffix={<IconSearch height={20} width={20} />}
            className="h-[44px] w-1/3 [@media(max-width:914px)]:w-1/2 [@media(max-width:811px)]:!w-3/4"
            value={searchName}
            onChange={(e) => setSearchName(e.target.value)}
            allowClear
          />
          <Select
            size="middle"
            placeholder={collectionLabels.status}
            options={[
              { label: collectionLabels.all, value: null },
              ...Object.values(APPROVAL_STATUS).map((status) => ({
                label: STATUS_TEXT[status],
                value: status,
              })),
            ]}
            onChange={(value) =>
              setCollectionStatus(value as APPROVAL_STATUS | null)
            }
            className="h-[44px] !w-[202px]"
            allowClear
          />

          <Select
            size="middle"
            placeholder={collectionLabels.creator}
            options={[
              { label: collectionLabels.all, value: null },
              ...listCreatorsOfCollections.items.map((creator) => ({
                label: creator.email,
                value: creator.email,
              })),
            ]}
            className="h-[44px] w-[120px]"
            onChange={(value) => setCollectionByCreator(value as string | null)}
            allowClear
          />
          <Select
            size="middle"
            placeholder={collectionLabels.priority}
            options={[
              { label: collectionLabels.all, value: null },
              { label: collectionLabels.priority, value: true },
              { label: collectionLabels.nonPriority, value: false },
            ]}
            onChange={(value) =>
              setCollectionByPriority(value as boolean | null)
            }
            className="h-[44px] w-[120px]"
            allowClear
          />
        </div>
        {canCreate && (
          <div className="my-3 [@media(min-width:945px)]:my-0 float-right">
            <UiButton
              size={BUTTON_SIZES.NORMAL}
              title={collectionLabels.createCollection}
              handleClick={() => router.push(ROUTES.adminCreateCollection)}
              isDisabled={!canCreate}
            ></UiButton>
          </div>
        )}
      </div>
      <AdminTable
        className="custom-table table-fixed"
        columns={columns}
        dataSource={editableCollections || []}
        rowKey="_id"
        loading={isLoading}
        pagination={{
          pageSize: collectionQueryParams.pageSize,
          current: collectionQueryParams.pageIndex,
          total: listCollections?.totalItems || 0,
          position: ['bottomCenter'],
          showSizeChanger: false,
        }}
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={MESSAGE_FORM.noData}
            />
          ),
        }}
        onChange={handleTableChange}
        scroll={{ x: 'max' }}
      />
      <UiModal
        header={'コレクションの削除'}
        isOpen={confirmVisible}
        onClose={handleCancelDelete}
        cancelButton={{
          isShow: true,
          title: collectionLabels.cancel,
          action: handleCancelDelete,
        }}
        confirmButton={{
          isShow: true,
          title: collectionLabels.yes,
          action: handleConfirmDelete,
        }}
      >
        {selectedCollection ? (
          <p className="text-center text-sm font-semibold">
            <span className="text-red-500">{`「${selectedCollection.name}」`}</span>
            {collectionLabels.doYouWantDelete}?
          </p>
        ) : (
          ''
        )}
      </UiModal>
      {isPending && <OverlayLoading />}
    </div>
  )
}
