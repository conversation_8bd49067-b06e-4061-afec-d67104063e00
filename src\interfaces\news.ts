export enum NewStatus {
  DRAFT = 'draft',
  APPROVED = 'APPROVED',
}

import { APPROVAL_STATUS } from '@/constants'

export interface TNewsItem {
  _id?: string
  title: string
  imageId?: string
  context: string
  imageUrl?: string
  approvalStatus: APPROVAL_STATUS
  publishedAt?: string
  createdAt?: string
  updatedAt?: string
}

export type TNewsData = {
  items: TNewsItem[]
  pageIndex: number
  pageSize: number
  totalItems: number
}

export type TNewsPayload = {
  title: string
  imageId?: string
  context: string
  approvalStatus?: NewStatus
}

export type TNewsApprovePayload = {
  approvalStatus: APPROVAL_STATUS
}
