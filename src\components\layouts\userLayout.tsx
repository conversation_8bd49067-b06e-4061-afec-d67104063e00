import { AppProvider } from '@/hooks/useAppContext'
import Footer from './footer'
import Header from './header'
import { CookiesProvider } from 'react-cookie'

const UserLayout = ({ children }: { children: React.ReactElement }) => (
  <CookiesProvider defaultSetOptions={{ path: '/' }}>
    <AppProvider>
      <div className="min-h-screen relative flex flex-col wrapper main-layout !bg-getBg px-auto">
        <Header />
        <main className="2xl:pt-[var(--height-header)] pt-[80px] grow shrink-0 basis-auto w-auto">
          <div className="xl:w-[1200px] m-auto xl:px-0 sm:px-12 px-6">
            {children}
          </div>
        </main>
        <Footer />
      </div>
    </AppProvider>
  </CookiesProvider>
)

export default UserLayout
