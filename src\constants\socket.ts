export const socketEvents = {
  CREDIT_AUTH_PAYMENT_SUCCESS: 'creditAuthPaymentSuccess',
  CREDIT_PROCESS_START: 'creditStartProcess',
  CREDIT_PROCESS_SUCCESS: 'creditSucceedProcess',
  CREDIT_PROCESS_FAILED: 'creditFailedProcess',
  CREDIT_PROCESS_DENIED: 'creditDeniedProcess',
  CREDIT_PROCESS_DONE: 'creditDoneProcess',
  WEBHOOK_RESPONSE: 'webhookResponse',
}

export enum SOCKET_EVENT {
  GET_PLATFORM_PAYMENT_SUCCESS = 'getPlatformPaymentSuccess',
  GET_PLATFORM_PAYMENT_FAILED = 'getPlatformPaymentFailed',
  CHECKOUT_SESSION_EXPIRED = 'checkoutSessionExpired',
}
