import { IIcon } from '@/interfaces'

export const DefaultAvatar = (props: IIcon) => {
  const { width = '204', height = '204' } = props
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 204 204"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width={204} height={204} rx={102} fill="#1c1c1c" />
      <g clipPath="url(#clip0_187_488)">
        <path
          d="M80.238 100.248C86.3419 104.707 93.8788 107.308 102 107.308C121.86 107.308 139.153 90.1174 139.153 70.1538C139.153 50.1903 122.487 33 102 33C81.5119 33 64.8457 49.6662 64.8457 70.1538C64.8457 82.5208 70.8965 93.5077 80.238 100.248Z"
          fill="#d4d4d4"
        />
        <path
          d="M134.695 104.919C126.15 112.987 114.632 117.923 102 117.923C89.3677 117.923 77.85 112.987 69.3046 104.919C47.7023 116.596 33 139.472 33 165.692C33 168.611 35.3885 171 38.3077 171H165.692C168.612 171 171 168.611 171 165.692C171 139.472 156.298 116.596 134.695 104.919Z"
          fill="#d4d4d4"
        />
      </g>
      <defs>
        <clipPath id="clip0_187_488">
          <rect x={33} y={33} width={138} height={138} rx={69} fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export const OutlineExternalLink = () => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 4.00001L9.99997 14M20 4.00001L20 10M20 4.00001L14 4M10 4.00001H4V20H20V14"
        stroke="#696969"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const AddUserIcon = () => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 8V16M8 12L16 12M8 20H16C18.2091 20 20 18.2091 20 16V8C20 5.79086 18.2091 4 16 4H8C5.79086 4 4 5.79086 4 8V16C4 18.2091 5.79086 20 8 20Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const SaveIcon = () => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 20V15H9V20M20 9.82843V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6C4 4.89543 4.89543 4 6 4H14.1716C14.702 4 15.2107 4.21071 15.5858 4.58579L19.4142 8.41421C19.7893 8.78929 20 9.29799 20 9.82843Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
