import { UseInfiniteQueryResult } from '@tanstack/react-query'
import React, { ReactNode, useCallback, useEffect, useRef } from 'react'

export interface InfiniteScrollProps<T> {
  // React Query integration
  query: UseInfiniteQueryResult<any, Error>

  // Data rendering
  data?: T[]
  renderItem: (item: T, index: number) => ReactNode
  keyExtractor: (item: T, index: number) => string | number

  // Layout options
  direction?: 'vertical' | 'horizontal'
  cols?: number // Grid columns (1 = list, >1 = grid)
  gap?: string // Tailwind gap class

  // Loading states
  loadingComponent?: ReactNode
  skeletonComponent?: ReactNode
  skeletonCount?: number

  // Empty states
  emptyComponent?: ReactNode
  emptyTitle?: string
  emptyDescription?: string
  emptyIcon?: ReactNode

  // Error states
  errorComponent?: ReactNode
  errorTitle?: string
  errorDescription?: string
  errorIcon?: ReactNode

  // Infinite scroll behavior
  rootMargin?: string // Intersection observer margin
  disabled?: boolean

  // Styling
  className?: string
  containerClassName?: string
  itemClassName?: string
  loadingClassName?: string

  // Pull to refresh
  enablePullToRefresh?: boolean
  refreshThreshold?: number
  onRefresh?: () => void

  // Callbacks
  onEndReached?: () => void
  onScrollChange?: (
    scrollTop: number,
    scrollHeight: number,
    clientHeight: number
  ) => void
}

const InfiniteScroll = <T,>({
  query,
  data,
  renderItem,
  keyExtractor,

  direction = 'vertical',
  cols = 1,
  gap = 'gap-4',

  loadingComponent,
  skeletonComponent,
  skeletonCount = 5,

  emptyComponent,
  emptyTitle = 'No data found',
  emptyDescription = 'There are no items to display',
  emptyIcon,

  errorComponent,
  errorTitle = 'Something went wrong',
  errorDescription = 'Failed to load data. Please try again.',
  errorIcon,
  rootMargin = '100px',
  disabled = false,

  className = '',
  containerClassName = '',
  itemClassName = '',
  loadingClassName = '',

  enablePullToRefresh = false,
  refreshThreshold = 60,
  onRefresh,

  onEndReached,
  onScrollChange,
}: InfiniteScrollProps<T>) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const loadingRef = useRef<HTMLDivElement>(null)
  const pullToRefreshRef = useRef<HTMLDivElement>(null)

  // Pull to refresh state
  const [isPulling, setIsPulling] = React.useState(false)
  const [pullDistance, setPullDistance] = React.useState(0)
  const [touchStartY, setTouchStartY] = React.useState(0)

  // Flatten data from react-query pages
  const flatData = React.useMemo(() => {
    if (data) return data
    return (
      query.data?.pages?.flatMap(
        (page: any) => page.data || page.items || page.results || page
      ) || []
    )
  }, [data, query.data])

  // Handle infinite scroll trigger
  const handleLoadMore = useCallback(() => {
    if (!disabled && query.hasNextPage && !query.isFetchingNextPage) {
      query.fetchNextPage()
      onEndReached?.()
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [
    disabled,
    query.hasNextPage,
    query.isFetchingNextPage,
    query.fetchNextPage,
    onEndReached,
  ])

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const loadingElement = loadingRef.current
    if (!loadingElement) return

    const observer = new IntersectionObserver(
      (entries) => {
        const first = entries[0]
        if (first.isIntersecting) {
          handleLoadMore()
        }
      },
      {
        rootMargin,
        threshold: 0.1,
      }
    )

    observer.observe(loadingElement)
    return () => observer.disconnect()
  }, [handleLoadMore, rootMargin])

  // Scroll event handler
  useEffect(() => {
    const container = containerRef.current
    if (!container || !onScrollChange) return

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container
      onScrollChange(scrollTop, scrollHeight, clientHeight)
    }

    container.addEventListener('scroll', handleScroll)
    return () => container.removeEventListener('scroll', handleScroll)
  }, [onScrollChange])

  // Pull to refresh handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!enablePullToRefresh) return
    setTouchStartY(e.touches[0].clientY)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!enablePullToRefresh || !containerRef.current) return

    const touchY = e.touches[0].clientY
    const container = containerRef.current

    if (container.scrollTop === 0 && touchY > touchStartY) {
      const distance = Math.min(touchY - touchStartY, refreshThreshold * 2)
      setPullDistance(distance)
      setIsPulling(distance > refreshThreshold)
    }
  }

  const handleTouchEnd = () => {
    if (!enablePullToRefresh) return

    if (isPulling && onRefresh) {
      onRefresh()
      query.refetch()
    }

    setPullDistance(0)
    setIsPulling(false)
    setTouchStartY(0)
  }

  // Grid classes
  const getGridClasses = () => {
    if (cols === 1) return ''
    return `grid grid-cols-${Math.min(cols, 12)} ${gap}`
  }

  // Direction classes
  const getDirectionClasses = () => {
    return direction === 'horizontal' ? 'flex overflow-x-auto' : ''
  }

  // Default components
  const DefaultLoadingComponent = () => (
    <div
      className={`flex justify-center items-center py-4 ${loadingClassName}`}
    >
      <div className="flex items-center space-x-2">
        <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-gray-400">Loading more...</span>
      </div>
    </div>
  )

  const DefaultSkeletonComponent = () => (
    <div className={`animate-pulse ${itemClassName}`}>
      <div className="bg-gray-700 h-24 rounded-lg mb-3"></div>
      <div className="bg-gray-600 h-4 rounded mb-2"></div>
      <div className="bg-gray-600 h-4 rounded w-3/4"></div>
    </div>
  )

  const DefaultEmptyComponent = () => (
    <div className="flex flex-col items-center justify-center py-16 text-center">
      <div className="w-16 h-16 mb-4 text-gray-400">
        {emptyIcon || (
          <svg
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            className="w-full h-full"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
            />
          </svg>
        )}
      </div>
      <h3 className="text-lg font-semibold text-gray-300 mb-2">{emptyTitle}</h3>
      <p className="text-gray-400 max-w-md">{emptyDescription}</p>
    </div>
  )

  const DefaultErrorComponent = () => (
    <div className="flex flex-col items-center justify-center py-16 text-center">
      <div className="w-16 h-16 mb-4 text-red-400">
        {errorIcon || (
          <svg
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            className="w-full h-full"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        )}
      </div>
      <h3 className="text-lg font-semibold text-red-300 mb-2">{errorTitle}</h3>
      <p className="text-gray-400 max-w-md mb-4">{errorDescription}</p>
    </div>
  )

  // Pull to refresh indicator
  const PullToRefreshIndicator = () => {
    if (!enablePullToRefresh || pullDistance === 0) return null

    const progress = Math.min(pullDistance / refreshThreshold, 1)
    const rotation = progress * 180

    return (
      <div
        ref={pullToRefreshRef}
        className="absolute top-0 left-0 right-0 flex justify-center items-center bg-gray-900/90 backdrop-blur-sm z-10 transition-transform duration-200"
        style={{
          height: Math.min(pullDistance, refreshThreshold),
          transform: `translateY(-${Math.max(
            0,
            refreshThreshold - pullDistance
          )}px)`,
        }}
      >
        <div className="flex items-center space-x-2 text-blue-400">
          <svg
            className="w-5 h-5 transition-transform duration-200"
            style={{ transform: `rotate(${rotation}deg)` }}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          <span className="text-sm">
            {isPulling ? 'Release to refresh' : 'Pull to refresh'}
          </span>
        </div>
      </div>
    )
  }

  // Handle initial loading
  if (query.isLoading) {
    return (
      <div className={`w-full ${containerClassName}`}>
        <div className={`${getGridClasses()} ${gap}`}>
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <div key={index}>
              {skeletonComponent || <DefaultSkeletonComponent />}
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Handle error state
  if (query.isError) {
    return (
      <div className={containerClassName}>
        {errorComponent || <DefaultErrorComponent />}
      </div>
    )
  }

  // Handle empty state
  if (!query.isLoading && flatData.length === 0) {
    return (
      <div className={containerClassName}>
        {emptyComponent || <DefaultEmptyComponent />}
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={`w-full relative overflow-auto ${getDirectionClasses()} ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <PullToRefreshIndicator />

      <div
        className={`${containerClassName} ${getGridClasses()} ${
          !getGridClasses() ? gap : ''
        }`}
      >
        {flatData.map((item: T, index: number) => (
          <div key={keyExtractor(item, index)} className={itemClassName}>
            {renderItem(item, index)}
          </div>
        ))}
      </div>

      {/* Loading indicator for infinite scroll */}
      {query.hasNextPage && (
        <div ref={loadingRef}>
          {loadingComponent || <DefaultLoadingComponent />}
        </div>
      )}

      {/* End of list indicator */}
      {!query.hasNextPage && flatData.length > 0 && (
        <div className="text-center py-8 text-gray-500">
          <div className="inline-flex items-center">
            <div className="h-px bg-gray-700 flex-1"></div>
            <span className="px-4 text-sm">You&apos;ve reached the end</span>
            <div className="h-px bg-gray-700 flex-1"></div>
          </div>
        </div>
      )}
    </div>
  )
}

export default InfiniteScroll
