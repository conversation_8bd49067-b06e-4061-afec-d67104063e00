import { API_URLS } from '@/constants'
import {
  TNewsData,
  TNewsItem,
  TNewsPayload,
  TNewsApprovePayload,
} from '@/interfaces'
import { get, post, patch, del } from './baseApi'
import { ROLE } from '@/constants'
import { DEFAULT_CONFIG_PARAMS } from '@/constants'

export const getNews = async (params: {
  pageSize?: number
  pageIndex?: number
}): Promise<TNewsData> => {
  return await get(API_URLS.userNews, params)
}

export const getNewsForAdmin = async (
  params: {
    pageSize?: number
    pageIndex?: number
    searchWord?: string
  },
  role: ROLE.systemAdmin | ROLE.operator | ROLE.approver
): Promise<TNewsData> => {
  return await get(API_URLS.adminNews, params, role)
}

export const getListNewsForUser = async (params: {
  pageSize?: number
  pageIndex?: number
}): Promise<TNewsData> => {
  return await get(API_URLS.userNews, params, ROLE.user)
}

export const getNewsById = async (id): Promise<TNewsItem> => {
  return await get(`${API_URLS.news}/${id}`, DEFAULT_CONFIG_PARAMS)
}

export const createNews = async (
  params: TNewsPayload,
  role: ROLE.systemAdmin | ROLE.operator
) => {
  return await post(API_URLS.news, params, DEFAULT_CONFIG_PARAMS, role)
}

export const updateNews = async (
  id: string,
  params: TNewsPayload,
  role: ROLE.systemAdmin | ROLE.operator
) => {
  return await patch(`${API_URLS.news}/${id}`, params, role)
}

export const deleteNews = async (
  id: string,
  role: ROLE.systemAdmin | ROLE.operator
) => {
  return await del(`${API_URLS.news}/${id}`, {}, role)
}

export const approveNews = async (
  id: string,
  params: TNewsApprovePayload,
  role: ROLE.systemAdmin | ROLE.approver
) => {
  return await patch(`${API_URLS.news}/${id}/approval`, params, role)
}

export const getNewsDetail = async (newsId: string | undefined) => {
  return await get(`${API_URLS.userNew}/${newsId}`)
}
