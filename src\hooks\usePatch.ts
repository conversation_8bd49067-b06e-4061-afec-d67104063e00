import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-toastify'
import { textNotifications } from '@/constants'
import { ERR_MESSAGE } from '@/constants'

interface PatchProps<T = any> {
  queryKey: unknown[]
  callback: (arg: T) => Promise<any>
}

export const usePatch = <T = any>({ queryKey, callback }: PatchProps<T>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (arg: T) => callback(arg),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey })
      toast.success(textNotifications.CHANGE_SUCCESS)
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message
      toast.error(ERR_MESSAGE[errorMessage] || textNotifications.CHANGE_FAILED)
    },
  })
}
