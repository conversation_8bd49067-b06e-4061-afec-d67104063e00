import React, { useState, useEffect, useRef } from 'react'
import { Avatar, Button, Input, message, Typography, Form } from 'antd'
import { CameraOutlined, CloseOutlined } from '@ant-design/icons'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { toast } from 'react-toastify'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  updateUserProfile,
  getProfileStatistic,
} from '@/services/apiCall/users'
import { shortenString } from '@/utils'
import {
  IconCopy,
  IconOutlinedUser,
  IconPen,
  DefaultAvatar,
  IconDollar,
  IconLineChart,
  IconWallet,
} from '@/icons'
import { useTranslate } from '@/hooks'
import UiModal from '@/components/ui/Modal'
import { SocialMediaButtons } from '@/components/buttons'
import { STORAGEKEY } from '@/services/cookies'
import { useCookies } from 'react-cookie'
import { useRouter } from 'next/navigation'
import { formatPrice } from '@/utils'
const { Text } = Typography

interface MyProfileProps {
  walletAddress: string
  setIsUpdating?: any
  isUpdatingAva?: boolean
}

// URL validation function
const isValidUrl = (url: string): boolean => {
  if (!url) return true // Allow empty URLs
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

function MyProfile({
  walletAddress,
  setIsUpdating,
  isUpdatingAva,
}: MyProfileProps) {
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [form] = Form.useForm()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const editFormState = useState({
    name: '',
    biography: '',
    aboutMe: '',
    xUrl: '',
    facebookUrl: '',
    instagramUrl: '',
  })
  const setEditForm = editFormState[1]
  const router = useRouter()

  const queryClient = useQueryClient()
  const trans = useTranslate()

  const [cookies] = useCookies([STORAGEKEY.USER, STORAGEKEY.USER_ACCESS_TOKEN])
  const { otherWallet: evmWalletAddress } = cookies[STORAGEKEY.USER] || {}
  const isConnected = !!cookies[STORAGEKEY.USER_ACCESS_TOKEN]
  const [userProfile, setUserProfile] = useState<any>()
  const [profileLoading, setUserProfileLoading] = useState<boolean>(true)

  useEffect(() => {
    const handleUserProfile = () => {
      const profile = queryClient.getQueryData(['userProfile', walletAddress])
      setUserProfile(profile)
      setUserProfileLoading(false)
    }
    !!walletAddress && !userProfile && setTimeout(handleUserProfile, 250)
  }, [walletAddress])

  const {
    data: profileStatistic,
    isLoading: isLoadingStatistic,
    isError: isErrorStatistic,
  } = useQuery({
    queryKey: ['profile-statistic'],
    queryFn: getProfileStatistic,
    enabled: isConnected,
    refetchInterval: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  })

  // Update local state when userProfile data changes
  useEffect(() => {
    if (userProfile) {
      const formData = {
        name: userProfile.name || '',
        biography: userProfile.biography || '',
        aboutMe: userProfile.aboutMe || '',
        xUrl: userProfile.xUrl || '',
        facebookUrl: userProfile.facebookUrl || '',
        instagramUrl: userProfile.instagramUrl || '',
      }
      setEditForm(formData)
      form.setFieldsValue(formData)
    }
  }, [userProfile, form, setEditForm])

  // Mutation for updating user profile
  const updateProfileMutation = useMutation({
    mutationFn: (editInfo: {
      name: string
      biography: string
      aboutMe: string
      xUrl?: string
      facebookUrl?: string
      instagramUrl?: string
    }) => updateUserProfile(editInfo),
    onSuccess: () => {
      message.success(trans.profile_updated_successfully)
      queryClient.invalidateQueries({
        queryKey: ['userProfile', walletAddress],
      })
      setEditModalVisible(false)
      setTimeout(router.refresh, 1000)
    },
    onError: () => {
      message.error(trans.failed_to_update_profile)
    },
  })

  // Mutation for updating avatar
  const updateAvatarMutation = useMutation({
    mutationFn: (file: File) => updateUserProfile({ file }),
    onSuccess: () => {
      message.success(trans.profile_updated_successfully)
      queryClient.invalidateQueries({
        queryKey: ['userProfile', walletAddress],
      })
      setTimeout(router.refresh, 1000)
    },
    onError: () => {
      message.error(trans.failed_to_update_profile)
    },
  })

  // Mutation for removing avatar
  const removeAvatarMutation = useMutation({
    mutationFn: () => updateUserProfile({ removeAvatar: true }),
    onSuccess: () => {
      message.success(trans.profile_updated_successfully)
      queryClient.invalidateQueries({
        queryKey: ['userProfile', walletAddress],
      })
      setTimeout(router.refresh, 1000)
    },
    onError: () => {
      message.error(trans.failed_to_update_profile)
    },
  })

  const handleCopyAddress = () => {
    toast.success(trans.wallet_address_copied)
  }

  const handleEditProfile = () => {
    setEditModalVisible(true)
  }

  const handleSaveProfile = async () => {
    try {
      const values = await form.validateFields()
      updateProfileMutation.mutate(values)
    } catch (error) {
      // Form validation failed
      console.error('Validation failed:', error)
    }
  }

  const handleAvatarClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type - only allow specific image formats
      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
      ]
      if (!allowedTypes.includes(file.type)) {
        message.error(trans.validation_avatar_file_type)
        return
      }

      // Validate file size (max 50MB)
      if (file.size > 50 * 1024 * 1024) {
        message.error(trans.validation_avatar_max_size)
        return
      }
      setIsUpdating?.(true)
      updateAvatarMutation.mutate(file)
    }
  }

  const handleDeleteAvatar = () => {
    removeAvatarMutation.mutate()
  }

  if (profileLoading || isLoadingStatistic) {
    return (
      <div className="text-center mb-12 space-y-4">
        <div className="text-white">{trans.loading_profile}</div>
      </div>
    )
  }

  if (isErrorStatistic) {
    return (
      <div className="text-center mb-12 space-y-4">
        <div className="text-white">{trans.failed_to_load_profile}</div>
      </div>
    )
  }

  return (
    <>
      {/* User Profile Section */}
      <div className="text-center mb-12 space-y-4">
        {/* Profile Picture */}
        <div className="relative inline-block">
          {userProfile?.avatar && !isUpdatingAva ? (
            <Avatar size={158} src={userProfile.avatar} className="border-4">
              {userProfile?.name?.charAt(0) || 'U'}
            </Avatar>
          ) : (
            <div className="w-[158px] h-[158px] rounded-full border-4 border-neutral-300 overflow-hidden">
              <DefaultAvatar width="158" height="158" />
            </div>
          )}
          <div className="absolute bottom-0 right-0 flex gap-2">
            <Button
              shape="circle"
              icon={<CameraOutlined rev="icon" className="text-black" />}
              size="middle"
              className="bg-tailwindBrand1 bg-primary hover:bg-primaryHover border-0 transition-all duration-300 !shadow-none h-8"
              onClick={handleAvatarClick}
              loading={updateAvatarMutation.isPending}
            />
            {userProfile?.avatar && (
              <Button
                shape="circle"
                icon={<CloseOutlined rev="icon" className="text-black" />}
                size="middle"
                className="bg-red-500 hover:bg-red-600 border-0 transition-all duration-300 !shadow-none"
                onClick={handleDeleteAvatar}
                loading={removeAvatarMutation.isPending}
              />
            )}
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg,image/png,image/gif,image/webp"
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />
        </div>

        {/* Name */}
        <div className="flex items-center justify-center space-x-2">
          <p className="font-montserrat font-semibold text-[24px] leading-[120%] text-right tracking-[0.02em] text-white">
            {userProfile?.name || 'User name'}
          </p>
          <button
            className="text-primary hover:text-primaryHover"
            onClick={handleEditProfile}
          >
            <IconPen />
          </button>
        </div>

        {/* Biography */}
        <p className="font-montserrat font-normal text-[14px] leading-[170%] text-center tracking-[0.02em] text-white">
          {userProfile?.biography || trans.write_something_about_yourself}
        </p>

        {/* Social Links */}
        <SocialMediaButtons user={userProfile} />
      </div>

      {/* User Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="relative bg-getCardBg items-center content-center text-center rounded-md py-3 h-[200px]">
          <span className="absolute top-4 left-6 font-montserrat font-semibold text-[14px] leading-[140%] tracking-[0.02em] text-white gap-1">
            <IconDollar /> {trans.nfts_owned}
          </span>
          <p className="font-montserrat font-normal text-[40px] leading-[140%] flex justify-center text-center tracking-[0.02em] text-white">
            {profileStatistic?.totalMintCount}
          </p>
        </div>
        <div className="relative bg-getCardBg items-center content-center text-center rounded-md py-3 h-[200px]">
          <span className="absolute top-4 left-6 font-montserrat font-semibold text-[14px] leading-[140%] tracking-[0.02em] text-white gap-1">
            <IconLineChart />
            {trans.total_value}
          </span>
          <p className="font-montserrat font-normal text-[40px] leading-[140%] flex justify-center text-center tracking-[0.02em] text-white">
            {`${formatPrice(profileStatistic?.totalPrice as number, 4)}`}
            <span className="text-[14px] ml-3">GET PAY</span>
          </p>
        </div>

        <div className="relative bg-getCardBg items-center content-center text-center rounded-md py-3 h-[200px]">
          <span className="absolute top-4 left-6 font-montserrat font-semibold text-[14px] leading-[140%] tracking-[0.02em] text-white gap-1">
            <IconWallet /> {trans.wallet_address}
          </span>
          <div className="flex items-center justify-center space-x-1">
            <p className="font-normal text-[20px] leading-[140%] flex justify-center items-center text-center tracking-[0.02em] text-white">
              {evmWalletAddress
                ? shortenString(evmWalletAddress, 8, 8)
                : '012345678...76543210'}
            </p>
            <CopyToClipboard
              text={evmWalletAddress || ''}
              onCopy={handleCopyAddress}
            >
              <button className="text-primary hover:text-primaryHover">
                <IconCopy />
              </button>
            </CopyToClipboard>
          </div>
        </div>
      </div>

      {/* About Me Section */}
      <div className="mb-2 bg-getCardBg rounded-[8px] p-[20px] min-h-[172px]">
        <div className="flex items-center mb-4 space-x-1">
          <IconOutlinedUser className="text-white" />
          <p className="font-montserrat font-semibold text-[14px] leading-[140%] flex items-center tracking-[0.02em] text-white">
            {trans.about_me}
          </p>
          <button
            onClick={handleEditProfile}
            className="text-primary hover:text-primaryHover pl-1"
          >
            <IconPen />
          </button>
        </div>
        <div className="flex flex-1">
          <p className="font-montserrat font-medium text-[14px] tracking-[0.02em] text-white">
            {userProfile?.aboutMe ||
              `Passionate about the intersection of art and technology, I focus on emerging artists and generative art. My diverse collection reflects everything from abstract expressionism to cyberpunk aesthetics. I believe in the power of digital ownership and the democratization of art through blockchain technology.`}
          </p>
        </div>
      </div>

      {/* Edit Profile Modal */}
      <UiModal
        isOpen={editModalVisible}
        header={trans.edit_profile}
        onClose={() => setEditModalVisible(false)}
        cancelButton={{
          isShow: true,
          title: trans.cancel,
          action: () => setEditModalVisible(false),
        }}
        confirmButton={{
          isShow: true,
          title: trans.save,
          action: handleSaveProfile,
        }}
      >
        <Form form={form} layout="vertical" className="w-full">
          <Form.Item
            name="name"
            label={<Text className="text-white">{trans.name}</Text>}
            rules={[
              { required: true, message: trans.validation_name_required },
              { max: 50, message: trans.validation_name_max },
            ]}
          >
            <Input
              placeholder={trans.enter_your_name}
              className="bg-getCardBg text-white"
            />
          </Form.Item>

          <Form.Item
            name="biography"
            label={<Text className="text-white">{trans.biography}</Text>}
            rules={[{ max: 250, message: trans.validation_biography_max }]}
          >
            <Input.TextArea
              placeholder={trans.enter_your_biography}
              rows={3}
              className="bg-getCardBg text-white"
            />
          </Form.Item>

          <Form.Item
            name="aboutMe"
            label={<Text className="text-white">{trans.about_me}</Text>}
            rules={[{ max: 500, message: trans.validation_about_me_max }]}
          >
            <Input.TextArea
              placeholder={trans.tell_us_about_yourself}
              rows={4}
              className="bg-getCardBg text-white"
            />
          </Form.Item>

          <Form.Item
            name="xUrl"
            label={<Text className="text-white">{trans.x_url}</Text>}
            rules={[
              {
                validator: (_, value) => {
                  if (value && !isValidUrl(value)) {
                    return Promise.reject(
                      new Error(trans.validation_invalid_url)
                    )
                  }
                  return Promise.resolve()
                },
              },
            ]}
          >
            <Input
              placeholder={trans.enter_x_url}
              className="bg-getCardBg text-white"
            />
          </Form.Item>

          <Form.Item
            name="facebookUrl"
            label={<Text className="text-white">{trans.facebook_url}</Text>}
            rules={[
              {
                validator: (_, value) => {
                  if (value && !isValidUrl(value)) {
                    return Promise.reject(
                      new Error(trans.validation_invalid_url)
                    )
                  }
                  return Promise.resolve()
                },
              },
            ]}
          >
            <Input
              placeholder={trans.enter_facebook_url}
              className="bg-getCardBg text-white"
            />
          </Form.Item>

          <Form.Item
            name="instagramUrl"
            label={<Text className="text-white">{trans.instagram_url}</Text>}
            rules={[
              {
                validator: (_, value) => {
                  if (value && !isValidUrl(value)) {
                    return Promise.reject(
                      new Error(trans.validation_invalid_url)
                    )
                  }
                  return Promise.resolve()
                },
              },
            ]}
          >
            <Input
              placeholder={trans.enter_instagram_url}
              className="bg-getCardBg text-white"
            />
          </Form.Item>
        </Form>
      </UiModal>
    </>
  )
}

export default React.memo(MyProfile)
