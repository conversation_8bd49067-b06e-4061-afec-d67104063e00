import React from 'react'

interface GradientSpinnerProps {
  className?: string
  size?: string
  strokeColorFrom?: string
  strokeColorTo?: string
}
const UiSpinner = ({
  className = '',
  size = 'h-5 w-5',
  strokeColorFrom = '#F2AF36',
  strokeColorTo = '#FAE475',
}: GradientSpinnerProps) => {
  return (
    <svg
      className={`animate-spin ${size} ${className}`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <defs>
        <linearGradient id="spinnerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0%"
            style={{ stopColor: strokeColorFrom, stopOpacity: 1 }}
          />
          <stop
            offset="100%"
            style={{ stopColor: strokeColorTo, stopOpacity: 1 }}
          />
        </linearGradient>
      </defs>
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="url(#spinnerGradient)"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="url(#spinnerGradient)"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )
}

export default React.memo(UiSpinner)
