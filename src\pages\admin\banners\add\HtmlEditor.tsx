import { Editor } from '@tinymce/tinymce-react'
import { useController } from 'react-hook-form'
import { LegacyRef, useRef, useState } from 'react'
import { Editor as TinyMCEEditor } from 'tinymce'

import { config } from '@/utils/tinyMCE'
import { uploadBannerFile, uploadImageEditor } from '@/services/apiCall/banner'
import { UseFormClearErrors, UseFormSetError } from 'react-hook-form/dist/types'
import { MAX_LENGTH_INPUT } from '@/constants'
import ModalUploadFile from '@/components/modal/ModalUploadFile'
// import { MESSAGE_FORM } from '@/utils/string'

type TBanner = {
  description?: string
  imageId: string
  collection: string
  externalLink: string
}

type HtmlEditorProps = {
  name: 'description'
  control?: any
  setError: UseFormSetError<TBanner>
  clearErrors: UseFormClearErrors<TBanner>
  className?: string
  disabled?: boolean
}
export function HtmlEditorComponent({
  name,
  control,
  setError,
  clearErrors,
  className,
  disabled = false,
}: HtmlEditorProps) {
  const editorRef = useRef<TinyMCEEditor | null>(null)
  const {
    field: { onChange, ...field },
  } = useController({ control, name })
  const [isModalUpload, setOpenModalUpload] = useState(false)
  const handleChangeText = (value: string, editor: TinyMCEEditor) => {
    const content = editor.getContent({ format: 'text' })
    const checkMaxLength = content.length > MAX_LENGTH_INPUT.introduction
    checkMaxLength && setError('description', { message: ``, type: 'notShow' })

    !checkMaxLength &&
      content.length < MAX_LENGTH_INPUT.introduction &&
      clearErrors('description')
    !checkMaxLength && onChange(value)
  }

  return (
    <>
      <div className={className}>
        <Editor
          disabled={disabled}
          apiKey={process.env.NEXT_PUBLIC_NODE_TINYMCE}
          {...field}
          value={field.value}
          onInit={(_, editor) => {
            editorRef.current = editor
            return
          }}
          ref={editorRef as LegacyRef<Editor>}
          onEditorChange={handleChangeText}
          init={{
            ...config,
            readonly: true,
            images_upload_handler: uploadImageEditor,
            placeholder: 'Introduction',
            images_file_types: 'jpg,webp,png,gif,jpeg',
            toolbar_mode: 'sliding',
            setup: function (editor) {
              editor.ui.registry.addButton('uploadButton', {
                icon: 'upload',
                tooltip: 'ファイルの挿入/編集',
                onAction: function () {
                  setOpenModalUpload(true)
                },
              })
            },
          }}
        />
      </div>

      <ModalUploadFile
        isModalOpen={isModalUpload}
        setModalOpen={setOpenModalUpload}
        editorRef={editorRef}
        funcUpload={uploadBannerFile}
      />
    </>
  )
}
