import { ROUTES, R<PERSON><PERSON>, adminUrlRoles } from '@/constants'
import { RoleType } from '@/interfaces'
import { STORAGEKEY } from '@/services/cookies'
import { deleteCookie } from './handleCookie'

export const logout = (role: string) => {
  if (role === ROLE.systemAdmin) {
    deleteCookie([STORAGEKEY.ADMIN_ACCESS_TOKEN, STORAGEKEY.ADMIN])
  } else {
    deleteCookie([
      STORAGEKEY.WALLET_ADDRESS,
      STORAGEKEY.USER,
      STORAGEKEY.USER_ACCESS_TOKEN,
    ])
  }
}

export const parseJwt = (token: string) => {
  try {
    return JSON.parse(atob(token.split('.')[1]))
  } catch (e) {
    return null
  }
}

export const getRoleParsed = (token?: string | null) => {
  try {
    return token ? parseJwt(token)?.role || null : null
  } catch (error) {
    return null
  }
}
export const getAdminRole = (url: string, role: RoleType): boolean => {
  let result: string[] = []
  for (const key in ROUTES) {
    if (ROUTES[key] === url) {
      result = adminUrlRoles[key]
      break
    }
  }
  return result.includes(role)
}
interface CheckValidUrlResult {
  isValid: boolean
  redirectUrl: string
}
interface CheckValidUrlProps {
  url: string
  role: RoleType | null
  roleAdmin: RoleType | null
}

export const checkValidUrlWithRole = ({
  url,
  role,
  roleAdmin,
}: CheckValidUrlProps): CheckValidUrlResult => {
  let isValid = true
  let redirectUrl = '/'

  const isLoginPage = url.startsWith(ROUTES.adminLogin)
  const isAdminPage = !isLoginPage && url.startsWith(ROUTES.admin)

  if (isLoginPage) {
    isValid = !roleAdmin
    redirectUrl = ROUTES.adminDashboard
  } else {
    isValid =
      (!role && url === ROUTES.myProfile) || (!roleAdmin && isAdminPage)
        ? false
        : true
    redirectUrl = isAdminPage ? ROUTES.adminLogin : ROUTES.home
  }

  return {
    isValid,
    redirectUrl,
  }
}
