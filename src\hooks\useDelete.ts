import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-toastify'
import { textNotifications } from '@/constants'

interface DeleteProps {
  queryKey: unknown[]
  callback: (ids: string | string[]) => Promise<any>
}

export const useDelete = ({ queryKey, callback }: DeleteProps) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string | string[]) => callback(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey })
      toast.success(textNotifications.DELETE_SUCCESS)
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message || textNotifications.DELETE_FAILED
      toast.error(errorMessage)
    },
  })
}
