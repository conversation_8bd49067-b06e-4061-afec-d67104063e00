## Requirement

- Use `yarn` instead `npm`

- `node` >=18.14.0

- `yarn` >=1.22.0

- Knowledge about nextjs
- Knowledge about state management (we use Zustand)
  [Zustand Documentation](https://github.com/pmndrs/zustand)
- Knowledge about css framework (we use tailwind)
  [Tailwind Documentation](https://tailwindcss.com/)
  [Tailwind css classess](https://tailwind.build/classes)
- Format code with default settings ( prettier, eslint )

- Commit message format:

  ```
  <type>: <subject>
  ```

  **Example:**

  ```
  feat: get transaction list data
  ```

  **Type enums**:

  ```
  feat: A new feature.
  fix: A bug fix.
  style: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc).
  refactor: A code change that neither fixes a bug nor adds a feature.
  perf: A code change that improves performance.
  docs: Documentation only changes.
  test: Adding missing tests.
  config: Config
  revert: revert code
  ```

## Getting started

- First, install dependencies:

  ```
  yarn
  ```

- Run the development server:

  ```
  yarn dev
  ```

- Run the production server:

  ```
  yarn build
  ```
