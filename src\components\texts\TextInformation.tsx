import clsx from 'clsx'

import React from 'react'
import Text from './Text'

function TextInformation({
  title,
  require = false,
  className = '',
}: {
  title: string
  require?: boolean
  className?: string
}) {
  return (
    <div className={clsx('flex  gap-2 mb-4', className)}>
      <Text require={require}>
        <span className="text-white text-xs font-montserrat font-semibold">
          {title}
        </span>
      </Text>
    </div>
  )
}

export default TextInformation
