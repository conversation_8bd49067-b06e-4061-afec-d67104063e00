import ButtonComponent from '../../components/buttons/Button'
import React from 'react'
import Image from 'next/image'
import { Form, Input } from 'antd'

import { BUTTON, IMAGE, MESSAGE_FORM } from '@/utils/string'
import { useRouter } from 'next/router'
import { get } from '@/services/apiCall/baseApi'
import { API_URLS, pageHeadConfigDefault, ROLE, ROUTES } from '@/constants'
import { toast } from 'react-toastify'
import Head from 'next/head'
import { useCookies } from 'react-cookie'
import { STORAGEKEY } from '@/services/cookies'

const textErrStatus = {
  404: MESSAGE_FORM.wrongInputCode,
  400: MESSAGE_FORM.giftCodeUsed,
}

const ClaimNft = () => {
  const { push } = useRouter()
  const [cookies] = useCookies([STORAGEKEY.USER_ACCESS_TOKEN])
  const isConnected = cookies[STORAGEKEY.USER_ACCESS_TOKEN]
  const handleClaimNft = async ({ giftCode }: { giftCode: string }) => {
    try {
      const response = await get(
        `${API_URLS.giftCode}/${giftCode.trim()}`,
        {},
        ROLE.user
      )
      if (response.metadata) {
        push(`/nfts/${response.metadata._id}?gift=${giftCode.trim()}`)
      }
    } catch (err) {
      toast.error(textErrStatus[err.response.status])
    }
  }

  return (
    <>
      <Head>
        <title>title</title>
        <meta name="description" content={pageHeadConfigDefault.description} />
        <meta name="keywords" content={pageHeadConfigDefault.keywords} />
        <meta property="og:title" content="title" />
        <meta
          property="og:description"
          content={pageHeadConfigDefault.description}
        />
      </Head>
      <Form
        className="flex flex-col items-center justify-center 2xl:h-[80vh] h-[79vh]"
        onFinish={handleClaimNft}
      >
        <Image src={IMAGE.giftBox} alt="gift box" width={104} height={110} />
        <div className="my-12 flex flex-col items-center">
          <h1 className="text-center text-tailwindNeutral1 text-2xl font-medium font-sans 2xl:w-[250px] w-[240px] mb-5">
            NFTを受け取るコードを入力してください。
          </h1>
          {!isConnected && (
            <>
              <p className="text-sm text-tailwindFailed mb-2">
                ※NFTを受け取るには、ウォレットを接続する必要があります。
              </p>
              <span className="text-sm mb-2">
                ウォレットを接続するには{' '}
                <a
                  className="underline text-tailwindFailed hover:text-tailwindBrand1"
                  href={ROUTES.faqs}
                >
                  ここ
                </a>{' '}
                をご参照ください。
              </span>
            </>
          )}
          <Form.Item
            name={'giftCode'}
            rules={[
              { required: true, message: MESSAGE_FORM.giftCode },
              { whitespace: true, message: MESSAGE_FORM.whiteSpace },
            ]}
          >
            <Input
              className="w-[40vh]"
              placeholder="コードを入力してください。"
            />
          </Form.Item>
        </div>
        <ButtonComponent
          title={BUTTON.claimNft}
          type="primary"
          typeSubmit="submit"
        />
      </Form>
    </>
  )
}

export default ClaimNft
