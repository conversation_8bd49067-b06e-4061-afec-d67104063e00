import { ETYPE_TIME_OPTION } from '@/constants'
import { useState } from 'react'

export const useTimeSelectBar = (
  defaultTimeOption: ETYPE_TIME_OPTION = ETYPE_TIME_OPTION.lastWeek
) => {
  const [timeOption, setTimeOption] = useState(defaultTimeOption)
  const buttons: {
    id: ETYPE_TIME_OPTION
    label: string
    isActive: boolean
  }[] = [
    [ETYPE_TIME_OPTION.yesterday, '1日'],
    [ETYPE_TIME_OPTION.lastWeek, '7日'],
    [ETYPE_TIME_OPTION.threeMonths, '3ヶ月'],
    [ETYPE_TIME_OPTION.sixMonths, '6ヶ月'],
  ].map(([id, label]) => ({
    id: id as ETYPE_TIME_OPTION,
    label,
    isActive: id === timeOption,
  }))

  return {
    timeOption,
    TimeSelectBar: ({ className }: { className?: string }) => (
      <div className={`flex mb-4 ${className || ''}`}>
        <div className="flex gap-2 p-1 bg-[#1c1c1c] w-fit rounded-lg">
          {buttons.map((button) => (
            <button
              key={button.id}
              className={`${
                button.isActive ? 'bg-[#3e3e3e]' : 'hover:bg-neutral-950'
              } w-1/4 text-white py-2 px-6 transition-colors duration-300 text-xs text-nowrap rounded-[4px]`}
              onClick={() => setTimeOption(button.id as ETYPE_TIME_OPTION)}
            >
              {button.label}
            </button>
          ))}
        </div>
      </div>
    ),
  }
}
