import { StaticPageCard } from '@/components/card'
import { StaticPageLayout } from '@/components/layouts/staticPageLayout'
import { getCurrentLanguage } from '@/utils'
import { IMAGE } from '@/utils/string'

const GetWalletOfficalSite =
  process.env.NEXT_PUBLIC_GET_WALLET_OFFICIAL_SITE || '#'

const EnglishContent = () => (
  <div className="static-page">
    <p>
      GET Wallet is the only officially supported wallet that seamlessly
      integrates with Glitters.
    </p>
    <p>
      All transactions on Glitters and every Web3 entertainment experience
      starts with this wallet.
    </p>
    <a href={GetWalletOfficalSite} target="_blank" rel="noreferrer">
      ➡ Learn more: Visit the GET Wallet official site
    </a>

    <h3>Why Do You Need GET Wallet?</h3>
    <ul>
      <li>
        <strong>The Only Wallet Compatible with GET Platform</strong>
        <br />
        GET Wallet is the only wallet that can connect directly to Glitters and
        the GET Platform. Other wallets are not supported.
      </li>
      <li>
        <strong>Essential for Using GET PAY</strong>
        <br />
        To purchase or support entertainment content with GET PAY, you must have
        GET Wallet.
      </li>
      <li>
        <strong>Beginner-Friendly Design</strong>
        <br />
        Creating or restoring your wallet takes just a few clicks. Even those
        unfamiliar with blockchain can start using it with ease.
      </li>
    </ul>

    <h3>What You Can Do with GET Wallet</h3>
    <ul>
      <li>
        <strong>Manage Your NFT Collection in One Place</strong>
        <br />
        Store and view NFTs from both Cardano and Ethereum chains in one
        intuitive interface. Browse your favorite works at a glance.
      </li>
      <li>
        <strong>Join Fan Activities</strong>
        <br />
        Use GET tokens and GET PAY to purchase NFTs, vote, and participate in
        exclusive events—unlocking new ways to support your favorite creators.
      </li>
      <li>
        <strong>A Hub for NFT Creators</strong>
        <br />
        Easily manage your own NFTs and deliver your creations directly to fans
        around the world.
      </li>
      <li>
        <strong>Participate in Web3 Events and Airdrops</strong>
        <br />
        No account registration required. Instantly activate your wallet to
        receive tokens and join campaigns smoothly.
      </li>
    </ul>

    <h3>Key Features</h3>
    <h4>Security</h4>
    <ul>
      <li>Passed third-party vulnerability assessments</li>
      <li>Local storage only—no personal data is sent to external servers</li>
      <li>Self-custody design (non-custodial) ensures full user control</li>
    </ul>
    <h4>Usability</h4>
    <ul>
      <li>Instantly accessible via Chrome extension</li>
      <li>Wallet creation and recovery completed in just a few clicks</li>
      <li>
        Easy-to-use interface for sending/receiving tokens and NFTs, and viewing
        transaction history
      </li>
    </ul>
    <h4>Global Compatibility</h4>
    <ul>
      <li>Multilingual interface: supports both English and Japanese</li>
      <li>Compatible with international blockchain projects and events</li>
      <li>Free language switching for users around the world</li>
    </ul>

    <h3>Step Into the Next Stage with GET Wallet</h3>
    <p>
      Take the first step into a new entertainment experience powered by GET
      tokens and NFTs.
    </p>
    <p>
      GET Wallet is your gateway—whether you’re a fan, a creator, or an
      investor.
    </p>
    <a href={GetWalletOfficalSite} target="_blank" rel="noreferrer">
      ➡ Access now: Visit the GET Wallet official site
    </a>
  </div>
)

const JapaneseContent = () => (
  <div className="static-page">
    <p>
      GET
      Wallet（ゲット・ウォレット）は、Glittersとシームレスに連携する唯一の公式対応ウォレットです。
    </p>
    <p>
      Glittersでの取引、Web3エンタメ体験のすべては、このウォレットから始まります。
    </p>
    <a href={GetWalletOfficalSite} target="_blank" rel="noreferrer">
      ➡ 詳細を見る：GET Wallet公式サイトへ
    </a>

    <h3>なぜGET Walletが必要なのか？</h3>
    <ul>
      <li>
        <strong>GET Platformに唯一対応した公式ウォレット</strong>
        <br />
        GlittersやGET Platformと直接接続できるのは、GET
        Walletだけ。他のウォレットでは利用できません。
      </li>
      <li>
        <strong>GET PAYの利用に不可欠</strong>
        <br />
        エンタメコンテンツをGET
        PAYで購入・支援するには、このウォレットが必須です。
      </li>
      <li>
        <strong>初心者でも安心の設計</strong>
        <br />
        ウォレットの作成・復元は数クリックで完了。ブロックチェーンに不慣れな方でも、すぐに利用を始められます。
      </li>
    </ul>

    <h3>GET Walletでできること</h3>
    <ul>
      <li>
        <strong>NFTコレクションを一元管理</strong>
        <br />
        CardanoとEthereum両方のNFTをまとめて保管・表示可能。お気に入りの作品を一覧できる直感的なUIを実現。
      </li>
      <li>
        <strong>ファン活動に参加</strong>
        <br />
        GETトークンやGET
        PAYを使って、NFT購入、投票、限定イベントなど、ファンとしての新しい関わり方が広がります。
      </li>
      <li>
        <strong>クリエイターのNFT発信拠点</strong>
        <br />
        自分のNFTを管理し、世界中のファンに直接届けることが可能です。
      </li>
      <li>
        <strong>Web3イベントやエアドロップへの対応</strong>
        <br />
        アカウント登録不要ですぐにウォレットを起動でき、エアドロップやキャンペーン参加もスムーズ。
      </li>
    </ul>

    <h3>主な機能と特長</h3>
    <h4>セキュリティ</h4>
    <ul>
      <li>第三者機関による脆弱性診断をクリア</li>
      <li>ローカル保存型で外部サーバーに個人情報を送信しない</li>
      <li>自己管理型ウォレット（非カストディアル）で完全なユーザー主導権</li>
    </ul>
    <h4>操作性</h4>
    <ul>
      <li>Chrome拡張型で即利用可能</li>
      <li>ウォレットの作成・復元が数クリックで完結</li>
      <li>トークン／NFTの送受信や取引履歴の確認も簡単操作</li>
    </ul>
    <h4>グローバル対応</h4>
    <ul>
      <li>英語・日本語の多言語UIを搭載</li>
      <li>国際プロジェクト・イベントにも対応</li>
      <li>インターフェースの言語は自由に切り替え可能</li>
    </ul>

    <h3>さあ、あなたもGET Walletで次のステージへ</h3>
    <p>GETトークンとNFTを活用した、新しいエンタメ体験の扉を開く第一歩。</p>
    <p>GET Walletは、ファン・クリエイター・投資家すべての起点です。</p>
    <a href={GetWalletOfficalSite} target="_blank" rel="noreferrer">
      ➡ 今すぐアクセス：GET Wallet公式サイトを見る
    </a>
  </div>
)

function WhatIsAGetWallet() {
  const lang = getCurrentLanguage()
  const title = lang === 'en' ? 'What is GET Wallet?' : 'GET Walletとは？'

  return (
    <StaticPageLayout title={title}>
      <StaticPageCard
        title={title}
        imageUrl={IMAGE.whatIsAGetWallet}
        createdAt="2025-08-01"
      >
        {lang === 'en' ? <EnglishContent /> : <JapaneseContent />}
      </StaticPageCard>
    </StaticPageLayout>
  )
}

export default WhatIsAGetWallet
