import React, { useState, useEffect } from 'react'
import { useGet } from '@/hooks/useGet'
import { getCollectionsForUser, getListCategories } from '@/services/apiCall'
import { Collection } from '@/interfaces'
import { HOMEPAGE_CONFIG } from '@/constants/homepage'
import { COLLECTIONS_CONFIG } from '@/constants/collections'
import SearchFilterBar from '@/pages/home/<USER>/SearchFilterBar'
import CollectionGrid from '@/pages/home/<USER>/CollectionGrid'
import Breadcrumbs from '@/components/breadcrumbs/Breadcrumbs'
import { useTranslate } from '@/hooks/useTranslate'
import { UiPagination } from '@/components/ui/Pagination'

export default function CollectionsPage() {
  const t = useTranslate()

  // State for search and filters
  const [searchValue, setSearchValue] = useState('')
  const [categoryValue, setCategoryValue] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [collections, setCollections] = useState<Collection[]>([])

  // State for actual search parameters (used in API calls)
  const [searchParams, setSearchParams] = useState({
    searchWord: '',
    category: '',
  })

  // Fetch categories for filter dropdown
  const { data: categoryData, isLoading: isLoadingCategories } = useGet({
    queryKey: ['categories'],
    callback: getListCategories,
  })

  // Fetch collections with search and filter
  const { data: collectionData, isLoading: isLoadingCollections } = useGet({
    queryKey: [
      'collections',
      'user',
      searchParams.searchWord,
      searchParams.category,
      currentPage,
    ],
    callback: () =>
      getCollectionsForUser({
        pageIndex: currentPage,
        pageSize: HOMEPAGE_CONFIG.defaultPageSize,
        searchWord: searchParams.searchWord || undefined,
        category: searchParams.category || undefined,
      }),
  })

  // Update collections when data changes
  useEffect(() => {
    if (collectionData) {
      setCollections(collectionData.items || [])
    }
  }, [collectionData])

  // Handle search button click
  const handleSearch = () => {
    setSearchParams({
      searchWord: searchValue,
      category: categoryValue,
    })
    setCurrentPage(1)
  }

  // Handle pagination change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Calculate total pages
  const totalPages = collectionData
    ? Math.ceil(collectionData.totalItems / collectionData.pageSize)
    : 0

  return (
    <div className="min-h-screen">
      {/* Main Content Section */}
      <Breadcrumbs />

      {/* Search and Filter Bar */}
      <div className="sm:inline-flex justify-start mt-8">
        <SearchFilterBar
          searchValue={searchValue}
          categoryValue={categoryValue}
          categories={categoryData?.items || []}
          onSearchChange={setSearchValue}
          onCategoryChange={setCategoryValue}
          onSearch={handleSearch}
          isLoading={isLoadingCategories}
          searchPlaceholder={t[COLLECTIONS_CONFIG.searchPlaceholder]}
          categoryPlaceholder={t[COLLECTIONS_CONFIG.categoryAll]}
          searchButtonText={t[COLLECTIONS_CONFIG.searchButton]}
        />
      </div>

      <h1 className="font-montserrat font-medium text-[36px] leading-[120%] tracking-[0.02em] text-white text-center mb-8">
        {t[COLLECTIONS_CONFIG.pageTitle]}
      </h1>

      {/* Collection Grid - Full Width */}
      <div className="w-full">
        <CollectionGrid
          collections={collections}
          isLoading={isLoadingCollections}
          hasMore={false} // Disable load more since we're using pagination
          categories={categoryData?.items || []}
          noResultsText={t[COLLECTIONS_CONFIG.noCollections]}
          noResultsSubtext={t[COLLECTIONS_CONFIG.noCollectionsSubtext]}
        />
      </div>

      {/* Pagination */}
      {!isLoadingCollections && totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <UiPagination
            current={currentPage}
            total={collectionData?.totalItems || 0}
            pageSize={HOMEPAGE_CONFIG.defaultPageSize}
            onChange={handlePageChange}
            showSizeChanger={false}
            showQuickJumper
            className="pagination-custom"
            locale={{
              items_per_page: '',
              jump_to: t[COLLECTIONS_CONFIG.paginationJumpTo],
              jump_to_confirm: t[COLLECTIONS_CONFIG.paginationGoTo],
              page: t[COLLECTIONS_CONFIG.paginationPage],
            }}
          />
        </div>
      )}
    </div>
  )
}
