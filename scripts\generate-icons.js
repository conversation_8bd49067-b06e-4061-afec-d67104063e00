const fs = require('fs')
const path = require('path')

// Icon sizes needed for PWA
const ICON_SIZES = [
  { size: 72, name: 'icon-72x72.png' },
  { size: 96, name: 'icon-96x96.png' },
  { size: 128, name: 'icon-128x128.png' },
  { size: 144, name: 'icon-144x144.png' },
  { size: 152, name: 'icon-152x152.png' },
  { size: 192, name: 'icon-192x192.png' },
  { size: 384, name: 'icon-384x384.png' },
  { size: 512, name: 'icon-512x512.png' },
]

// Additional icons for shortcuts
const SHORTCUT_ICONS = [
  { name: 'icon-collections.png', description: 'Collections icon' },
  { name: 'icon-news.png', description: 'News icon' },
  { name: 'icon-profile.png', description: 'Profile icon' },
]

function generateIconsDirectory() {
  const iconsDir = path.join(process.cwd(), 'public', 'icons')
  
  // Create icons directory if it doesn't exist
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true })
    console.log('✅ Created icons directory')
  }
  
  // Check existing icons
  console.log('\n📋 Required PWA Icons:')
  ICON_SIZES.forEach(({ size, name }) => {
    const iconPath = path.join(iconsDir, name)
    const exists = fs.existsSync(iconPath)
    console.log(`${exists ? '✅' : '❌'} ${name} (${size}x${size})`)
  })
  
  console.log('\n📋 Shortcut Icons:')
  SHORTCUT_ICONS.forEach(({ name, description }) => {
    const iconPath = path.join(iconsDir, name)
    const exists = fs.existsSync(iconPath)
    console.log(`${exists ? '✅' : '❌'} ${name} - ${description}`)
  })
  
  // Generate placeholder icons if they don't exist
  generatePlaceholderIcons(iconsDir)
}

function generatePlaceholderIcons(iconsDir) {
  console.log('\n🎨 Generating placeholder icons...')
  
  // Create a simple SVG template
  const createSVGIcon = (size, text, color = '#f2b516') => {
    return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
    <rect width="${size}" height="${size}" fill="${color}"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size/8}" fill="white" text-anchor="middle" dominant-baseline="middle">${text}</text>
  </svg>`
  }
  
  // Generate main app icons
  ICON_SIZES.forEach(({ size, name }) => {
    const iconPath = path.join(iconsDir, name)
    if (!fs.existsSync(iconPath)) {
      const svg = createSVGIcon(size, 'G', '#f2b516')
      fs.writeFileSync(iconPath.replace('.png', '.svg'), svg)
      console.log(`📝 Created placeholder: ${name.replace('.png', '.svg')}`)
    }
  })
  
  // Generate shortcut icons
  const shortcutConfigs = [
    { name: 'icon-collections.png', text: 'C', color: '#4f46e5' },
    { name: 'icon-news.png', text: 'N', color: '#059669' },
    { name: 'icon-profile.png', text: 'P', color: '#dc2626' },
  ]
  
  shortcutConfigs.forEach(({ name, text, color }) => {
    const iconPath = path.join(iconsDir, name)
    if (!fs.existsSync(iconPath)) {
      const svg = createSVGIcon(96, text, color)
      fs.writeFileSync(iconPath.replace('.png', '.svg'), svg)
      console.log(`📝 Created placeholder: ${name.replace('.png', '.svg')}`)
    }
  })
}

function generateIconInstructions() {
  console.log('\n📖 ICON GENERATION INSTRUCTIONS:')
  console.log('================================')
  console.log('')
  console.log('1. 🎨 DESIGN REQUIREMENTS:')
  console.log('   - Use your logo/brand icon as base')
  console.log('   - Ensure good contrast on various backgrounds')
  console.log('   - Keep design simple and recognizable at small sizes')
  console.log('   - Use PNG format for best compatibility')
  console.log('')
  console.log('2. 🛠️  RECOMMENDED TOOLS:')
  console.log('   - Online: https://realfavicongenerator.net/')
  console.log('   - Online: https://www.favicon-generator.org/')
  console.log('   - Desktop: Adobe Illustrator, Figma, Canva')
  console.log('   - CLI: imagemagick, sharp-cli')
  console.log('')
  console.log('3. 📏 ICON SIZES NEEDED:')
  ICON_SIZES.forEach(({ size, name }) => {
    console.log(`   - ${name}: ${size}x${size}px`)
  })
  console.log('')
  console.log('4. 🎯 SHORTCUT ICONS:')
  SHORTCUT_ICONS.forEach(({ name, description }) => {
    console.log(`   - ${name}: 96x96px - ${description}`)
  })
  console.log('')
  console.log('5. 🚀 QUICK GENERATION WITH IMAGEMAGICK:')
  console.log('   # Install imagemagick first')
  console.log('   # Convert your logo.png to all sizes:')
  console.log('')
  ICON_SIZES.forEach(({ size, name }) => {
    console.log(`   magick logo.png -resize ${size}x${size} public/icons/${name}`)
  })
  console.log('')
  console.log('6. ✅ VALIDATION:')
  console.log('   - Test manifest.json: https://manifest-validator.appspot.com/')
  console.log('   - Test PWA: Chrome DevTools > Application > Manifest')
  console.log('   - Test install prompt on mobile devices')
}

// Run the script
console.log('🎯 PWA Icon Generator for GLITTERS')
console.log('==================================')

generateIconsDirectory()
generateIconInstructions()

console.log('\n✨ Icon generation complete!')
console.log('📝 Next steps:')
console.log('1. Replace placeholder SVGs with actual PNG icons')
console.log('2. Test manifest.json in browser DevTools')
console.log('3. Test PWA install functionality')
console.log('4. Submit to app stores if needed')
