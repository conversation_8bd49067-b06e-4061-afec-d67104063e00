import { Input } from 'antd'
import React, { useEffect, useState } from 'react'

import { ETYPE_INPUT } from '@/constants'
import { ONLY_TYPE_INPUT } from '@/constants/type'
import {
  regexAlphabet,
  regexAlphabetNumber,
  regexBanJapanese,
  regexFullWidth,
  schemaNumber,
} from '@/utils'

interface IInputComponentProps {
  defaultValue?: string
  placeholder?: string
  onChange?: (value: string) => void
  showCount?: boolean
  maxLength?: number
  onlyType?: ONLY_TYPE_INPUT
  prefix?: string
  id?: string
}

const objSchemaOnlyNumber = {
  [ETYPE_INPUT.alphabet]: regexAlphabet,
  [ETYPE_INPUT.number]: schemaNumber,
  [ETYPE_INPUT.number_alphabet]: regexAlphabetNumber,
  [ETYPE_INPUT.ban_japanese]: regexBanJapanese,
}

const InputComponent = (props: IInputComponentProps) => {
  const [value, setValue] = useState<string>('')

  useEffect(() => {
    if (
      !objSchemaOnlyNumber[props.onlyType || 'ban_japanese'].test(
        props.defaultValue as string
      ) &&
      props.defaultValue !== ''
    ) {
      return
    }
    setValue(props.defaultValue as string)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.defaultValue])

  const triggerChange = (changedValue: string) => {
    props.onChange?.(changedValue)
  }

  const onValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (
      !objSchemaOnlyNumber[props.onlyType || 'ban_japanese'].test(
        e.target.value
      ) &&
      e.target.value !== ''
    ) {
      return
    }
    !(props.maxLength && e.target.value.length > props.maxLength) &&
      (setValue(e.target.value), triggerChange(e.target.value))
  }

  const onBlurValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (
      !objSchemaOnlyNumber[props.onlyType || 'number'].test(e.target.value) &&
      e.target.value !== ''
    ) {
      return
    }
    // Using the value 65248 is a standard way to convert between full-width and half-width characters in Japanese
    setValue(
      e.target.value.replace(regexFullWidth, (char) =>
        String.fromCharCode(char.charCodeAt(0) - 65248)
      )
    )
    triggerChange(
      e.target.value.replace(regexFullWidth, (char) =>
        String.fromCharCode(char.charCodeAt(0) - 65248)
      )
    )
  }

  return (
    <div id={props.id}>
      <Input
        type="text"
        value={value}
        onChange={onValueChange}
        placeholder={props.placeholder}
        showCount={props.showCount}
        maxLength={props.maxLength}
        prefix={props.prefix}
        onBlur={onBlurValue}
      />
    </div>
  )
}

export default InputComponent
