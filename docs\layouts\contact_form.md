# Contact Form UI Documentation

This document describes the structure, fields, and styling of the Contact Form UI, excluding the footer section.

## Overview

The contact form is divided into two main panels:

1. **Contact Information Panel** – Left side, showing instructions and a list of required details.
2. **Contact Form Panel** – Right side, containing input fields for user submissions.

---

## Components

### 1. Layout Container

- **Description:** Main wrapper holding both panels.
- **Background Color:** `#000000` (black)
- **Padding:** `2rem` on both sides
- **Font:** Sans-serif
- **Text Color:** `#FFFFFF` (white)
- **Layout:** Two-column flex layout

---

### 2. Contact Information Panel (Left Side)

- **Background Color:** `#1A1A1A` (dark gray)
- **Padding:** `1.5rem`
- **Border Radius:** `8px`
- **Content:**
  - **Title:** "Contact"
  - **Instruction text:** Explains purpose and request for required details
  - **Required Information List:**
    - Full Name
    - Company Name (if applicable)
    - Email Address
    - Phone Number
    - Subject
    - Inquiry Details
  - **Note:** Replies are handled in order received and may take time

---

### 3. Contact Form Panel (Right Side)

- **Background Color:** `#1A1A1A` (dark gray)
- **Padding:** `1.5rem`
- **Border Radius:** `8px`
- **Field Spacing:** `1rem` between each form field

---

## Form Fields

| Field Name      | Type        | Required | Description                         |
| --------------- | ----------- | -------- | ----------------------------------- |
| Full Name       | Text Input  | Yes      | User's full name                    |
| Company Name    | Text Input  | No       | Company name (if applicable)        |
| Email Address   | Email Input | Yes      | User's email address                |
| Phone Number    | Text Input  | Yes      | User's phone number                 |
| Subject         | Text Input  | Yes      | Short title describing the inquiry  |
| Inquiry Details | Textarea    | Yes      | Detailed description of the inquiry |

---

### 4. FormField Styling

- **Label**
  - Font Size: `14px`
  - Font Weight: bold
  - Color: `#FFFFFF`
  - Required Asterisk: red `#FF4D4F` (for mandatory fields)
- **Input / Textarea**
  - Background: transparent or `#0D0D0D`
  - Border: `1px solid #333333`
  - Border Radius: `4px`
  - Padding: `0.5rem 0.75rem`
  - Text Color: `#FFFFFF`
  - Placeholder Color: `#888888`
  - Focus State: border color changes to yellow `#FFD15C`

---

### 5. SubmitButton Component

- **Background Color:** `#FFD15C` (bright yellow)
- **Text Color:** `#000000` (black)
- **Font Weight:** bold
- **Border Radius:** `4px`
- **Padding:** `0.75rem 1rem`
- **Hover State:** `#FFC233` (darker yellow)
- **Active State:** `#E6A800` (even darker yellow)

---

## Notes

- All text uses a sans-serif font for clean and modern readability.
- UI design emphasizes high contrast for better accessibility.
- On smaller screens, the layout should stack vertically for better usability.
