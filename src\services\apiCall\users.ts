import { API_URLS, ROLE } from '@/constants'
import { get, patch } from './baseApi'
import { DEFAULT_CONFIG_PARAMS } from '@/constants'
import { UserBalance, UserDetail, Users } from '@/interfaces'

// Get all users (admin only)
export const getUsers = async (params: {
  page?: number
  pageSize?: number
  search?: string
  sortCondition?: string
}): Promise<Users> => {
  return await get(API_URLS.users, params, ROLE.systemAdmin)
}

// Get user detail by wallet address
export const getUserDetail = async (role?: ROLE): Promise<UserDetail> => {
  return await get(
    `${API_URLS.userProfile}`,
    DEFAULT_CONFIG_PARAMS,
    role || ROLE.user
  )
}

export const adminGetUserDetail = async (
  walletAddress: string,
  role: ROLE
): Promise<UserDetail> => {
  return await get(
    `${API_URLS.adminUsers}/${walletAddress}`,
    DEFAULT_CONFIG_PARAMS,
    role
  )
}

// Update user profile
export const updateUserProfile = async (params: {
  name?: string
  biography?: string
  xUrl?: string
  instagramUrl?: string
  facebookUrl?: string
  aboutMe?: string
  file?: File
  removeAvatar?: boolean
}): Promise<{ message: string }> => {
  const formData = new FormData()

  for (const key in params) {
    if (params[key] !== undefined) {
      formData.append(key, params[key])
    }
  }

  return await patch(`${API_URLS.userProfile}`, formData, ROLE.user)
}

// Get user balance
export const getUserBalance = (): Promise<UserBalance> => {
  return get(`${API_URLS.users}/balance`, DEFAULT_CONFIG_PARAMS, ROLE.user)
}

// Set NFT as profile image
export const setProfileImage = async (
  nftId: string
): Promise<{ message: string }> => {
  return await patch(`${API_URLS.users}/profile-image`, { nftId }, ROLE.user)
}

export const getProfileStatistic = async (): Promise<{
  totalMintCount: number
  totalPrice: number
}> => {
  return await get(`${API_URLS.users}/statistic/profile`, {}, ROLE.user)
}
