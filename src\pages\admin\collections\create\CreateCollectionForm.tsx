import React, { useState } from 'react'
import { Form, Radio, Input } from 'antd'
import { Row, Col } from 'antd'
import UiButton from '@/components/ui/Button'
import { BUTTON_SIZES } from '@/constants/enum'
import { Select } from 'antd'
import Text from '@/components/texts/Text'
const { TextArea } = Input
import UploadImage from '@/components/uploadImages/UploadImages'
import { API_URLS, ROLE } from '@/constants'
import { post } from '@/services/apiCall/baseApi'
import { toast } from 'react-toastify'
import { usePost } from '@/hooks/usePost'
import { createCollection } from '@/services/apiCall/collections'
import { CollectionPayload } from '@/interfaces/collection'
import { useGet } from '@/hooks/useGet'
import { getListCategories } from '@/services/apiCall/categories'
import { Category } from '@/interfaces/categories'
import router from 'next/router'
import { CONTRACT_TYPES } from '@/constants/common'
import { useStore } from '@/store'

const TEXT_LABELS = {
  CREATE_NFT_COLLECTION: 'NFTコレクションを作成',
  STANDARD: 'コレクションの標準をお選びください',
  NAME: 'コレクション名',
  CATEGORY: 'カテゴリー',
  DESCRIPTION: 'コレクション紹介',
  UPLOAD_LOGO: 'ロゴ画像をアップロード',
  UPLOAD_BACKGROUND: '背景画像をアップロード',
  SAVE_DRAFT: 'ドラフトを保存',
  CREATE: '作成',
  NAME_YOUR_NFT: 'NFTに名前を付けます',
  DESCRIPTION_PLACEHOLDER: '説明...',
  PLEASE_ENTER_NAME: 'コレクション名を入力してください',
  PLEASE_ENTER_DESCRIPTION: 'コレクションの説明を入力してください',
  PLEASE_SELECT_CATEGORY: 'カテゴリーを選択してください',
  PLEASE_SELECT_STANDARD: 'コレクションの標準を選択してください',
  PLEASE_UPLOAD_LOGO: 'ロゴ画像をアップロードしてください',
  PLEASE_UPLOAD_BACKGROUND: '背景画像をアップロードしてください',
  FAILED_TO_UPLOAD_LOGO: 'ロゴ画像のアップロードに失敗しました。',
  FAILED_TO_UPLOAD_BACKGROUND: '背景画像のアップロードに失敗しました。',
  CANNOT_EXCEED_1000: '説明は1000文字以内でなければなりません',
  CANNOT_EXCEED_100: '説明は100文字以内でなければなりません',
}

export default function CreateCollectionForm() {
  const role = useStore((state) => state.role)
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [bgFile, setBgFile] = useState<File | null>(null)

  const { mutate } = usePost({
    queryKey: ['createCollection'],
    callback: (payload: CollectionPayload) => {
      return createCollection(payload, role as ROLE.systemAdmin | ROLE.operator)
    },
  })

  // Fetch categories using useGet
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    isError: categoriesError,
  } = useGet({
    queryKey: ['categories'],
    callback: () => getListCategories(),
  })

  const uploadImage = async (file: File, label: string) => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      const res = await post(API_URLS.adminUploadCollectionImage, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      return res
    } catch (err) {
      toast.error(`${label} upload failed!`)
      return ''
    }
  }

  const handleLogoImage = async (
    logoFile: File | null
  ): Promise<string | null> => {
    if (logoFile) {
      const uploadedId = await uploadImage(logoFile, 'Logo image')
      if (!uploadedId) {
        toast.error(TEXT_LABELS.FAILED_TO_UPLOAD_LOGO)
        setLogoFile(null)
        return null
      }
      return uploadedId
    }
    toast.error(TEXT_LABELS.PLEASE_UPLOAD_LOGO)
    return null
  }

  const handleBackgroundImage = async (
    bgFile: File | null
  ): Promise<string | null> => {
    if (bgFile) {
      const uploadedId = await uploadImage(bgFile, 'Background image')
      if (!uploadedId) {
        toast.error(TEXT_LABELS.FAILED_TO_UPLOAD_BACKGROUND)
        setBgFile(null)
        return null
      }
      return uploadedId
    }
    toast.error(TEXT_LABELS.PLEASE_UPLOAD_BACKGROUND)
    return null
  }

  const handleSubmit = async (values: any, isDraft = false) => {
    setLoading(true)

    const logoImageId = await handleLogoImage(logoFile)
    if (!logoImageId) {
      setLoading(false)
      return
    }

    const backgroundImageId = await handleBackgroundImage(bgFile)
    if (!backgroundImageId) {
      setLoading(false)
      return
    }

    // Prepare payload
    const payload: CollectionPayload = {
      ...values,
      logoImageId,
      backgroundImageId,
      isDraft,
    }

    // Submit collection
    mutate(payload, {
      onSuccess: () => {
        form.resetFields()
        setLogoFile(null)
        setBgFile(null)
        setLoading(false)
        router.back()
      },
      onError: () => {
        setLogoFile(null)
        setBgFile(null)
        setLoading(false)
      },
    })
  }

  const onFinish = async (values: any) => {
    // Default behavior - create collection (not draft)
    await handleSubmit(values, false)
  }

  const onSaveDraft = async () => {
    // Save as draft
    const values = await form.validateFields()
    await handleSubmit(values, true)
  }

  return (
    <Row>
      <Col xs={24} md={10}>
        <UploadImage
          width="w-[60%]"
          height="h-1/2"
          label={TEXT_LABELS.UPLOAD_LOGO}
          required
          recommendedSize="350 x 350"
          value={logoFile}
          maxSize={2}
          onChange={(file) => {
            setLogoFile(file)
          }}
        />
        <UploadImage
          height="h-1/2"
          label={TEXT_LABELS.UPLOAD_BACKGROUND}
          recommendedSize="1920 x 1080"
          value={bgFile}
          required
          maxSize={5}
          onChange={setBgFile}
        />
      </Col>
      <Col xs={24} md={12}>
        <Form
          requiredMark={false}
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="flex flex-row gap-8 w-full text-white"
        >
          <Row>
            {/* Optionally show loading or error for categories */}
            {categoriesLoading && <span>Loading categories...</span>}
            {categoriesError && <span>Failed to load categories</span>}
          </Row>
          <div className="flex flex-col flex-1 gap-6">
            <div>
              <Form.Item
                name="standard"
                initialValue={CONTRACT_TYPES[0]}
                rules={[
                  { required: true, message: 'Please select contract type' },
                ]}
                className="mb-2"
                label={
                  <Text require={true} className="text-xs font-montserrat">
                    {TEXT_LABELS.STANDARD}
                  </Text>
                }
              >
                <Radio.Group className="flex gap-8">
                  {CONTRACT_TYPES.map((type) => (
                    <Radio key={type} value={type}>
                      {type}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </div>
            <Form.Item
              name="name"
              label={
                <Text require={true} className="text-xs font-montserrat">
                  {TEXT_LABELS.NAME}
                </Text>
              }
              rules={[
                { required: true, message: TEXT_LABELS.PLEASE_ENTER_NAME },
                { max: 100, message: TEXT_LABELS.CANNOT_EXCEED_100 },
                {
                  whitespace: true,
                  message: TEXT_LABELS.PLEASE_ENTER_NAME,
                },
              ]}
              className="mb-2"
            >
              <Input
                size="large"
                placeholder={TEXT_LABELS.NAME_YOUR_NFT}
                maxLength={100}
                showCount
                required
                allowClear
              />
            </Form.Item>
            <Form.Item
              name="category"
              label={
                <Text require={true} className="text-xs font-montserrat">
                  {TEXT_LABELS.CATEGORY}
                </Text>
              }
              rules={[
                { required: true, message: TEXT_LABELS.PLEASE_SELECT_CATEGORY },
              ]}
              className="mb-2"
            >
              <Select
                options={
                  categoriesData?.items?.map((cat: Category) => ({
                    value: cat._id,
                    label: cat.name,
                  })) || []
                }
                placeholder={TEXT_LABELS.CATEGORY}
                size="large"
                loading={categoriesLoading}
                disabled={categoriesLoading || categoriesError}
                allowClear
              />
            </Form.Item>
            <Form.Item
              name="description"
              label={
                <Text require={true} className="text-xs font-montserrat">
                  {TEXT_LABELS.DESCRIPTION}
                </Text>
              }
              rules={[
                {
                  required: true,
                  message: TEXT_LABELS.PLEASE_ENTER_DESCRIPTION,
                },
                {
                  max: 1000,
                  message: TEXT_LABELS.CANNOT_EXCEED_1000,
                },
                {
                  whitespace: true,
                  message: TEXT_LABELS.PLEASE_ENTER_DESCRIPTION,
                },
              ]}
              className="mb-1"
            >
              <TextArea
                rows={4}
                placeholder={TEXT_LABELS.DESCRIPTION_PLACEHOLDER}
                className="w-full border border-[#333] p-4"
                maxLength={1000}
                showCount
                required
                allowClear
              />
            </Form.Item>
            <div className="flex flex-col gap-3 mt-6">
              <UiButton
                title={TEXT_LABELS.SAVE_DRAFT}
                size={BUTTON_SIZES.LG}
                isGradient={false}
                handleClick={onSaveDraft}
                className="w-full border border-[#333] !bg-transparent text-white"
                isDisabled={loading}
              />
              <UiButton
                title={TEXT_LABELS.CREATE}
                size={BUTTON_SIZES.LG}
                isGradient={true}
                handleClick={form.submit}
                className="w-full"
                isLoading={loading}
                isDisabled={loading}
              />
            </div>
          </div>
        </Form>
      </Col>
    </Row>
  )
}
